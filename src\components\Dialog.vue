<template>
  <div class="spurs-dialog">
    <el-dialog
      v-bind="excessProps"
      ref="dialogRef"
      v-model="isVisible"
      :data-dialog-id="dialogId"
      :destroy-on-close="true"
      :modal-class="`[&_.el-dialog]:(shadow-[0_0_7.5rem_0] shadow-[#00000014] rounded-2.5 p-8.5 pt-5 lt-md:p-4 my-0 bg-model-select-bg text-primary-300) !z-20030 ${modalClass} ${modalLeftClass}`"
      @close="closeDialog"
    >
      <template #header="{ titleId }">
        <div
          :id="titleId"
          :class="`${dialogOptions.titleClass} pb-3 border-b-[1px] border-b-[var(--share-dialog-divider)]`"
        >
          <div v-if="typeof titleContent === 'string'" class="flex items-center justify-between">
            <div>{{ titleContent }}</div>
            <i
              class="i-ri-close-line cursor-pointer text-#A8AFBF"
              @click="closeDialog"
            ></i>
          </div>
          <component :is="titleContent" v-bind="titleProps" v-else />
        </div>
      </template>
      <div v-if="typeof content === 'string'">{{ content }}</div>
      <component :is="content" v-bind="contentProps" v-else />

      <template v-if="dialogOptions.showFooter" #footer>
        <span
          class="dialog-footer [&_.el-button:first-child]:(border-[#CBD5E1] text-[#A8AFBF] hover:bg-transparent) [&_.el-button:last-child]:(border-[#F05858] bg-[#F05858] text-white) [&_.el-button]:(rounded-1.43 font-normal)"
        >
          <el-button v-if="dialogOptions.showCancel" @click="closeDialog">{{
            $t("sideBar.cancel")
          }}</el-button>
          <el-button
            v-if="dialogOptions.showSubmit"
            type="primary"
            @click="dialogOptions.onSubmit"
          >
            {{ $t("dialog.confirm") }}
          </el-button>
        </span>
      </template>
      <slot name="footer"></slot>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { generateLetterId } from '@/common/tools'
import { useDialog } from '@/hooks/useDialog'
import { useModalPosition } from '@/hooks/useModalPosition'

const dialogId = generateLetterId()
const dialogTarget = shallowRef<HTMLElement | null>(null)

const { modalLeftClass } = useModalPosition()
const dialogRef = ref()
const { isVisible, dialogOptions, closeDialog } = useDialog(dialogRef)
const {
  titleContent,
  content,
  titleProps,
  contentProps,
  modalClass = '',
  style,
  ...excessProps
} = dialogOptions

onMounted(() => {
  nextTick(() => {
    dialogTarget.value = document.querySelector(`[data-dialog-id="${dialogId}"]`)
  })
})

// 使用 onClickOutside 处理点击外部关闭
onClickOutside(
  dialogTarget,
  () => {
    const target = dialogTarget.value
    if (target) {
      closeDialog()
    }
  },
)
</script>
