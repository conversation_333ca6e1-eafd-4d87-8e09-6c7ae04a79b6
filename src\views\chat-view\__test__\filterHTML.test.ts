import fs from 'node:fs/promises'
import path from 'node:path'
import { describe, expect, it } from 'vitest'
import { filterHTML } from '../filterHTML'

/**
 * @vitest-environment jsdom
 */
describe('filter html', () => {
  it('should filter html', async () => {
    const html = await fs.readFile(path.join(__dirname, 'weixin.html'), 'utf-8')
    expect(await filterHTML(html)).toMatchSnapshot()
  })
})
