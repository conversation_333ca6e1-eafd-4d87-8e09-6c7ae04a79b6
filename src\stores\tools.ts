import service from '@/common/axios'
import { LLMS_MODES, MODE_TYPE } from '@/enum'
import { mixpanel } from '../config/mixpanel'
import { useAppStore } from './app'

interface Tool {
  id: string
  name: string
  isActive: boolean
}

export const useToolsStore = defineStore('tools', () => {
  const tools = ref<Tool[]>([])
  const localDeepThinking = localStorage.getItem('deepThinking') === 'true'
  const deepThinking = ref(localDeepThinking || false)
  const { selectedMode } = storeToRefs(useAppStore())

  const toolKey = computed(() => {
    return `tool-${selectedMode.value?.id}`
  })
  // 不支持Tools字段的模型
  const notSupportToolsLLMList = [
    MODE_TYPE.LEPTON_AUDIO,
    MODE_TYPE.O1PREVIEW,
    MODE_TYPE.O1MINI,
    MODE_TYPE.GROK3,
    MODE_TYPE.GPT_O3,
    MODE_TYPE.GPT_04_MINI,
    MODE_TYPE.QWEN_3,
  ]

  // 是否显示工具栏
  const showTool = computed(() => {
    if (!selectedMode.value) {
      return false
    }

    // 不在这个列表里的就是支持的
    const currentLLMSupportTools = notSupportToolsLLMList.includes(selectedMode.value?.type)

    // 取反就是不支持的模型，就不该显示工具栏
    return !currentLLMSupportTools
  })

  // 检查指定模型ID是否支持工具
  const isModelSupportTools = (modelId: number) => {
    if (!modelId) { return false }

    const { models } = storeToRefs(useAppStore()).modelSelectInfo.value

    // 查找模型ID对应的模型类型
    let modelType: number | undefined
    for (const modelGroup of models) {
      // @ts-ignore
      for (const mode of modelGroup.modes) {
        if (mode.id === modelId) {
          modelType = mode.type
          break
        }
      }
      if (modelType !== undefined) { break }
    }

    if (modelType === undefined) { return false }

    return !notSupportToolsLLMList.includes(modelType)
  }

  const toolStatus = (name: string) => {
    const tool = tools.value.find(tool => tool.id.toLowerCase() === name.toLowerCase())
    if (tool) {
      return {
        [name]: tool.isActive,
      }
    }
    else {
      return {
        [name]: false,
      }
    }
  }

  async function fetchTools() {
    if (!showTool.value) { return }
    try {
      const res = await service.get<never, Tool[]>(`${import.meta.env.VITE_APP_V1_API}/v1/app/tools`)
      const fetchTools = res.map((item: Tool) => ({
        ...item,
        isActive: true,
      }))
      if (localStorage[toolKey.value]) {
        const originalLocalTools = JSON.parse(localStorage[toolKey.value])
        // 获取fetchTools中id在originalLocalTools中的数据, 如果originalLocalTools中没有, 则返回fetchTool
        const mergedTools = fetchTools.map((fetchTool: Tool) => {
          const originalTool = originalLocalTools.find((localTool: Tool) => localTool.id === fetchTool.id)
          return originalTool || fetchTool
        })
        tools.value = mergedTools
        return
      }
      tools.value = fetchTools
    }
    catch (error) {
      console.error(error)
      tools.value = []
      throw error
    }
  }
  watch(() => tools.value, () => {
    localStorage.setItem(toolKey.value, JSON.stringify(tools.value))
  }, {
    deep: true,
  })

  watch(() => deepThinking.value, (value) => {
    localStorage.setItem('deepThinking', value.toString())
    mixpanel.deepThinking(value)
  })

  watch(() => selectedMode.value, () => {
    if (showTool.value && localStorage[toolKey.value]) {
      tools.value = JSON.parse(localStorage[toolKey.value])
    }
  })

  return {
    tools,
    fetchTools,
    showTool,
    deepThinking,
    toolStatus,
    isModelSupportTools,
  }
})

export function useTools() {
  const store = useToolsStore()
  const { tools, showTool, deepThinking } = storeToRefs(store)
  const { fetchTools, isModelSupportTools } = store
  return {
    tools,
    fetchTools,
    showTool,
    deepThinking,
    toolStatus: store.toolStatus,
    isModelSupportTools,
  }
}
