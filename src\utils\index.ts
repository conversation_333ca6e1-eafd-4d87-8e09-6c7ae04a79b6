export const removeCitation = (markdown: string): string => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(markdown, 'text/html')
  doc.querySelectorAll('.citation').forEach(el => el.remove())
  return doc.body.innerHTML
}

export const base64ToFile = (base64: string): File => {
  const match = base64.match(/^data:([A-Za-z-+/]+);base64,/)
  const mimeType = match ? match[1] : 'application/octet-stream'
  const base64Data = base64.replace(/^data:[A-Za-z-+/]+;base64,/, '')
  const byteString = atob(base64Data)
  const byteArray = new Uint8Array(byteString.length)
  for (let i = 0; i < byteString.length; i++) {
    byteArray[i] = byteString.charCodeAt(i)
  }
  const blob = new Blob([byteArray], { type: mimeType })
  const subfix = mimeType.split('/')[1] || 'png'
  return new File([blob], `clipboard-image-${Date.now()}.${subfix}`, { type: mimeType })
}
