import type { NotificationProps } from 'element-plus'
import moment from 'moment'
import { custom<PERSON><PERSON><PERSON>bet } from 'nanoid'
import { FILE_CATEGORY, FILE_TYPE } from '@/model/common'

export function downloadStaticFile(url: string, fileName: string) {
  const link = document.createElement('a')
  link.style.display = 'none'
  document.body.appendChild(link)
  link.href = url
  link.target = '_blank'
  link.download = fileName
  link.click()
  document.body.removeChild(link)
}

export function getFullImageUrl(url: string) {
  if (!url) {
    return ''
  }
  if (url.includes('http')) {
    return `${url}`
  }
  else {
    return `${import.meta.env.VITE_APP_IMAGE_URL}${url}`
  }
}

export function htmlEncode(html: string) {
  let temp: any = document.createElement('div');

  // eslint-disable-next-line unicorn/prefer-dom-node-text-content
  (temp.textContent !== undefined) ? (temp.textContent = html) : (temp.innerText = html)
  const output = temp.innerHTML
  temp = null
  return output
}

export function getFileCategory(fileString: string) {
  const ext = getFileExt(fileString)
  switch (ext) {
    case 'jpg':
    case 'jpeg':
    case 'webp':
    case 'png':
    case 'gif':
      return FILE_CATEGORY.IMAGE
    case 'doc':
    case 'docx':
    case 'pdf':
    case 'ppt':
    case 'pptx':
    case 'txt':
    case 'rtf':
    case 'html':
    case 'htm':
    case 'md':
      return FILE_CATEGORY.TEXT
    case 'xls':
    case 'xlsx':
    case 'xlsm':
    case 'xlsb':
    case 'xlt':
    case 'xltx':
    case 'xltm':
    case 'csv':
    case 'log':
    case 'xml':
    case 'json':
      return FILE_CATEGORY.DATA
    case 'mp3':
      return FILE_CATEGORY.AUDIO
    default:
      return FILE_CATEGORY.TEXT
  }
}

export function getFileExt(filename: string) {
  return filename?.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLocaleLowerCase()
}

export function getFileShowType(fileString: string) {
  const ext = getFileExt(fileString)
  switch (ext) {
    case 'jpg':
    case 'jpeg':
    case 'webp':
    case 'png':
    case 'gif':
      return FILE_TYPE.IMAGE
    case 'doc':
    case 'docx':
      return FILE_TYPE.WORD
    case 'pdf':
      return FILE_TYPE.PDF
    case 'ppt':
    case 'pptx':
      return FILE_TYPE.PPT
    case 'xls':
    case 'xlsx':
    case 'xlsm':
    case 'xlsb':
    case 'xlt':
    case 'xltx':
    case 'xltm':
      return FILE_TYPE.EXCEL
    default:
      return FILE_TYPE.OTHER
  }
}

export const notify = {
  error: (args: Partial<NotificationProps>) => {
    ElNotification.error(
      {
        ...args,
        zIndex: 9999,
      },
    )
  },
  success: (args: Partial<NotificationProps>) => {
    ElNotification.success({
      ...args,
      zIndex: 9999,
    })
  },
  warning: (args: Partial<NotificationProps>) => {
    ElNotification.warning({
      ...args,
      zIndex: 9999,
    })
  },
  info: (args: Partial<NotificationProps>) => {
    ElNotification.info({
      ...args,
      zIndex: 9999,
    })
  },
  closeAll: () => {
    ElNotification.closeAll()
  },
}
export const mergeUint8Array = (uint8Array: Uint8Array[]) => {
  const totalLength = uint8Array.reduce((acc, arr) => acc + arr.length, 0)
  const mergedArray = new Uint8Array(totalLength)
  let offset = 0
  for (const arr of uint8Array) {
    mergedArray.set(arr, offset)
    offset += arr.length
  }
  return mergedArray
}
export const getBtyes = (base64: string) => {
  const byteCharacters = atob(base64)
  const len = byteCharacters.length
  const btyes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    btyes[i] = byteCharacters.charCodeAt(i)
  }
  return btyes
}
// 将base64转换为音频
export const convertBase64ToAudio = async (base64AudioArray: string[]): Promise<Blob> => {
  try {
    const byteArrays: Uint8Array[] = []

    for (const base64 of base64AudioArray) {
      // 使用 Uint8Array 和 atob 来解码 base64
      byteArrays.push(getBtyes(base64))
    }

    // 合并所有的 Uint8Array
    const mergedArray = mergeUint8Array(byteArrays)
    return new Blob([mergedArray], { type: 'audio/mp3' })
  }
  catch (error) {
    console.error('转换 base64 到音频时出错:', error)
    throw error
  }
}

// 将音频转换为base64
export const convertAudioToBase64 = (audioBlob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        const base64Audio = reader.result.split(',')[1]
        resolve(base64Audio)
      }
      else {
        reject(new Error('转换失败'))
      }
    }
    reader.onerror = reject
    reader.readAsDataURL(audioBlob)
  })
}

/**
 * 将数字转换为K、M、B格式的字符串
 * @param num - 要转换的数字
 * @param digits - 保留的小数位数(默认为1)
 * @returns 转换后的字符串
 * @example
 * formatNumberToK(1000) // "1K"
 * formatNumberToK(999) // "999"
 */
export function formatNumberToK(num: number, digits: number = 1): string {
  const units = [
    { value: 1e9, symbol: 'B' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'K' },
  ]

  for (const unit of units) {
    if (num >= unit.value) {
      return `${(num / unit.value).toFixed(digits).replace(/\.0$/, '')}${unit.symbol}`
    }
  }
  return num.toString()
}

// 将日期字符串转换为时间戳
export function toTimestamp(dateStr: string) {
  return moment(dateStr, 'YYYY-MM-DD HH:mm:ss').valueOf()
}

const alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
export function generateLetterId(length: number = 20) {
  return customAlphabet(alphabet, length)()
}

class ThinkingInteraction {
  static mapper = new Map<HTMLElement, { timer: NodeJS.Timeout, startTime: number }>()

  static async getThinkingTime(): Promise<HTMLElement> {
    return new Promise((resolve) => {
      const observer = new MutationObserver((_, obs) => {
        const thinkingTimeList = document.querySelectorAll('.thinking-time')
        const thinkingTime = thinkingTimeList[thinkingTimeList.length - 1] as HTMLElement
        if (thinkingTime) {
          obs.disconnect()
          resolve(thinkingTime)
        }
      })
      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class'],
      })
    })
  }

  static async action(start: boolean) {
    if (start) {
      console.debug('Start thinking')
    }
    else {
      console.debug('End thinking')
    }

    try {
      if (start) {
        const thinkingTime = await this.getThinkingTime()
        if (this.mapper.has(thinkingTime)) {
          clearInterval(this.mapper.get(thinkingTime)!.timer)
        }

        thinkingTime.textContent = '0s'
        thinkingTime.previousElementSibling?.classList.add('shark-animation')
        const timer = setInterval(() => {
          const time = moment().diff(moment(this.mapper.get(thinkingTime)!.startTime), 'seconds')
          thinkingTime.textContent = `${time}s`
        }, 1000)

        this.mapper.set(thinkingTime, { timer, startTime: Date.now() })
      }
      else {
        this.destoryInteraction()
      }
    }
    catch (error) {
      console.error('ThinkingInteraction action error:', error)
    }
  }

  static destoryInteraction() {
    this.mapper.forEach((mapperData, key) => {
      key.previousElementSibling?.classList.remove('shark-animation')
      mapperData.timer && clearInterval(mapperData.timer)
      this.mapper.delete(key)
    })
  }
}

export class ThinkingMarkdownProcessor extends ThinkingInteraction {
  constructor() {
    super()
  }

  private static markdown: string = 'aa'
  private static reciving = false
  private static endSymbols = ['\n\n</juchats-thinking>\n\n', '\n```']
  private static startSymbols = ['\n\n<juchats-thinking>\n\n', '```thinking']

  public static reset() {
    this.markdown = ''
    this.reciving = false
  }

  public static destory() {
    this.reset()
    this.destoryInteraction()
  }

  public static stop() {
    this.mapper.forEach((mapperData, key) => {
      key.previousElementSibling?.classList.remove('shark-animation')
      mapperData.timer && clearInterval(mapperData.timer)
    })
  }

  private static splitMarkdown(symbol: string) {
    const index = this.markdown.indexOf(symbol)
    if (index === -1) {
      return
    }
    this.markdown = this.markdown.slice(index + symbol.length)
  }

  public static process(markdown: string, callback?: (reciving: boolean) => void) {
    this.markdown += markdown
    this.markdown = this.markdown.replace(/<think>/g, '\n```thinking').replace(/<\/think>/g, '```')
    if (this.reciving) {
      const hasEndSymbol = this.endSymbols.some(symbol => this.markdown.includes(symbol))
      if (hasEndSymbol) {
        this.reciving = false
        if (callback) {
          callback(false)
        }
        ThinkingInteraction.action(false)
        const endSymbol = this.endSymbols.find(symbol => this.markdown.includes(symbol))!
        this.splitMarkdown(endSymbol)
      }
    }
    else {
      const hasStartSymbol = this.startSymbols.some(symbol => this.markdown.includes(symbol))
      if (hasStartSymbol) {
        this.reciving = true
        if (callback) {
          callback(true)
        }
        ThinkingInteraction.action(true)
        const startSymbol = this.startSymbols.find(symbol => this.markdown.includes(symbol))!
        this.splitMarkdown(startSymbol)
      }
    }
  }
}

export const requestPlugin = async <T = any>(data: any): Promise<T> => {
  return new Promise((resolve, rejected) => {
    if (window.chrome && chrome.runtime) {
      try {
        chrome.runtime.connect('jjldmcbcaldpffkimblpecplgmkjcemn')
        chrome.runtime.sendMessage('jjldmcbcaldpffkimblpecplgmkjcemn', data, (result) => {
          resolve(result as T)
        })
      }
      catch (error) {
        rejected(error)
      }
    }
    else {
      rejected('plugin not active')
    }
  })
}

/**
 * 检查文件类型是否在接受列表中（检查MIME类型或文件扩展名）
 * @param file - 要检查的文件对象
 * @param acceptList - 接受的文件类型列表
 * @returns 文件类型是否被接受
 */
export function isAcceptedFileType(file: any, acceptList: string[]) {
  // 检查MIME类型
  if (acceptList.includes(file.type)) {
    return true
  }

  // 检查文件扩展名
  const fileName = file.name.toLowerCase()
  const fileExt = `.${fileName.split('.').pop()}`
  return acceptList.includes(fileExt)
}

/**
 * 根据时间戳判断其所属的时间范围
 * @param {number} timestamp - 需要判断的时间戳 (毫秒)
 * @returns {string} - 返回 '今天', '过去31天', 或 '更早'
 */
export const TimeCategory = {
  TODAY: 'today',
  WITHIN_7_DAYS: 'this_week',
  WITHIN_31_DAYS: 'this_month',
  EARLIER: 'earlier',
} as const

export function formatTimestampCategory(timestamp: number): (typeof TimeCategory)[keyof typeof TimeCategory] {
  const today = moment().startOf('day')
  const targetDate = moment(timestamp).startOf('day')
  const sevenDaysAgo = moment().subtract(7, 'days').startOf('day')
  const thirtyOneDaysAgo = moment().subtract(31, 'days').startOf('day')

  if (targetDate.isSame(today, 'day')) {
    return TimeCategory.TODAY
  }
  if (targetDate.isAfter(sevenDaysAgo)) {
    return TimeCategory.WITHIN_7_DAYS
  }
  if (targetDate.isAfter(thirtyOneDaysAgo)) {
    return TimeCategory.WITHIN_31_DAYS
  }
  return TimeCategory.EARLIER
}
