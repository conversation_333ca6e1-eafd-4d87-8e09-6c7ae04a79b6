<?xml version="1.0" encoding="UTF-8"?>
<svg width="428px" height="400px" viewBox="0 0 428 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 2</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="radialGradient-1">
            <stop stop-color="#FCA5A5" offset="0%"></stop>
            <stop stop-color="#FCD34D" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="50%" cy="48.7180769%" fx="50%" fy="48.7180769%" r="51.2819231%" gradientTransform="translate(0.5, 0.4872), scale(0.7143, 1), rotate(90), translate(-0.5, -0.4872)" id="radialGradient-2">
            <stop stop-color="#FCF3A5" offset="0%"></stop>
            <stop stop-color="#FD8D74" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="0%" y1="49.9996981%" x2="99.9999878%" y2="49.9996981%" id="linearGradient-3">
            <stop stop-color="#000000" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="49.9996981%" x2="-0.00543663525%" y2="49.9996981%" id="linearGradient-4">
            <stop stop-color="#000000" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </linearGradient>
        <path d="M0,17.714 C0,7.931 7.93,0 17.71,0 L170.29,0 C180.07,0 188,7.931 188,17.714 L188,30 C188,81.915 145.91,124 94,124 C42.09,124 0,81.915 0,30 L0,17.714 Z" id="path-5"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="moimoi-sticker-pack" transform="translate(-1064, -627)">
            <g id="编组" transform="translate(1064, 627)">
                <g>
                    <rect id="矩形" fill="url(#radialGradient-1)" fill-rule="nonzero" x="28" y="0" width="400" height="400" rx="200"></rect>
                    <ellipse id="椭圆形" fill="url(#radialGradient-2)" fill-rule="nonzero" cx="182" cy="170" rx="182" ry="130"></ellipse>
                    <path d="M220,167.999 C220,163.139 222.21,158.542 226,155.506 L266,123.506 C272.91,117.985 282.97,119.104 288.49,126.004 C294.01,132.904 292.9,142.973 286,148.493 L261.61,167.999 L286,187.506 C292.9,193.026 294.01,203.094 288.49,209.995 C282.97,216.895 272.91,218.013 266,212.493 L226,180.493 C222.21,177.457 220,172.86 220,167.999 Z" id="路径" fill="url(#linearGradient-3)"></path>
                    <path d="M260.36,166.438 L258.41,167.999 L260.36,169.561 L284.75,189.067 C290.78,193.897 291.76,202.707 286.93,208.745 C282.1,214.783 273.29,215.762 267.25,210.932 L227.25,178.932 C223.93,176.275 222,172.252 222,167.999 C222,163.746 223.93,159.724 227.25,157.067 L267.25,125.067 C273.29,120.237 282.1,121.216 286.93,127.254 C291.76,133.291 290.78,142.101 284.75,146.932 L260.36,166.438 Z" id="路径" stroke="#000000" stroke-width="4"></path>
                    <path d="M168,167.999 C168,163.139 165.79,158.542 162,155.506 L122,123.506 C115.1,117.985 105.03,119.104 99.51,126.004 C93.99,132.904 95.11,142.973 102.01,148.493 L126.39,167.999 L102.01,187.506 C95.11,193.026 93.99,203.094 99.51,209.995 C105.03,216.895 115.1,218.013 122,212.493 L162,180.493 C165.79,177.457 168,172.86 168,167.999 Z" id="路径" fill="url(#linearGradient-4)"></path>
                    <path d="M127.64,166.438 L129.59,167.999 L127.64,169.561 L103.25,189.067 C97.22,193.897 96.24,202.707 101.07,208.745 C105.9,214.783 114.71,215.762 120.75,210.932 L160.75,178.932 C164.07,176.275 166,172.252 166,167.999 C166,163.746 164.07,159.724 160.75,157.067 L120.75,125.067 C114.71,120.237 105.9,121.216 101.07,127.254 C96.24,133.291 97.22,142.101 103.25,146.932 L127.64,166.438 Z" id="路径" stroke="#000000" stroke-width="4"></path>
                    <path d="M104,245.714 C104,235.931 111.93,228 121.71,228 L274.29,228 C284.07,228 292,235.931 292,245.714 L292,258 C292,309.915 249.91,352 198,352 C146.09,352 104,309.915 104,258 L104,245.714 Z" id="路径" fill="#000000" fill-rule="nonzero"></path>
                    <g id="Clipped" transform="translate(104, 228)">
                        <mask id="mask-6" fill="white">
                            <use xlink:href="#path-5"></use>
                        </mask>
                        <g id="路径"></g>
                        <ellipse id="椭圆形" fill="#F87171" fill-rule="nonzero" mask="url(#mask-6)" cx="100" cy="86" rx="120" ry="66"></ellipse>
                    </g>
                </g>
                <rect id="矩形" stroke="#000000" stroke-width="12.8" x="34.4" y="6.4" width="387.2" height="387.2" rx="193.6"></rect>
            </g>
        </g>
    </g>
</svg>