{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "never"
  },
  "editor.quickSuggestions": {
    "strings": "on"
  },
  "editor.defaultFormatter": "stylelint.vscode-stylelint",
  "scss.lint.unknownAtRules": "ignore",

  // 禁用默认格式，改用 eslint
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // 静默 IDE 中的 stylistic 规则，但仍可自动修复
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off" },
    { "rule": "format/*", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  // 为所有支持的语言启用 eslint
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ],
  "stylelint.validate": [
    "css",
    "scss",
    "sass",
    "vue"
  ],
  "unot.switchMagic": false,
  "i18n-ally.displayLanguage": "zh",
  "i18n-ally.enabledFrameworks": [
    "vue"
  ],
  "i18n-ally.enabledParsers": [
    "ts",
    "json"
  ],
  "i18n-ally.localesPaths": [
    "src/i18n/lang"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "i18n-ally.keystyle": "nested",
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  }
}
