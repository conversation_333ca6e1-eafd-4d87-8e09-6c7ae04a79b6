import type { ThemeMode } from '@/hooks/useThemeChange'
import { useColorMode } from '@vueuse/core'

export const useThemeStore = defineStore('theme', () => {
  const mode = useColorMode()
  const currentTheme = ref<ThemeMode>(mode.value)
  const showThemeTransition = ref(false)

  function setShowThemeTransition(show: boolean) {
    showThemeTransition.value = show
  }

  return {
    currentTheme,
    showThemeTransition,
    setShowThemeTransition,
  }
})
