import type { HookModalProps } from './types'
import { type Component, computed, defineComponent, type PropType, ref, Teleport, unref, watch } from 'vue'

export type MyModalInstance = InstanceType<typeof MyModal>

export const MyModal = defineComponent({
  props: {
    content: {
      type: [String, Function] as PropType<string | (() => Component)>,
    },
    headerContent: {
      type: [String, Function] as PropType<string | (() => Component)>,
    },
    getContainer: {
      type: Element as PropType<Element>,
      default: document.body,
    },
    closeModal: Function,
    openModal: Function,
    visible: Boolean,
    modalClass: {
      type: String as PropType<string>,
      default: '',
    },
    style: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
  },
  setup(props, { attrs, expose }) {
    const confirmLoading = ref<boolean>(false)
    const propsRef = ref({ ...attrs, ...props })
    const modalRef = ref(null)

    const getProps = computed(() => {
      return { ...attrs, ...props, ...unref(propsRef) }
    })

    const bindValues = computed(() => {
      const _props = unref(getProps)
      const omit = (props: any, omitProps: string[]) => {
        const obj = JSON.parse(JSON.stringify(props))
        for (const prop of omitProps) {
          delete obj[prop]
        }
        return obj
      }
      return {
        ...omit(_props, ['onCancel', 'onOk', 'closeModal', 'content', 'getContainer']),
        visible: _props.visible,
      }
    })

    const setVisible = (visible: boolean) => {
      propsRef.value.visible = visible
    }

    const setProps = (props: HookModalProps) => {
      propsRef.value = {
        ...unref(getProps),
        ...props,
      }
    }

    watch(
      () => propsRef.value.visible,
      (val) => {
        if (val) {
          props.openModal?.()
        }
        else {
          props.closeModal?.()
        }
      },
    )

    const handleConfirm = async (e: MouseEvent) => {
      confirmLoading.value = true
      try {
        // await unref(getProps)?.onOk?.(e)
        setVisible(false)
      }
      catch (error) {
        return Promise.reject(error)
      }
      finally {
        confirmLoading.value = false
      }
    }
    const handleCancel = async (e: MouseEvent) => {
      // await unref(getProps)?.onCancel.(e)
      setVisible(false)
    }

    expose({
      setProps,
      modalRef,
    })

    return () => {
      const _props = unref(propsRef)
      const { content, getContainer, modalClass, visible, headerContent, style } = _props
      const Content = content instanceof Function ? content() : content
      const HeaderContent = headerContent instanceof Function ? headerContent() : headerContent

      return (
        <Teleport to={getContainer}>
          {
            visible && (
              <div ref={modalRef} class={[modalClass, 'absolute transition-cusbezier-300 inset-0 z-9999 bg-[var(--user-dialog-bg)] shadow-xl shadow-[#00000014] backdrop-blur-4rem']} style={style}>
                <div class="h-full w-full">
                  <div class="scrollbar-none h-full w-full overflow-y-auto">
                    <div class="relative h-full w-full flex flex-col p-32px lt-md:p-0px focus:outline-none">
                      {
                        HeaderContent || (
                          <div class="flex-[0_0_4.81rem] self-end">
                            <button class="h-3.5 w-3.5 cursor-pointer" onClick={handleCancel}>
                              <i class="i-ju-close text-primary-300" />
                            </button>
                          </div>
                        )
                      }
                      <div class="flex-1">
                        {/* <slot /> */}
                        {Content}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          }
        </Teleport>

      )
    }
  },
})
