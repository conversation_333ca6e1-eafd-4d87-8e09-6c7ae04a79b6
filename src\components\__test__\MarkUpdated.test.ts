import { describe, it, expect, vi, beforeEach } from 'vitest'
import MarkUpdated from '../MarkUpdated.vue'
import { mount } from '@vue/test-utils';

/**
 *  @vitest-environment jsdom
 */
describe('MarkUpdated', () => {
  // 在每个测试前恢复所有模拟
  beforeEach(() => {
    vi.restoreAllMocks();
    localStorage.clear();
  });

  it('should render correctly with default props', () => {
    const wrapper = mount(MarkUpdated, {
      props: {
        mark: 'new-feature'
      }
    })
    expect(wrapper.html()).toMatchSnapshot()
  })

  it('should render with slot content', () => {
    const wrapper = mount(MarkUpdated, {
      props: {
        mark: 'new-feature'
      },
      slots: {
        default: 'Test Content'
      }
    })
    expect(wrapper.text()).toContain('Test Content')
  })

  it('should accept mark, closest and version props', () => {
    const wrapper = mount(MarkUpdated, {
      props: {
        mark: 'new-feature',
        closest: '.test-selector',
        version: '1.0.0'
      },
      slots: {
        default: 'Feature Name'
      }
    })
    
    // 检查 props 是否正确传递给组件
    expect(wrapper.props('mark')).toBe('new-feature')
    expect(wrapper.props('closest')).toBe('.test-selector')
    expect(wrapper.props('version')).toBe('1.0.0')
  })

  it('should update localStorage after click', async () => {
    // 使用 spy 监视 localStorage.setItem
    const setItemSpy = vi.spyOn(Storage.prototype, 'setItem');
    
    const wrapper = mount(MarkUpdated, {
      props: {
        mark: 'new-feature',
        version: '1.0.0'
      }
    });
    
    // 检查组件的初始状态
    expect(wrapper.find('.mark-updated').exists()).toBe(true);
    
    // 直接触发容器上的点击事件
    await wrapper.find('.mark-updated-container').trigger('click');
    
    // 验证 localStorage 被正确设置
    expect(setItemSpy).toHaveBeenCalledWith('new-feature-1.0.0', 'yes');
    
    // 验证 localStorage 中有正确的值
    expect(localStorage.getItem('new-feature-1.0.0')).toBe('yes');
  })
})
