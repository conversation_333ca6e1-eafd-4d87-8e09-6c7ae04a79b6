<template>
  <button
    type="button"
    border="none"
    :bg="isDisabled ? '#F65134' : '#F65134 hover:#ff6547 active:#e54a2f'"
    text="14px #fff"
    class="confirm-btn h-52px w-full flex-c rounded-10px shadow-[0_20px_50px_0_#f85a001c] outline-none transition-cusbezier-200"
    :disabled="isDisabled"
    @click="$emit('click')"
  >
    <i v-if="loading" class="i-line-md-loading-twotone-loop mr-8px" />
    <slot />
  </button>
</template>

<script setup lang="ts">
const props = defineProps<{
  disabled?: boolean
  loading?: boolean
}>()

defineEmits<{
  (e: 'click'): void
}>()

const isDisabled = computed(() => props.disabled || props.loading)
</script>

<style lang="scss" scoped>
.confirm-btn {
  &:not(:disabled) {
    @apply cursor-pointer;
  }

  &:disabled {
    @apply cursor-not-allowed opacity-50;
  }
}
</style>
