@function hex-to-rgb($hex, $use-comma: true) {
  $separator: if($use-comma, ",", " "); // 根据$use-comma选择分隔符
  $rgb: color.red($hex) + $separator + color.green($hex) + $separator + color.blue($hex);

  @return $rgb;
}

$common-color-1: #000;
$common-color-2: #fff;
$common-color-3: #FF4A00;

// light mode variant
$light-color-1: #F2F2F2;
$light-color-2: #BCBCBC;
$light-color-3: #A8AFBF;
$light-color-4: #E1E5EA;
$light-color-5: #384A67;
$light-color-6: #FBFBFB;
$light-color-7: #CBD5E1;
$light-color-8: #717171;
$light-color-9: #FEFEFE;
$light-color-10: #ECF0F5;
$light-color-11: #64748B;

// dark mode variant
$dark-color-1: #CDCDCD;
$dark-color-2: #F1F1F1;
$dark-color-3: #727272;
$dark-color-4: #1E1E1E;
$dark-color-5: #363636;
$dark-color-6: #171717;
$dark-color-7: #1B1B1B;
$dark-color-8: #272727;


:root {
  $white: #fff;
  $black: #000;

  --white: #{$white};
  --black: #{$black};
  --white-rgb: #{hex-to-rgb($white)};
  --black-rgb: #{hex-to-rgb($black)};
  --header-bar-height: 78px;

  // new global
  // primary
  --p: var(--white);
  --p1: #{$light-color-3};
  --p2: #{$light-color-1};
  --p3: #{$common-color-1};
  --p4: #{$common-color-1};
  --p5: #{$common-color-2};
  --p6: #{$light-color-6};
  --p7: #{$light-color-7}; // groupTitle used
  --p8: #{$light-color-3};
  --p9: #{$light-color-7};
  --pf: var(--black);
  
  // @TODO 待替换
  
  // secondary
  --s: #ECF0F5;
  --s1: #384A67;
  --s2: #{$light-color-11};
  --s3: #CCC;
  --s4: #{$light-color-5};
  --sf: var(--black);

  // accent
  --a: #FFF7E4;
  --a1: #A77B1D;
  --af: var(--black);

  // destructive
  --d: #FF6121;
  --d1: #{$common-color-3};
  --d2: #F00;
  --d3: #F65134;
  --df: var(--black);

  // neutral
  --n: #ab68ff;

  // functional
  --border: #{$light-color-3};
  --border1: #b6dad2fa;
  --dialog: #00000014;
  --shrink: #0000001a;
  --shrink-hover: #000;
  --dialog1: #ECF0F6;
  --model-select-bg: #{$common-color-2};
  --model-select-text: #{$light-color-3};
  --model-select-text-active: #{$common-color-1};
  --model-title-text: #{$common-color-1};
  --model-display-text: #{$common-color-1};
  --chat-text-color: #{$common-color-1};
  --chat-link-color: #{$common-color-1};
  --network-card-bg: #FAFAFA;
  --network-card-border: #{$dark-color-2};
  --network-tab-active: #{$common-color-1};
  --network-tab-divider: #e7e7e7;
  --send-box-bg: #FDFDFD;
  --send-box-border: #F0F0F0;
  --send-box-character-limit: #{$common-color-1};
  --send-box-placeholder: #929292;
  --send-box-resend-button: #{$common-color-2};
  --send-box-button-bg: #{$common-color-1};
  --send-button-disabled-bg: #E4E4E4; 
  --send-box-button-text: #{$common-color-2};
  --send-button-disabled-text: #{$common-color-2};
  --send-box-stop-button-bg: #{$common-color-1};
  --send-box-stop-button-text: #{$common-color-2};
  --attachment-upload-button-bg: #{$light-color-10};
  --attachment-upload-button-hover-bg: #155EEF;
  --attachment-upload-icon: #{$common-color-1};
  --attachment-card-bg: #{$common-color-2};
  --attachment-card-file-name: #18181a;
  --attachment-card-file-name-extension: #{$light-color-7};
  --attachment-card-info: #18181a;
  --attachment-button-icon: #{$light-color-3};
  --attachment-close-button-bg: #{$light-color-10};
  --attachment-close-button-icon: #{$common-color-1};
  --control-button-group-bg: #{$common-color-2};
  --control-button-group-text-hover: #929292;
  --control-button-group-text-selected: #{$common-color-1};
  --app-bg: #{$common-color-2};
  --user-info-bg: #F8F8F8;
  --user-dialog-bg: #{$common-color-2};
  --sidebar-bg: #{$light-color-9};
  --sidebar-option: #{$light-color-5};
  --sidebar-option-bg: #{$light-color-10};
  --sidebar-option-text: #{$light-color-5};
  --sidebar-new-border: #{$light-color-4};
  --sidebar-new-border-hover: #b6dad2fa;
  --sidebar-add-group-color: #{$light-color-8};
  --mobile-sidebar-shadow-bg: #f0f0f0c7;
  --share-dialog-bg: #{$common-color-2};
  --share-dialog-text: #{$light-color-3};
  --share-dialog-link-bg: #FAFAFA;
  --egg-redemption-input-bg: #{$common-color-2};
  --egg-redemption-ticket-bg: #{$common-color-2};
  --share-dialog-divider: #EFEFEF;
  --input-label-color: #{$light-color-3};
  --theme-current-menu-text: #{$common-color-1};
  --theme-not-current-menu-text: #{$light-color-3};

  // 绘图比例按钮
  --ratio-button-bg: #FDFDFD;
  --ratio-button-text: #{$common-color-1};
  --ratio-button-hover-bg: #{$light-color-10};
  --ratio-button-hover-text: #{$common-color-1};
  --ratio-button-active-bg: #{$common-color-3};
  --ratio-button-active-text: #{$common-color-2};

  // tools调用提示
  --shark-text-color: #{$light-color-3};
  --shark-shadow-color: rgba(#{hex-to-rgb($common-color-1)}, 0.5);

  // 拖拽附件占位符图标
  --drag-attachment-placeholder-icon: #155EEF;

  // 背景点
  --dot-bg: #F8F9FD;
  --dot-item: #e5e7eb;

  // login swiper
  --login-card-bg: #{$common-color-2};
  --login-code-input-border: #dcdfe6;

  // Artifacts Code
  --artifacts-card-bg: #{$common-color-2};
  --artifacts-card-text: #18181A;
  --artifacts-card-desc: #{$light-color-7};
  --artifacts-code-bg: #{$common-color-2};
  --artifacts-code-border: #F1F1F1;
  --artifacts-code-tabs: #ECF0F5;
  --artifacts-code-tabs-border: #D8E0EA;
  --artifacts-code-head-bg: transparent;
  --artifacts-code-path-bg: #F9F9F9;
  --artifacts-code-deploy-bg: #000;
  --artifacts-code-deploy-text: #fff;
  --artifacts-tweak-desc: #{$light-color-8};

  // Markdown样式相关变量
  --markdown-media-border-color: #ecf0f500;
  --markdown-media-hover-border: #ecf0f5;
  --markdown-download-btn-bg: #fff3;
  --markdown-download-btn-hover-bg: #fff;
  --markdown-download-btn-text: #0003;
  --markdown-download-btn-hover-text: #000;
  --markdown-download-btn-shadow: #0000000a;
  --markdown-table-border: #ecf0f5;
  --markdown-table-header-bg: #F8FAFC;

  // Logo动画相关颜色
  --logo-animation-0-stroke: rgba(#{hex-to-rgb($common-color-1)}, 0.3);
  --logo-animation-1-stroke: rgba(#{hex-to-rgb($common-color-1)}, 0.9);
  --logo-animation-2-stroke: #{$common-color-1};
  --logo-animation-3-stroke: #{$common-color-1}; 

  // 复制成功提示
  --copy-btn-bg-hover: #f3f3f3;
  --copy-btn-text-hover: #{$common-color-1};

  // 套餐页面
  --plan-item-bg: #fff;
  --plan-item-border: #ECF0F5;
  --plan-item-border-medium: #ECF0F5aa;
  --plan-item-border-bottom: #fff;
  --plan-item-border-hover-a: #FF4C00;
  --plan-item-border-hover-b: #ECF0F5aa;
  --plan-item-border-hover-c: #fff;
  --plan-item-card-color-a: #FFD3C0;
  --plan-item-card-color-b: #FFF2EC;
  --plan-item-card-color-c: #fff;

  // markmap
  --markmap-text: rgba(#{hex-to-rgb($common-color-1)}, 0.8);
}
  
:root[class~='dark'] {
  color-scheme: dark;

  $white: #171717;
  $black: #fff;

  --white: #{$white};
  --black: #{$black};
  --white-rgb: #{hex-to-rgb($white)};
  --black-rgb: #{hex-to-rgb($black)};
  --header-bar-height: 78px;
  
  // new global

  /**
  @params (no used | used)
  if used, please show how many times it has been used
  **/
  // primary
  --p: #{$white};
  --p1: #{$dark-color-1};
  --p2: #{$dark-color-4};
  --p3: #{$dark-color-1};
  --p4: #{$dark-color-2};
  --p5: #{$dark-color-5};
  --p6: #{$dark-color-6};   
  --p7: #999;   
  --p8: #{$dark-color-3};
  --p9: #{$dark-color-3};   
  --pf: #{$black};

  // secondary
  --s: #{$dark-color-4};
  --s1: #CDCDCD;
  --s2: #{$dark-color-3};
  --s3: #CCC;
  --s4: #{$dark-color-2};
  --sf: #F1F1F1;
  
  // accent
  --a: #FFC107;
  --a1: #FF9800;
  --af: #{$black};
  
  // destructive
  --d: #FF3737;
  --d1: #{$common-color-3};
  --d2: #F00;
  --d3: #F65134;
  --df: #{$black};
  
  // neutral
  --n: #7A288A;
  
  // functional
  --border: #{$dark-color-3};
  --border1: #535252;
  --dialog: #00000014;
  --dialog1: #333;
  --shrink: #{$dark-color-5};
  --shrink-hover: #{$dark-color-5};;
  --model-select-bg: #{$dark-color-4};
  --model-select-text: #{$dark-color-3};
  --model-select-text-active: #{$dark-color-2};
  --model-title-text: #{$dark-color-2};
  --model-display-text: #{$dark-color-1};
  --chat-text-color: #{$dark-color-2};
  --chat-link-color: #{$dark-color-2};
  --network-card-bg: #{$dark-color-4};
  --network-tab-active: #{$dark-color-2};
  --network-tab-divider: #{$dark-color-8};
  --network-card-border: transparent;
  --send-box-bg: #242424;
  --send-box-border: transparent;
  --send-box-character-limit: #{$common-color-2};
  --send-box-placeholder: #929292;
  --send-box-button-bg: #{$dark-color-2};
  --send-button-disabled-bg: #{$dark-color-3}; 
  --send-box-button-text: #{$dark-color-8};
  --send-button-disabled-text: #{$dark-color-2};
  --send-box-resend-button: #333;
  --send-box-stop-button-bg: #{$dark-color-2};
  --send-box-stop-button-text: #{$dark-color-7};
  --attachment-upload-button-bg: #{$dark-color-6};
  --attachment-upload-button-hover-bg: #{$dark-color-6};
  --attachment-upload-icon: #{$light-color-3};
  --attachment-card-bg: #{$dark-color-4};
  --attachment-card-file-name: #{$dark-color-2};
  --attachment-card-file-name-extension: #{$dark-color-3};
  --attachment-card-info: #{$dark-color-3};
  --attachment-button-icon: #{$light-color-3};
  --attachment-close-button-bg: #{$dark-color-5};
  --attachment-close-button-icon: #{$dark-color-3};
  --control-button-group-bg: #{$dark-color-4};
  --control-button-group-text-hover: #{$dark-color-2};
  --control-button-group-text-selected: var(--d3);
  --app-bg: #{$dark-color-7};
  --user-info-bg: #{$dark-color-1};
  --user-dialog-bg: #191919;
  --sidebar-bg: #{$dark-color-7};
  --sidebar-option: #{$dark-color-1};
  --sidebar-option-bg: #{$dark-color-4};
  --sidebar-option-text: #{$dark-color-1};
  --sidebar-new-border: #{$dark-color-5};
  --sidebar-add-group-color: #BBB;
  --mobile-sidebar-shadow-bg: #212020cc;
  --share-dialog-bg: #17171780;
  --share-dialog-text: #{$light-color-3};
  --share-dialog-link-bg: #1B1B1B;
  --egg-redemption-input-bg: #{$common-color-1};
  --egg-redemption-ticket-bg: #{$dark-color-8};
  --share-dialog-divider: #272727;
  --input-label-color: #{$dark-color-5};
  --theme-current-menu-text: #{$common-color-2};
  --theme-not-current-menu-text: #{$dark-color-1};

  // 绘图比例按钮
  --ratio-button-bg: #{$dark-color-8};
  --ratio-button-text: #{$dark-color-2};
  --ratio-button-hover-bg: #{$dark-color-5};
  --ratio-button-hover-text: #{$dark-color-2};
  --ratio-button-active-bg: #{$common-color-3};
  --ratio-button-active-text: #{$dark-color-2};

  // tools调用提示
  --shark-text-color: #{$dark-color-3};
  --shark-shadow-color: rgba(#{hex-to-rgb($common-color-2)}, 0.7);

  // 拖拽附件占位符图标
  --drag-attachment-placeholder-icon: #3071f5;

  // 背景点
  --dot-bg: #1B1B1B;
  --dot-item: #2A2A2A;

  // login swiper
  --login-card-bg: #{$dark-color-8};
  --login-code-input-border: #{$dark-color-3};

  // Artifacts Code
  --artifacts-card-bg: #{$dark-color-4};
  --artifacts-card-text: #{$dark-color-2};
  --artifacts-card-desc: #{$dark-color-3};
  --artifacts-code-bg: #1E1E1E;
  --artifacts-code-border: #363636;
  --artifacts-code-tabs: #1E1E1E;
  --artifacts-code-tabs-border: transparent;
  --artifacts-code-head-bg: #272727;
  --artifacts-code-path-bg: #272727;
  --artifacts-code-deploy-bg: #FFF;
  --artifacts-code-deploy-text: #000;

  // Markdown样式相关变量
  --markdown-media-border-color: #282c3400;
  --markdown-media-hover-border: #282c34;
  --markdown-download-btn-bg: #282c3433;
  --markdown-download-btn-hover-bg: #282c34;
  --markdown-download-btn-text: #fff3;
  --markdown-download-btn-hover-text: #fff;
  --markdown-download-btn-shadow: #00000012;
  --markdown-table-border: #282C34;
  --markdown-table-header-bg: #1E2227;

  // Logo动画相关颜色
  --logo-animation-0-stroke: rgba(#{hex-to-rgb($common-color-2)}, 0.3);
  --logo-animation-1-stroke: rgba(#{hex-to-rgb($common-color-2)}, 0.9);
  --logo-animation-2-stroke: #{$common-color-2};
  --logo-animation-3-stroke: #{$common-color-2};

  // 复制成功提示

  --copy-btn-bg-hover: #{$dark-color-6};;
  --copy-btn-text-hover: #{$dark-color-2};

  // 套餐页面
  --plan-item-bg: #272727;
  --plan-item-border: #272727;
  --plan-item-border-bottom: #272727;
  --plan-item-border-medium: #272727aa;

  // 需求变为hover深浅边框效果一样
  --plan-item-border-hover-a: #FF4C00;
  --plan-item-border-hover-b: #424242aa;
  --plan-item-border-hover-c: #272727;

  // --plan-item-border-hover-a: #363636;
  // --plan-item-border-hover-b: #ECF0F5aa;
  // --plan-item-border-hover-c: #272727;
  --plan-item-card-color-a: #933D13;
  --plan-item-card-color-b: #4c362b;
  --plan-item-card-color-c: #272727;

  // markmap
  --markmap-text: rgba(#{hex-to-rgb($common-color-2)}, 0.8);
}

// keyframes list
/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes foolishOut {
  0% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1) rotate(360deg);
  }

  20% {
    opacity: 1;
    transform-origin: 0% 0%;
    transform: scale(0.5, 0.5) rotate(0deg);
  }

  40% {
    opacity: 1;
    transform-origin: 100% 0%;
    transform: scale(0.5, 0.5) rotate(0deg);
  }

  60% {
    opacity: 1;
    transform-origin: 0%;
    transform: scale(0.5, 0.5) rotate(0deg);
  }

  80% {
    opacity: 1;
    transform-origin: 0% 100%;
    transform: scale(0.5, 0.5) rotate(0deg);
  }

  100% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0) rotate(0deg);
  }
}

@keyframes wobble {
  0% {
    transform: translate3d(0, 0, 0);
  }

  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }

  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }

  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }

  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes thinkingDots {
  0%, 20% {
    content: '.';
  }

  40% {
    content: '..';
  }

  60% {
    content: '...';
  }

  80%, 100% {
    content: '';
  }
}




