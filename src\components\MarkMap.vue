<template>
  <div>
    <slot name="button" :open-mind-map="openMindMap">
      <el-button @click="openMindMap">查看MindMap</el-button>
    </slot>

    <ResizablePanel
      v-model:visible="drawerVis"
      v-model:full-visible="isFullscreen"
      :full-screenable="false"
      :width="getPanelWidth"
      :push-chat-content="false"
      :hide-header-on-specific-fullscreen="true"
      @toggle-full-screen="handleToggleFullScreen"
    >
      <!-- 自定义头部左侧操作按钮 -->
      <template #header-actions-left>
        <div class="ml-2 text-16px font-medium">{{ $t('markMap.title') }}</div>
      </template>

      <!-- 主要内容区域 -->
      <template #content>
        <div
          class="markmap-container relative size-full p-4"
          :class="[
            isEditMode ? 'grid' : 'flex',
            isPc ? 'grid-cols-3 gap-4' : 'grid-rows-2 gap-2',
          ]"
        >
          <!-- 左侧编辑区域 - 只在编辑模式下显示 -->
          <div
            v-if="isEditMode"
            class="col-span-1 h-full pb-18px"
            :class="[!isPc && 'order-2']"
          >
            <CodeEditor
              v-model="cleanedContent"
              :streaming="false"
              :options="{
                readOnly: false,
                language: 'markdown',
                minimap: {
                  enabled: false,
                },
              }"
            />
          </div>

          <!-- 右侧思维导图 -->
          <div
            class="relative flex"
            :class="[
              !isEditMode && 'size-full',
              isPc && isEditMode ? 'col-span-2' : '',
              !isPc && 'order-1']"
          >
            <div class="absolute left-0 top-0 z-10 flex space-x-2" :class="{ 'pt-36px': isClient && isEditMode }">
              <button class="markmap-button" :title="$t('markMap.zoomIn')" @click="zoomIn">
                <i class="i-ri-zoom-in-line"></i>
              </button>
              <button class="markmap-button" :title="$t('markMap.zoomOut')" @click="zoomOut">
                <i class="i-ri-zoom-out-line"></i>
              </button>
              <button class="markmap-button" :title="$t('markMap.fitScreen')" @click="fitScreen">
                <i class="i-ri-refresh-line"></i>
              </button>
              <el-popover
                trigger="click"
                popper-class="markmap-popover"
                :show-arrow="false"
                placement="bottom-start"
                class="min-w-auto"
                :width="20"
              >
                <template #reference>
                  <button class="markmap-button" :title="$t('markMap.download')">
                    <i class="i-ri-download-line"></i>
                  </button>
                </template>
                <div class="flex flex-col gap-2 text-12px font-Lexend">
                  <button
                    class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
                    @click="downloadPNG"
                  >
                    PNG
                  </button>
                  <button
                    class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
                    @click="downloadSVG"
                  >
                    SVG
                  </button>
                  <button
                    class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
                    @click="downloadHTML"
                  >
                    HTML
                  </button>
                  <button
                    class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
                    @click="downloadMarkdown"
                  >
                    Markdown
                  </button>
                </div>
              </el-popover>
              <button
                v-if="!isEditMode"
                class="markmap-button"
                :title="$t('markMap.fullscreen')"
                @click="toggleFullscreen"
              >
                <i :class="isFullscreen ? 'i-ri-fullscreen-exit-line' : 'i-ri-fullscreen-line'"></i>
              </button>
              <button
                v-if="isPc"
                class="markmap-button mr-2 text-14px"
                :title="isEditMode ? $t('markMap.exitEditMode') : $t('markMap.enterEditMode')"
                @click="() => toggleEditMode()"
              >
                <i :class="isEditMode ? 'i-tabler-code-off' : 'i-tabler-code'"></i>
              </button>
            </div>
            <svg ref="svgRef" class="flex-1"></svg>
          </div>
        </div>
      </template>
    </ResizablePanel>
  </div>
</template>

<script setup lang="ts">
import { throttle } from 'lodash'
import { fillTemplate } from 'markmap-render'
import { Markmap } from 'markmap-view'
import {
  calculateScaledDimensions,
  downloadBlob,
  svgToBase64,
  svgToPngBlob,
} from '@/common/imageHandler'
import { transformer } from '@/common/markmap'
import CodeEditor from '@/components/CodeEditor.vue'
import ResizablePanel from '@/components/ResizablePanel.vue'
import { useAppStore } from '@/stores/app'

const props = defineProps<{
  content: {
    string: string
  }
  unionid?: string
}>()
const emits = defineEmits(['closed'])

// 处理从ResizablePanel接收到的toggleFullScreen事件
function handleToggleFullScreen(value: boolean) {
  if (value === false && isEditMode.value) {
    // 当全屏关闭时，如果处于编辑模式，则关闭编辑模式
    toggleEditMode(false)
  }
  setTimeout(() => {
    fitScreen()
  }, 500)
}
const appStore = useAppStore()
const { isPc, isClient, chatViewSectionWidth } = storeToRefs(appStore)

const isFullscreen = ref(false)
const isEditMode = ref(false)
const editableContent = ref('')

const currentContent = computed(() => {
  if (isEditMode.value) {
    return cleanedContent.value
  }
  else {
    // 优先使用editedContent，其次是编辑模式下的内容，最后是原始内容
    return editedContent.value || props.content?.string?.trim() || ''
  }
})

const getPanelWidth = computed(() => {
  if (isFullscreen.value) {
    return '100%'
  }
  if (isPc.value) {
    // 因为滚动条宽度为8px，因为布局使用了scrollbar-gutter: stable，所以PC永远需要加8px
    return `${chatViewSectionWidth.value + 8}px`
  }
  else {
    return '100%'
  }
})

function decodeHTMLEntities(text: string) {
  const textarea = document.createElement('textarea')
  textarea.innerHTML = text
  return textarea.value
}

/**
 * 过滤Markdown内容，移除 citation span 标签和 Mermaid 代码块。
 * 例如：<span class="citation">xxx</span>
 * ```mermaid
 * flowchart TD
 *     A --> B
 * ```
 * @param text 要处理的文本
 */
function filterMarkdownContent(text: string): string {
  if (!text) { return '' }
  // 移除 citation span 标签
  // 移除 Mermaid 代码块
  const filteredText = text
    .replace(/<\s*span\s+class\s*=\s*["']citation["']\s*>[^<]*<\/\s*span\s*>/gi, '')
    .replace(/```mermaid[\s\S]*?```/g, '')

  return filteredText
}

let mm: Markmap | null = null

// 用于存储编辑后的内容
const editedContent = ref('')

// 更新update函数，使用editedContent或原始content
const throttledUpdate = throttle(() => {
  try {
    const contentToRender = filterMarkdownContent(currentContent.value) // 过滤内容
    const { root } = transformer.transform(contentToRender)
    mm?.setData(root)
    fitScreen()
  }
  catch {}
}, 500)

const svgRef = ref<SVGSVGElement>()

// 自定义toolbar功能
function zoomIn() {
  mm?.rescale(1.25)
}

function zoomOut() {
  mm?.rescale(0.8)
}

function fitScreen() {
  mm?.fit()
}

function initMindMap() {
  mm = Markmap.create(svgRef.value, {
    duration: 300,
    autoFit: true,
    style: (id) => {
      return `
      .${id} .markmap-foreign>div>div { 
        color: var(--markmap-text);
      }
    `
    },
  })
  // 初次加载直接更新，不使用节流版本
  const contentToUse = filterMarkdownContent(editedContent.value || props.content?.string?.trim() || '')
  const { root } = transformer.transform(contentToUse)
  mm?.setData(root)
  setTimeout(() => {
    fitScreen()
  }, 500)
}

/**
 * 获取处理后的内容，从根节点文本开始
 * 使用decodeHTMLEntities获取根节点文本
 * 因为AI可能一开始啰嗦很多内容，从根节点文本开始截取之后的内容
 * 根文本之前的行会被去除
 */
function getCleanedContent() {
  if (!editableContent.value) { return '' }

  // 解析Markdown以获取根节点文本
  try {
    const dummyContent = editableContent.value.trim()
    const { root } = transformer.transform(dummyContent)

    let rootText = ''
    if (root.content) {
      rootText = root.content
    }
    else if (root.children?.length) {
      // 有些时候没有一级，直接从二级开始的话，就不存在root.content
      // 此时使用第一个子节点的内容作为根节点文本
      rootText = root.children[0]?.content
    }

    if (!rootText) {
      return dummyContent
    }

    // 找到根节点文本在原始内容中的位置
    const decodedRootText = decodeHTMLEntities(rootText)
    const rootIndex = dummyContent.indexOf(decodedRootText)

    if (rootIndex > 0) {
      // 找到包含root.content的那一行的起始位置
      const lineStartIndex = dummyContent.lastIndexOf('\n', rootIndex)

      // 如果找不到换行符（说明是第一行），直接返回原始内容
      if (lineStartIndex === -1) {
        return dummyContent
      }

      // 从行开始位置截取内容，保留包含标记符（如##）的整行
      return dummyContent.substring(lineStartIndex + 1)
    }

    return dummyContent
  }
  catch {
    return editableContent.value?.trim()
  }
}

const cleanedContent = ref('')

watch(editableContent, () => {
  const cleanedHead = getCleanedContent()
  cleanedContent.value = filterMarkdownContent(cleanedHead || '')
})

// 下载为PNG
const downloadPNG = async () => {
  try {
    if (!svgRef.value) { return }

    // 获取SVG元素和尺寸
    const svgElement = svgRef.value
    const svgRect = svgElement.getBoundingClientRect()
    const width = svgRect.width
    const height = svgRect.height

    // 计算缩放后的尺寸
    const { width: scaledWidth, height: scaledHeight } = calculateScaledDimensions(width, height, 1500)

    // 克隆SVG并设置属性
    const clonedSvg = svgElement.cloneNode(true) as SVGSVGElement
    clonedSvg.setAttribute('width', width.toString())
    clonedSvg.setAttribute('height', height.toString())
    clonedSvg.setAttribute('viewBox', `0 0 ${width} ${height}`)

    // 序列化SVG
    const svgData = new XMLSerializer().serializeToString(clonedSvg)

    const svgBase64 = svgToBase64(svgData)

    // 转换为PNG Blob
    const pngBlob = await svgToPngBlob(svgBase64, scaledWidth, scaledHeight)

    // 下载文件
    downloadBlob(pngBlob, `Juchats-MindMap-${Date.now()}.png`)
  }
  catch (error) {
    console.error('Export failed:', error)
  }
}

// 下载为SVG
const downloadSVG = () => {
  if (!svgRef.value) { return }

  const svgData = new XMLSerializer().serializeToString(svgRef.value)
  const blob = new Blob([svgData], { type: 'image/svg+xml' })
  downloadBlob(blob, `Juchats-MindMap-${Date.now()}.svg`)
}

// 下载为交互式HTML
const downloadHTML = () => {
  try {
    const contentToUse = isEditMode.value ? editableContent.value?.trim() || '' : props.content?.string?.trim() || ''
    if (!contentToUse) { return }

    // 使用markmap-render的fillTemplate函数生成交互式HTML
    const { root } = transformer.transform(contentToUse)
    const { scripts, styles } = transformer.getAssets()
    const html = fillTemplate(root, { scripts, styles })

    // 创建并下载Blob
    const blob = new Blob([html], { type: 'text/html' })
    downloadBlob(blob, `Juchats-MindMap-${Date.now()}.html`)
  }
  catch (error) {
    console.error('Export HTML failed:', error)
  }
}

// 下载为Markdown
function downloadMarkdown() {
  try {
    // 使用编辑模式的内容或已编辑内容或原始内容
    const contentToDownload = currentContent.value

    if (!contentToDownload) { return }

    // 创建并下载Blob
    const blob = new Blob([contentToDownload], { type: 'text/markdown' })
    downloadBlob(blob, `Juchats-MindMap-${Date.now()}.md`)
  }
  catch (error) {
    console.error('Export Markdown failed:', error)
  }
}

onUpdated(() => {
  // 当有内容可渲染（原始内容或编辑模式下的内容）且抽屉打开且SVG元素存在时，更新思维导图
  if ((props.content?.string?.trim() || (isEditMode.value && editableContent.value)) && drawerVis.value && svgRef.value) {
    throttledUpdate()
  }
})

const drawerVis = ref(false)

// 监听面板关闭事件
watch(drawerVis, (newVal) => {
  if (!newVal) {
    handleClose()
  }
})

// 关闭思维导图面板
function handleClose() {
  mm?.destroy()
  isEditMode.value = false
  editedContent.value = '' // 重置编辑内容
  emits('closed', props.unionid)
}

// 打开思维导图面板
function openMindMap() {
  drawerVis.value = true
  nextTick(() => {
    initMindMap()
  })
}

// 进入全屏模式
function enterFullscreen() {
  isFullscreen.value = true
}

// 退出全屏模式
function exitFullscreen() {
  isFullscreen.value = false
}

// 切换全屏
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
  setTimeout(() => {
    fitScreen()
  }, 500)
}

// 切换编辑模式
// 如果传入参数，则直接设置编辑模式状态；如果不传参数，则切换编辑模式
function toggleEditMode(forceState?: boolean) {
  const newEditMode = forceState !== undefined ? forceState : !isEditMode.value

  // 如果退出编辑模式，保存用户编辑的内容
  if (isEditMode.value && !newEditMode && cleanedContent.value) {
    editedContent.value = cleanedContent.value
  }

  // 如果进入编辑模式
  if (!isEditMode.value && newEditMode) {
    // 确保是全屏状态
    enterFullscreen()
    // 设置左侧编辑器里的内容（优先使用用户已编辑的内容）
    editableContent.value = editedContent.value || props.content?.string || ''
  }
  else if (isEditMode.value && !newEditMode) {
    exitFullscreen()
  }

  // 设置编辑模式
  isEditMode.value = newEditMode

  // 在状态变化后更新图
  nextTick(() => {
    throttledUpdate()
  })
}

// 监听编辑器内容变化，更新思维导图
watch(cleanedContent, () => {
  if (isEditMode.value && drawerVis.value) {
    throttledUpdate()
  }
})

onUnmounted(() => {
  mm?.destroy()
  throttledUpdate.cancel()
})
</script>

<style lang="scss" scoped>
.markmap-download-button {
  @apply block w-full flex-y-c justify-between rounded-md px-4 py-2 text-left hover:(bg-[var(--user-info-bg)] text-#000);
}

.markmap-button {
  @apply transition-cusbezier-150 size-32px flex-c text-#24292e rounded border border-solid hover:(text-#000 bg-#eee5) dark:(hover:text-#fff text-#D8D8D8a9 border-#575E6988);
}

.markmap-popover {
  @apply I_shadow-none border border-solid border-#E5E5E5 rounded py-2 px-4 text-12px font-Lexend dark:(border-#575E69 text-#D8D8D8);
}
</style>
