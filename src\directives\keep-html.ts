import type { VNode } from 'snabbdom'
import type { App } from 'vue'
import {
  attributesModule,
  classModule,
  datasetModule,
  h,
  init,
  propsModule,
  styleModule,
  toVNode,

} from 'snabbdom'

const patch = init([
  {
    create(_, vnode) {
      // if (vnode.elm && vnode.elm instanceof Element
      //   // SVG标签以及内部标签不添加动画
      //   && vnode.sel !== 'svg'
      //   && (!vnode.data || vnode.data.ns !== 'http://www.w3.org/2000/svg')) {
      //   vnode.elm.classList.add('animationFadeInFast')
      // }
    },
  },
  // 通过传入模块初始化 patch 函数
  styleModule, // 支持内联样式同时支持动画
  classModule, // 开启 classes 功能
  propsModule, // 支持传入 props
  attributesModule,
  datasetModule,
])

export default function (app: App<Element>) {
  const tmp = document.createElement('div')
  let oldVnode: VNode
  app.directive('keep-html', {
    mounted(el: HTMLElement, binding: any) {
      el.innerHTML = ''
      tmp.innerHTML = binding.value
      if (el.textContent && el.textContent.trim().length === 0 && el.firstElementChild?.tagName !== 'IFRAME') {
        oldVnode = patch(el, h('!'))
      }
      else {
        try {
          oldVnode = patch(el, toVNode(tmp))
        }
        catch {
        }
      }
    },
    beforeUpdate(el: Element, binding) {
      if (binding.value !== binding.oldValue) {
        try {
          tmp.innerHTML = binding.value
          oldVnode = patch(oldVnode, toVNode(tmp))
        }
        catch {
        }
      }
    },
    beforeUnmount() {
      tmp.remove()
    },
  })
}
