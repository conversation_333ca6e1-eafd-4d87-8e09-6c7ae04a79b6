<template>
  <HomeBase>
    <iframe :src="frame" frameborder="0" />
  </HomeBase>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import HomeBase from './components/HomeBase.vue'

const route = useRoute()
const frame: any = ref('')

onMounted(async () => {
  if (route.query.static) {
    frame.value = `${location.origin}/static/${route.query.static}`
  }
})
</script>

<style lang="scss" scoped>
iframe {
  width: 80%;
  height: calc(100% - 200px);
}
</style>
