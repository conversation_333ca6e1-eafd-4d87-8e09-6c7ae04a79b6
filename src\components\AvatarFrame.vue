<script setup lang="ts">
// import dark_avatar from '@/assets/images/user-default-avatar.svg'
import default_avatar from '@/assets/images/user-default-avatar.svg'
import { useAppStore } from '@/stores/app'
import { nanoid } from 'nanoid'

const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  shadow: {
    type: Boolean,
    default: false,
  },
  upload: {
    type: Boolean,
    default: false,
  },
  border: {
    type: Boolean,
    default: false,
  },
})

const avatarSrc = computed(() => props.src || default_avatar)

const uniqueId = nanoid()
const { isPc } = storeToRefs(useAppStore())
</script>

<template>
  <div class="avatar-frame" :class="name">
    <div
      v-if="shadow"
      class="absolute inset-0 m-auto size-0 bg-transparent content-['']"
      :class="[isPc ? 'shadow-[0_0_20px_20px_#00000020]' : 'shadow-[0_0_13px_13px_#00000020]']"
    ></div>
    <svg
      class="absolute h-full w-full"
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
    >
      <path
        v-if="!border"
        d="M0 18 Q0 0 18 0 L22 0 Q40 0 40 18 L40 22 Q40 40 22 40 L18 40 Q0 40 0 22 Z"
        fill="none"
        stroke="#000000"
        stroke-width="0"
      ></path>
      <path
        v-else
        d="M0 18 Q0 0 18 0 L22 0 Q40 0 40 18 L40 22 Q40 40 22 40 L18 40 Q0 40 0 22 Z"
        fill="none"
        stroke="#d2dfe9"
        stroke-dasharray="1,1"
      ></path>
      <clipPath :id="uniqueId">
        <path d="M0 18 Q0 0 18 0 L22 0 Q40 0 40 18 L40 22 Q40 40 22 40 L18 40 Q0 40 0 22 Z" />
      </clipPath>
      <image
        class="h-full w-full"
        :clip-path="`url(#${uniqueId})`"
        :xlink:href="avatarSrc"
        alt=""
      />
      <path
        :class="upload && !border ? 'avatar-frame__cover' : 'op-0'"
        d="M0 18 Q0 0 18 0 L22 0 Q40 0 40 18 L40 22 Q40 40 22 40 L18 40 Q0 40 0 22 Z"
        fill="#00000050"
        stroke="#000000"
        stroke-width="0"
      ></path>
    </svg>
    <div v-if="upload && !border" class="avatar-frame__hover">
      上传头像
    </div>
  </div>
</template>

<style lang="scss" scoped>
.avatar-frame__cover {
  transition: all 0.3s var(--ani-bezier);
  opacity: 0;
}

.avatar-frame__hover {
  @apply absolute flex-c size-full text-[#fff] text-3.5 op-0 transition-cusbezier-300;
}

.avatar-frame {
  @apply relative shrink-0;

  &:hover {
    .avatar-frame__hover {
      opacity: 1;
    }

    .avatar-frame__cover {
      opacity: 1;
    }
  }
}
</style>
