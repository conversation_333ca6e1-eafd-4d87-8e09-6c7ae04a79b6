import { useEventListener } from '@vueuse/core'
import { ref, type Ref, watch } from 'vue'

/**
 * 监听输入法输入
 * @param target 监听的元素
 * @returns 是否正在输入
 */
export function useComposition(target: Ref<HTMLElement | null>) {
  const isComposing = ref(false)

  watch(() => target.value, (el) => {
    if (!el) { return }

    useEventListener(el, 'compositionstart', () => {
      isComposing.value = true
    })

    useEventListener(el, 'compositionend', () => {
      isComposing.value = false
    })
  }, { immediate: true })

  return {
    isComposing,
  }
}
