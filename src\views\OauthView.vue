<template>
  <div
    v-if="!isNewUser"
    class="size-full fixed flex justify-center items-center"
  >
    <div class="text-center">
      <i class="text-[80px] i-ri-github-fill"></i>
      <div class>{{ $t("googleOauth.tip") }}</div>
    </div>
  </div>
  <HomeBase v-else>
    <div class="absolute w-full min-h-320px">
      <EGGSuccess :gift-info="giftInfo"></EGGSuccess>
    </div>
  </HomeBase>
</template>
<script setup lang="ts">
import services from "@/common/axios";

import { useRoute, useRouter } from "vue-router";
import { ElNotification } from "element-plus";
import HomeBase from "./components/HomeBase.vue";
import EGGSuccess from "./components/EGGSuccess.vue";
const giftInfo = ref({});
const route = useRoute();
const router = useRouter();
import { useGlobalI18n } from "@/hooks/useGlobalI18n";

const t = useGlobalI18n();
const isNewUser = ref(false);
onMounted(() => {
  const { from, code } = route.query;
  if (from === "BIND") {
    bindLogin({ code, name: "Chat" });
  } else if (from === "HEADER") {
    bindLogin({ code, name: "Chat" });
  } else {
    getLogin();
  }
});
const bindLogin = async ({ code, name }: any) => {
  await services.post("/user/bind/thirdParty/login", {
    githubCode: code,
  });
  ElNotification.success({ title: t("oauthView.bindSucceed") });
  router.push({ name });
};
const getLogin = async () => {
  const { code, inviteCode } = route.query;
  if (code) {
    try {
      const { token, exchangeInfo, newUser }: any = await services.post(
        "/user/third/party/regLogin",
        {
          githubCode: code,
          inviteCode,
        }
      );
      localStorage.setItem("token", token);
      if (newUser) {
        isNewUser.value = newUser;
        if (exchangeInfo) {
          giftInfo.value = exchangeInfo;
        }
        return;
      }
      router.replace({
        name: "Chat",
      });
    } catch ({ code }: any) {
      if ([402].includes(code)) {
        router.push({
          name: "Home",
          query: {
            isGithub: "true",
          },
        });
      } else {
        router.push({
          name: "Home",
          query: {
            isGithub: "true",
          },
        });
      }
    }
  }
};
</script>
