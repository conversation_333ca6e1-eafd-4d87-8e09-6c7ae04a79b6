import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login/:code?',
      name: 'Home',
      component: () => import('@/views/HomeView.vue'),
    },
    {
      path: '/chat/:id?',
      name: 'Chat',
      component: () => import('@/views/chat-view/index.vue'),
    },
    {
      path: '/oauth',
      name: 'Oauth',
      component: () => import('@/views/OauthView.vue'),
    },
    {
      path: '/gift',
      name: 'Gift',
      component: () => import('@/views/GiftView.vue'),
    },
    {
      path: '/export',
      name: 'Share',
      component: () => import('@/views/ShareView.vue'),
    },
    {
      path: '/frame',
      name: 'Frame',
      component: () => import('@/views/FrameView.vue'),
    },
    {
      path: '/stripe',
      name: 'Stripe',
      component: () => import('@/views/StripeView.vue'),
    },
    {
      path: '/cancel',
      name: 'Cancel',
      component: () => import('@/views/CancelView.vue'),
    },
    {
      path: '/googleOauth',
      name: 'GoogleOauth',
      component: () => import('@/views/GoogleOauth.vue'),
    },
    {
      path: '/policy',
      component: () => import('@/layouts/PolicyLayout.vue'),
      children: [
        {
          path: 'consumer-terms',
          name: 'ConsumerTerms',
          component: () => import('@/views/Policy/ConsumerTerms.vue'),
        },
        {
          path: 'usage-policy',
          name: 'UsagePolicy',
          component: () => import('@/views/Policy/UsagePolicy.vue'),
        },
        {
          path: 'privacy-policy',
          name: 'PrivacyPolicy',
          component: () => import('@/views/Policy/PrivacyPolicy.vue'),
        },
      ],
    },
  ],
})

router.beforeEach((to) => {
  const name = to.name

  if (to.fullPath === '/' && localStorage.token) {
    return { name: 'Chat' }
  }
  else if (
    !localStorage.token
    && ![
      'Home',
      'Share',
      'Oauth',
      'GoogleOauth',
      'ConsumerTerms',
      'UsagePolicy',
      'PrivacyPolicy',
    ].includes(name as string)
  ) {
    return {
      name: 'Home',
      query: Object.keys(to.query).length > 0 ? to.query : {},
    }
  }
})

export default router
