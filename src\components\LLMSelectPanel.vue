<template>
  <div
    class="rounded-10px bg-model-select-bg px-[44px] pt-[44px] font-Lexend shadow-[0_0_30px_0_#0000000f] transition-cusbezier-300 lt-md:(p-10px pl-56px) md:pr-[0px] md:pr-20px dark:shadow-xl !lt-xl:min-w-500px"
    :class="[
      { 'lt-md:(dark:bg-#171717)': !isPc },
      { '!pt-32px': !isPc && panelInHomeTop },
    ]"
    :style="panelWidthStyle"
  >
    <DefineTemplate>
      <div class="pb-35px">
        <div class="grid cols-5 w-950px gap-y-40px lt-lg:(cols-2 w-500px) lt-md:(cols-1 gap-y-20px) lt-xl:(cols-4 w-600px w-800px)">
          <template v-for="(models, provider) in modelData" :key="provider">
            <div v-if="models?.length" class="model_group">
              <div class="model_title">
                <i :class="getIconClass(provider)" />
                <span>{{ getDisplayName(provider) }}</span>
                <span
                  v-if="getTag(provider)"
                  class="ml-[7px] h-[20px] w-[68px] border rounded-[4px] text-center text-[11px] leading-[18px] font-[var(--font-family)]"
                  :class="getTagClass(provider)"
                >
                  {{ getTag(provider) }}
                </span>
              </div>
              <ul class="model_list">
                <li
                  v-for="item in models"
                  :key="item.id"
                  class="list_item"
                  @click="selectLLM(item.id)"
                >
                  <div
                    class="mr-3px"
                    :class="[getNameClass(provider), needsMaxWidth(provider) && xlAndLarger && 'max-w-120px']"
                  >
                    {{ item.showName }}
                  </div>
                  <i
                    :class="[modelSelectInfo.mode === item.id && 'I_visible']"
                    class="i-ri-check-fill check-icon invisible"
                  />
                </li>
              </ul>
            </div>
          </template>
        </div>

        <div v-if="showVoice" class="mt-16px lt-md:hidden">
          <div class="model_title">
            <i class="iconfont icon-Voice" />
            <div class="text-16px">Voice</div>
          </div>

          <!-- 语种选择 -->
          <div class="mt-15px pl-30px text-13px text-model-select-text transition-cusbezier-300 space-y-20px">
            <div class="flex flex-wrap">
              <div
                v-for="({ lang }, index) in modelSelectInfo.types"
                :key="index"
                class="list_item min-w-125px"
                @click="selectLanguage(index)"
              >
                {{ lang }}
                <i :class="[modelSelectInfo.languageType === index && 'I_visible']" class="i-ri-check-fill check-icon invisible" />
              </div>
            </div>

            <!-- 「文字 → 语音」选择 -->
            <el-collapse-transition>
              <div v-show="![languageTypeDefault, undefined].includes(modelSelectInfo.languageType)" class="flex flex-wrap">
                <div
                  v-for="{ id, label } in modelSelectInfo.enums"
                  :key="id"
                  class="list_item min-w-125px"
                  @click="selectTts(id)"
                >
                  {{ label }}
                  <i :class="[modelSelectInfo.ttsId === id && 'I_visible']" class="i-ri-check-fill check-icon invisible" />
                </div>
              </div>
            </el-collapse-transition>

            <!-- 朗读角色选择 -->
            <el-collapse-transition>
              <div v-show="modelSelectInfo.languageType !== languageTypeDefault" class="flex flex-wrap">
                <div
                  v-for="{ id, name } in currentReadRoleList"
                  :key="id" class="list_item min-w-125px"
                  @click="selectLanguageId(id)"
                >
                  {{ name }}
                  <i
                    :class="[modelSelectInfo.languageId === id && 'I_visible']"
                    class="i-ri-check-fill check-icon invisible"
                  />
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>
    </DefineTemplate>

    <div v-if="isPc">
      <ReuseTemplate />
    </div>

    <div
      v-else
      ref="el"
      class="scrollable-div overflow-auto transition-cusbezier-300"
      :class="[
        { 'hide-scrollbar': !scrollbarVisible },
      ]"
      :style="{
        height: `${mobileHeight}px`,
      }"
    >
      <ReuseTemplate />
    </div>
  </div>
</template>

<script setup lang="ts">
import { cssBreakPoints } from '@/config/cssBreakPoints'
import { getProviderIcon } from '@/config/providerIcons'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  llmPanelWidth: {
    type: String,
  },
  panelInHomeTop: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['closePanel'])

const mobileHeight = ref(800) // 默认高度

const updateHeight = () => {
  const height = window.innerHeight
  mobileHeight.value = props.panelInHomeTop
    ? height - 100
    : height - 200
}

onMounted(() => {
  // 初始化面板高度
  updateHeight()

  // 监听 resize 事件
  useEventListener(window, 'resize', updateHeight)
})

const showVoice = false // 不再展示语音选择

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()
const panelWidthStyle = computed(() => {
  if (props.llmPanelWidth) {
    return {
      minWidth: `${props.llmPanelWidth} !important`,
    }
  }
  else {
    return {}
  }
})

const { selectMode, selectLanguage, selectTts, selectLanguageId } = useAppStore()
const { modelSelectInfo, languageTypeDefault, isPc, modelData: rawModelData, currentReadRoleList } = storeToRefs(useAppStore())

// 定义供应商排序权重映射（值越大排序越靠后）
const providerSortWeights: Record<string, number> = {
  OPENAI: 100,
  ANTHROPIC: 200,
  GEMINI: 300,
  GROK: 400,
  VISION: 450,
  DEEPSEEK: 500,
  QWEN: 600,
  KIMI: 650,
}

// 对modelData进行排序处理
const modelData = computed(() => {
  if (!rawModelData.value) { return {} }

  // 获取所有存在的供应商
  const providers = Object.keys(rawModelData.value)

  // 将供应商分为两组：有权重的和没有权重的
  const providersWithWeight: string[] = []
  const providersWithoutWeight: string[] = []

  providers.forEach((provider) => {
    if (provider in providerSortWeights) {
      providersWithWeight.push(provider)
    }
    else {
      providersWithoutWeight.push(provider)
    }
  })

  // 根据权重排序有权重的供应商
  providersWithWeight.sort((a, b) => {
    return providerSortWeights[a] - providerSortWeights[b]
  })

  // 合并两组供应商：先是有权重的（按权重排序），然后是没有权重的（保持原始顺序）
  const sortedProviders = [...providersWithWeight, ...providersWithoutWeight]

  // 创建排序后的modelData对象，保持与原始modelData相同的类型
  const sortedModelData = {} as typeof rawModelData.value
  sortedProviders.forEach((provider) => {
    sortedModelData[provider] = rawModelData.value[provider]
  })

  return sortedModelData
})

function selectLLM(id: number) {
  selectMode(id)
  emits('closePanel')
}

const breakpoints = useBreakpoints(cssBreakPoints)
const xlAndLarger = breakpoints.greaterOrEqual('xl')

// 获取模型供应商的显示名称
function getDisplayName(provider: string | number): string {
  const providerStr = String(provider)
  return providerStr === providerStr.toUpperCase() ? providerStr.charAt(0) + providerStr.slice(1).toLowerCase() : providerStr
}

// 获取模型供应商的图标类
function getIconClass(provider: string | number): string {
  return getProviderIcon(String(provider), 'panel')
}

// 获取模型供应商的标签
function getTag(provider: string | number): string {
  const providerStr = String(provider)
  switch (providerStr) {
    case 'OPENAI':
      return 'Reasoning'
    case 'ANTHROPIC':
      return 'Multimodal'
    default:
      return ''
  }
}

// 获取模型供应商的标签样式
function getTagClass(provider: string | number): string {
  const providerStr = String(provider)
  switch (providerStr) {
    case 'OPENAI':
      return 'border-[#EDD6AD] bg-[#FFF2DC] text-[#BB9A60] shadow-[0_2px_5px_0_#fae6c7ba] dark:shadow-none'
    case 'ANTHROPIC':
      return 'border-[#C3EAAC] bg-[#EBFFDF] text-[#67A941] shadow-[0_2px_5px_0_#c3eaac7a] dark:shadow-none'
    default:
      return ''
  }
}

// 获取模型供应商的名称类
function getNameClass(provider: string | number): string {
  const providerStr = String(provider)
  return ['GEMINI', 'VISION'].includes(providerStr) ? 'truncate' : ''
}

// 判断是否需要最大宽度限制
function needsMaxWidth(provider: string | number): boolean {
  const providerStr = String(provider)
  return ['GEMINI', 'VISION'].includes(providerStr)
}

const el = ref(null)
const { isScrolling } = useScroll(el)

const scrollbarVisible = ref(false)
watch(isScrolling, (newVal) => {
  if (newVal) {
    scrollbarVisible.value = true
  }
  else {
    setTimeout(() => {
      scrollbarVisible.value = false
    }, 800)
  }
})
</script>

<style lang="scss" scoped>
.model_group {
  // @apply flex-1 ;
}

.model_title {
  @apply h-20px flex-y-c space-x-8px min-w-160px font-500 text-14px text-model-title-text tracking-0 text-justify lt-xl:(min-w-130px);
}

.model_list {
  @apply mt-15px pl-28px space-y-11px text-13px text-model-select-text;
}

.list_item {
  @apply cursor-pointer relative flex-y-c transition-cusbezier-300 ws-nowrap hover:(text-#384A67 dark:text-#cdcdcd) dark:(active:text-#f1f1f1);

  &:has(.check-icon.I_visible) {
    color: var(--model-select-text-active);
  }
}

.scrollable-div {
  background-color: transparent;
}

.scrollable-div::-webkit-scrollbar {
  width: 8px;
  border-radius: 4px;
  background-color: transparent;
}

.scrollable-div::-webkit-scrollbar-thumb {
  transition: background-color 0.5s;
  background-color: rgba(0, 0, 0, 10%);
  border-radius: 8px;
  background-clip: content-box;
  border: 2px solid transparent;
}

.scrollable-div.hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
