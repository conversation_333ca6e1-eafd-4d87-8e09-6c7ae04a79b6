<script setup lang="ts">
import type { UploadStatus } from 'element-plus'
import service from '@/common/axios'
import { getFileExt, getFileShowType } from '@/common/tools'
import { FILE_TYPE } from '@/model/common'

const props = defineProps({
  analyzeStatus: {
    type: String,
  },
  fileName: {
    type: String,
    required: true,
  },
  fileId: {
    type: String,
  },
  hover: {
    type: Boolean,
    default: false,
  },
  showType: {
    type: Boolean,
    default: false,
  },
  isParsing: {
    type: Boolean,
    default: false,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  uploadStatus: {
    type: String as PropType<UploadStatus>,
    default: 'ready',
  },
  onlyDisplay: {
    type: Boolean,
    default: false,
  },
})

const iframe = ref<HTMLIFrameElement>()
const fileLink = ref('')
const showParsingSuccess = ref(false)
const manualFetchedContent = ref('')

const fileShowType = computed(() => {
  return getFileShowType(props.fileName)
})

const otherPreview = computed(() => {
  const ext = getFileExt(props.fileName)
  return ['pdf', 'json', 'txt', 'markdown', 'md'].includes(ext)
})

const canPreview = computed(() => {
  return otherPreview.value || isOffice.value
})

const isOffice = computed(() => {
  return (
    fileShowType.value === FILE_TYPE.WORD
    || fileShowType.value === FILE_TYPE.PPT
    || fileShowType.value === FILE_TYPE.EXCEL
  )
})

const manualFetchContent = computed(() => {
  if (isOffice.value || fileShowType.value === FILE_TYPE.PDF) {
    return false
  }
  return true
})

async function getFileLink() {
  if (props.onlyDisplay) {
    return
  }
  const url = `${import.meta.env.VITE_APP_V1_API}/v1/app/file/getLink`
  const res = await service.post<any, { fileLink: string }>(
    url,
    {
      fileId: props.fileId,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  // office文件
  if (isOffice.value) {
    fileLink.value = `https://view.officeapps.live.com/op/view.aspx?src=${res.fileLink}`
  }
  else {
    fileLink.value = `${res.fileLink}`

    if (fileShowType.value !== FILE_TYPE.PDF) {
      fetch(fileLink.value).then(res => res.text()).then((text) => {
        manualFetchedContent.value = text
      })
    }
  }
}

function mouseEnter() {
  if (!fileLink.value && props.showType) {
    getFileLink()
  }
}

function openFile() {
  if (!fileLink.value) {
    return
  }
  window.open(fileLink.value, '_blank')
}
const cardMain = ref()
// const isHoveredCardMain = useElementHover(cardMain)
// const nameShouldBeDisplayed = computed(() => {
//   // 解析过程中只有hover了才显示文件名，其余情况都显示
//   return !props.isParsing || isHoveredCardMain.value
// })

const analysisFailed = computed(() => {
  return props.analyzeStatus === 'failed'
})

watch(props, (newVal) => {
  if (newVal.analyzeStatus === 'success') {
    showParsingSuccess.value = true
    setTimeout(() => {
      showParsingSuccess.value = false
    }, 2000)
  }
})
</script>

<template>
  <div
    v-debounce.mouseenter="mouseEnter"
    :class="{
      'hover-trigger': hover,
    }"
  >
    <div class="inline-block min-w-160px" :class="{ 'w-full': !hover }">
      <div
        ref="cardMain"
        :class="[hover ? 'hover:shadow-[0_0_2rem_0_#00000026]' : '']"
        class="flex items-start b-rd-[0.56rem] bg-[var(--attachment-card-bg)] p-[0.73rem] shadow-[0_0_2.5rem_0_#0000000f] transition-all"
      >
        <SendAttachmentFileIcon
          :always-show-icon="false"
          :upload-status="uploadStatus"
          :file-name="fileName"
        >
          <template #progress>
            <slot name="progress"></slot>
          </template>
          <template #success>
            <slot name="success"></slot>
          </template>
        </SendAttachmentFileIcon>
        <div
          class="flex flex-grow-1 flex-col self-stretch overflow-hidden text-left text-0.81rem text-[var(--attachment-card-file-name)] font-700 tracking-0 font-RobotoSlab !my-0 !ml-0.73rem"
        >
          <div :class="[analysisFailed && 'text-destructive-200']">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="fileName"
              placement="top"
              :disabled="onlyDisplay"
            >
              <div class="line-clamp-1">{{ fileName }}</div>
            </el-tooltip>
            <el-collapse-transition>
              <div v-if="analysisFailed">
                {{ $t("attachmentCard.parseFailure") }}
              </div>
            </el-collapse-transition>
          </div>
          <div class="relative flex-grow-1">
            <el-collapse-transition>
              <div v-if="!isParsing && !showParsingSuccess" class="absolute">
                <span
                  class="text-0.75rem text-[var(--attachment-card-file-name-extension)]"
                >{{
                  fileShowType === FILE_TYPE.OTHER
                    ? getFileExt(fileName)?.toUpperCase()
                    : fileShowType
                }}</span>
              </div>
            </el-collapse-transition>
            <el-collapse-transition>
              <div
                v-if="isParsing && !showParsingSuccess"
                class="absolute inline-block text-0.81rem text-[var(--attachment-card-info)]"
              >
                {{ $t("attachmentCard.parsing") }} <ThinkingDots />
              </div>
            </el-collapse-transition>
            <el-collapse-transition>
              <div
                v-if="showParsingSuccess"
                class="absolute inline-block text-0.81rem text-[var(--attachment-card-info)]"
              >
                {{ $t("attachmentCard.parseSuccess") }}
              </div>
            </el-collapse-transition>
          </div>
        </div>
        <!-- <i
          v-if="showType"
          class="i-ju-expand-full ml-0.3rem cursor-pointer text-[1.4rem] c-#CBD5E1 transition-all hover:c-gray"
          @click="openFile"
        ></i> -->
      </div>
    </div>
    <div
      v-if="hover && canPreview"
      :class="[hover && canPreview ? 'preview' : '']"
      class="invisible box-border h-0 max-h-800px w-full overflow-auto b-rd-0.56rem opacity-0 shadow-[0_0_2.5rem_0_#0000000f] transition-all"
    >
      <div v-if="manualFetchContent">
        {{ manualFetchedContent }}
      </div>
      <iframe
        v-else
        ref="iframe"
        :src="fileLink"
        frameborder="0"
        class="h-[calc(50vh-2.5rem)] w-full overflow-auto b-rd-0.56rem"
      ></iframe>
    </div>
  </div>
</template>

<style scoped lang="scss">
.hover-trigger {
  &:hover {
    .preview {
      @apply opacity-100 h-50vh visible mt-20px p-1.25rem;
    }
  }
}
</style>
