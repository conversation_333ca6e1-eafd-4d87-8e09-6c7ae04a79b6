<template>
  <div
    v-if="!isNewUser"
    class="w-full h-full fixed flex justify-center items-center"
  >
    <div class="text-center">
      <i class="text-[80px] ri-google-fill"></i>
      <div class>{{ $t("googleOauth.tip") }}</div>
    </div>
  </div>
  <HomeBase v-else>
    <div class="absolute w-full section-box">
      <EGGSuccess :gift-info="giftInfo"></EGGSuccess>
    </div>
  </HomeBase>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElNotification } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import HomeBase from "./components/HomeBase.vue";
import EGGSuccess from "./components/EGGSuccess.vue";
import { useGlobalI18n } from "@/hooks/useGlobalI18n";
const route = useRoute();
const router = useRouter();
const giftInfo = ref({});
const isNewUser = ref(false);

const t = useGlobalI18n();
const base64UrlDecode = (base64Url: any) => {
  let base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
  const padding = base64.length % 4;
  if (padding > 0) {
    base64 += "=".repeat(4 - padding);
  }
  const decodedString = atob(base64);
  try {
    return decodeURIComponent(escape(decodedString));
  } catch (e) {
    return "";
  }
};
onMounted(() => {
  try {
    const { params } = route.query;
    const { code, data, msg } = JSON.parse(base64UrlDecode(params));
    const { from, newUser, token, exchangeInfo } = data;
    if ([200].includes(code)) {
      if (from === "BIND") {
        ElNotification.success({ title: t("oauthView.bindSucceed") });
        router.push({ name: "Chat" });
      }
      if (from === "LOGIN") {
        if (token) {
          localStorage.setItem("token", token);
          if (newUser) {
            isNewUser.value = newUser;
            if (exchangeInfo) {
              giftInfo.value = exchangeInfo;
            }
            return;
          }
          router.push({ name: "Chat" });
        }
      }
    } else {
      throw msg;
    }
  } catch (error: any) {
    ElNotification.error({ title: error || t("googleOauth.loginRedirecting") });
    setTimeout(() => {
      router.push({ name: "Home" });
    }, 3000);
  }
});
</script>