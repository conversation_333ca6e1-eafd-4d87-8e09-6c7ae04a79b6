<template>
  <div ref="controlBarRef" class="control-bar relative w-fit">
    <div class="h-full flex items-center justify-end">
      <div
        class="control-bar__group flex select-none items-center justify-between text-14px font-Lexend"
        :class="`control-bar__group--${isArtifact ? (selected || 2) : selected}`"
      >
        <div @click="clearDialog">
          {{ $t('controlBar.clear') }}
        </div>
        <div
          :class="{ 'text-destructive-300 font-semibold': favorited }"
          @click="favorite()"
        >
          {{ $t("packageBox.starburst") }}
        </div>
        <div
          :class="{ 'control-bar__group--active': selected === 1 }"
          @click="select(1)"
        >
          {{ $t("packageBox.group") }}
        </div>
        <div
          v-if="!selected || selected > 1"
          :class="{ 'control-bar__group--active': selected === 2 }"
          @click="select(2)"
        >
          {{ $t("headerMore.export") }}
        </div>
        <div
          v-if="!isArtifact && (!selected || selected > 2)"
          @click="toShare(chatViewData.messageId, chatViewData.messageName)"
        >
          {{ $t("sideBar.share") }}
        </div>
        <!-- <div
          v-if="!selected || selected > 3"
          :class="selected === 4 ? 'control-bar__group--active' : ''"
          @click="select(4)"
        >
          删除
        </div> -->
      </div>
      <div
        class="control-bar__handle flex items-center justify-start"
        :class="[selected && `control-bar__handle--${selected}`]"
      >
        <template v-if="selected === 1">
          <div
            v-for="{ name, id } in childGroups"
            :key="id"
            class="line-clamp-1"
            :class="{
              'font-600 !text-control-button-group-text-selected':
                groupId === id,
            }"
            @click="moveGroup({ groupId: id })"
          >
            <span>{{ name }}</span>
            <i
              v-if="groupId === id"
              class="i-ri-check-line ml-1"
              @click="deleteMessage()"
            ></i>
          </div>
        </template>
        <!-- 导出的选项部分 -->
        <template v-if="selected === 2">
          <div
            v-for="item in exportOptions"
            :key="item.type"
            v-debounce="() =>
              download({
                messageId: chatViewData.messageId,
                inviteCode: userInfo.inviteCode,
                type: item.type,
                sections,
              })
            "
            :style="{ width: item.width }"
          >
            {{ item.label }}
          </div>
        </template>
        <template v-if="selected === 4">
          <div class="pl-4 pr-8 text-[#000000]" @click="deleteMessage">
            {{ $t("controlBar.confirmText") }}
          </div>
          <i class="i-ri-close-line mr-2" @click="select(4)"></i>
          <i class="i-ri-check-line" @click="deleteMessage()"></i>
        </template>
      </div>
    </div>
    <ExportImageDialog v-model:visible="visible" :canvas="canvas" />
  </div>
</template>

<script setup lang="ts">
import service from '@/common/axios'
import messageDB from '@/common/message'
import { notify } from '@/common/tools'
import { useControlBar } from '@/hooks/useControlBar'
import { useDialog } from '@/hooks/useDialog'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useModalPosition } from '@/hooks/useModalPosition'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { onClickOutside } from '@vueuse/core'

const props = defineProps({
  favorited: {
    type: Boolean,
    default: false,
  },
  content: {
    type: Object as any,
    default: null,
  },
  groups: {
    type: Array as any,
    default: () => [],
  },
  sections: {
    type: Array as any,
    default: () => [],
  },
  groupId: {
    type: Number,
    default: null,
  },
  messageId: {
    type: Number,
    default: null,
  },
})

const emits = defineEmits(['favorite', 'delete', 'grouping', 'refresh'])

const controlBarRef = ref()

const { userInfo } = storeToRefs(useUserStore())

const { canvas, visible, download, toShare } = useControlBar()
const { exportOptions, chatViewData, isArtifact } = storeToRefs(useAppStore())

const { modalStyle } = useModalPosition()
const { openDialog, closeDialog } = useDialog()
const t = useGlobalI18n()

const childGroups: any = computed(() => {
  return props.groups.filter(
    (element: any) => !element.starFlag && !element.defaultFlag,
  )
})

const selected = ref(0)
const select = (value: any) => {
  if (selected.value === value) {
    selected.value = 0
  }
  else {
    selected.value = value
  }
}
const favorite = () => {
  emits('favorite')
}
const moveGroup = ({ groupId }: any) => {
  emits('grouping', { groupId })
}
const deleteMessage = () => {
  emits('delete')
}

function clearDialog() {
  openDialog({
    titleContent: t('controlBar.clearDialogTitle'),
    style: modalStyle.value,
    content: () => t('controlBar.clearDialogContent').value,
    onSubmit: async () => {
      const data: any = await service.post('/gpt/clearChats', {
        id: chatViewData.value.messageId,
      })
      if (data.code === 200) {
        messageDB.deleteMessageById(chatViewData.value.messageId)
        notify.success({
          title: t('controlBar.clearDialogSuccess').value,
        })
      }
      emits('refresh')
      closeDialog(false)
    },
  })
}

defineExpose({
  select,
})
onMounted(async () => {})

// 使用 onClickOutside 处理点击外部关闭
onClickOutside(
  controlBarRef,
  () => {
    select(0)
  },
)
</script>

<style lang="scss" scoped>
.control-bar__group {
  @apply w-255px h-40px transition-all transition-cusbezier-300 bg-control-button-group-bg shadow-[0_5px_20px_0_#00000012] rounded-5px px-15px py-0 ml-20px text-#929292 relative z-1 whitespace-nowrap overflow-hidden dark:(shadow-2xl text-#F1F1F1);

  & > div {
    @apply transition-cusbezier-400 cursor-pointer font-normal shrink-0;

    &:hover {
      @apply text-control-button-group-text-hover font-bold text-[var(--black)];

      &:first-child {
        @apply text-destructive-300;
      }
    }
  }
}

.control-bar__group--active {
  @apply text-control-button-group-text-hover font-600;
}

.control-bar__group--1 {
  width: 160px;
}

.control-bar__group--2 {
  width: 210px;
}

.control-bar__group--4 {
  width: 290px;
}

.control-bar__handle {
  @apply h-40px mt-3px absolute right-0 bg-control-button-group-bg pt-0 pb-0 pl-40px pr-20px text-14px op-0 rounded-r-5px transition-cusbezier-300;

  & > div {
    @apply w-80px text-#929292 cursor-pointer text-center transition-cusbezier-300;

    &:hover {
      @apply text-control-button-group-text-hover font-600;
    }
  }
}

.control-bar__handle--1 {
  @apply right-170px op-100;
}

.control-bar__handle--2 {
  @apply right-220px op-100;
}

.control-bar__handle--4 {
  @apply right-280px op-100 whitespace-nowrap;

  & > div {
    @apply w-auto text-#000;

    &:hover {
      @apply font-normal;
    }
  }

  i {
    @apply size-28px rounded-50% flex-c cursor-pointer text-16px;

    &:nth-last-child(2) {
      @apply text-#f37979 bg-#ffe8e8;
    }

    &:last-child {
      @apply text-#72aa65 bg-#ddf6d7;
    }
  }
}
</style>
