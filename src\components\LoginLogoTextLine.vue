<template>
  <svg class="mb-10px w-215px" viewBox="0 0 125 50" xmlns="http://www.w3.org/2000/svg">
    <text class="text-28px font-600 font-[Kanit] italic" x="51%" y="70%" dominant-baseline="middle" text-anchor="middle" vector-effect="non-scaling-stroke">
      <tspan class="first-part">{{ text.slice(0, 2) }}</tspan>
      <tspan class="second-part">{{ text.slice(2) }}</tspan>
    </text>
  </svg>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

defineProps<{
  text: string
}>()

const { isApple } = storeToRefs(useAppStore())

const strokedasharray = computed(() => {
  return isApple.value ? '160' : '60%'
})
</script>

<style lang="scss" scoped>
.first-part {
  animation: code-bg-animated-ju 3.6s var(--ani-bezier);
  animation-fill-mode: both;
}

.second-part {
  animation: code-bg-animated-chats 3.6s var(--ani-bezier);
  animation-fill-mode: both;
}

@keyframes code-bg-animated-ju {
  0% {
    fill: #0000;
    stroke: #f058586a;
    stroke-dashoffset: 40%;
    stroke-dasharray: 0 v-bind(strokedasharray);
    stroke-width: 0.7;
  }

  26% {
    fill: #0000;
    stroke: #f05858e8;
    stroke-width: 0.5;
  }

  70% {
    fill: #f05858f1;
    stroke: #f0585814;
    stroke-width: 0.1;
  }

  90%,
  100% {
    fill: #F05858;
    stroke: #0000;
    stroke-dashoffset: -40%;
    stroke-dasharray: v-bind(strokedasharray) 0;
    stroke-width: 0;
  }
}

@keyframes code-bg-animated-chats {
  0% {
    fill: #0000;
    stroke: var(--logo-animation-0-stroke);
    stroke-dashoffset: 40%;
    stroke-dasharray: 0 v-bind(strokedasharray);
    stroke-width: 0.7;
  }

  26% {
    fill: #0000;
    stroke: var(--logo-animation-1-stroke);
    stroke-width: 0.5;
  }

  70% {
    fill: var(--logo-animation-2-stroke);
    stroke: var(--logo-animation-0-stroke);
    stroke-width: 0.1;
  }

  90%,
  100% {
    fill: var(--logo-animation-3-stroke);
    stroke: #0000;
    stroke-dashoffset: -40%;
    stroke-dasharray: v-bind(strokedasharray) 0;
    stroke-width: 0;
  }
}
</style>
