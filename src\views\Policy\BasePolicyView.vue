<template>
  <div class="h-full flex flex-col overflow-hidden">
    <h2 class="led-font mb-19px text-center text-48px lt-lg:(mb-30px text-26px)">{{ title }}</h2>
    <div class="policy-content c-scrollbar flex-1 overflow-auto text-14px" v-html="contentHTML"></div>
  </div>
</template>

<script setup lang="ts">
import { marked } from 'marked'
import { onMounted, ref } from 'vue'

const props = defineProps<{
  title: string
  mdPath: string
}>()

const contentHTML = ref('')

onMounted(() => {
  fetch(props.mdPath)
    .then(response => response.text())
    .then((markdownContent) => {
      contentHTML.value = marked(markdownContent)
    })
    .catch((error) => {
      console.error(`Error fetching ${props.title}:`, error)
    })
})
</script>

<style lang="scss">
@import './markdown2html';
</style>
