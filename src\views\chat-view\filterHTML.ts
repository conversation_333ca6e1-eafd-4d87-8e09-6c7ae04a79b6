import sanitizeHtml from 'sanitize-html'
import TurndownService from 'turndown'

export const filterHTML = async (html: string) => {
  const data = await fetch('https://strapi.herm.studio/api/img-proxy').then(res => res.json())
  const domain = data.data.domain as string[]

  const turndownService = new TurndownService()
  const sanizitedHtml = sanitizeHtml(html, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat(['img']),
    transformTags: {
      img: (_, attribs) => {
        if (attribs.src && attribs.src.startsWith('http')) {
          const sourceURL = new URL(attribs.src)
          if (domain.includes(sourceURL.host)) {
            const proxyURL = new URL('api/v1/proxy/img', import.meta.env.VITE_APP_IMAGE_URL || location.origin)
            proxyURL.searchParams.set('imgUrl', attribs.src)
            attribs.src = proxyURL.toString()
          }
        }

        return {
          tagName: 'img',
          attribs: {
            ...attribs,
          },
        }
      },
    },
  })
  const markdown = turndownService.turndown(sanizitedHtml)
  return markdown
}
