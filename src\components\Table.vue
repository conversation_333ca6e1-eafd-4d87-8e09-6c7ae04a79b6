<template>
  <table class="w-full border-none text-3.5">
    <thead>
      <tr class="border-b-1px border-dialog-100 text-primary-300">
        <th v-for="col in columns" :key="col.key" class="whitespace-nowrap border-none px-6 py-3 text-left font-normal tracking-wider uppercase">
          {{ col.label }}
        </th>
      </tr>
    </thead>
    <tbody class="px-2">
      <tr v-for="row in data" :key="row.id" class="border-b-1px border-dialog-100 border-dashed">
        <td v-for="col in columns" :key="col.key" class="font-Lexend whitespace-nowrap border-none px-6 py-4 text-[var(--sidebar-option-text)]">
          <component v-bind="row" :is="col.component" v-if="col.component"></component>
          <span v-else>{{ row[col.key] }}</span>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts" setup>
import type { Component, PropType } from 'vue'

export interface ColumnProps {
  key: string
  label: string
  component?: Component
}
interface DataProps {
  [k: string]: any
}
defineProps({
  columns: {
    type: Array as PropType<ColumnProps[]>,
    required: true,
  },
  data: {
    type: Array as PropType<DataProps[]>,
    required: true,
  },
})
</script>
