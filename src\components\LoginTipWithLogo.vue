<template>
  <div class="flex-y-c space-x-12px">
    <svg class="h-18px text-0px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" fill-rule="evenodd"><circle cx="37.3" cy="19.8" r="10.5" fill="#f43513" /><path class="fill-[var(--black)]" d="M.3 30.5h26.3v8.1H.3v-8.1z" /></svg>

    <div class="vertical-base space-x-8px">
      <span class="text-18px font-bold">Juchats</span>
      <span class="whitespace-nowrap text-14px text-#979797">{{ tip || '默认文案' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  tip?: string
}>()
</script>

<style lang="scss" scoped>
</style>
