import type { UserInfo } from '@/model/common'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'

export function useUserInfo() {
  const userStore = useUserStore()
  const { userInfo, userInfoV2 } = storeToRefs(userStore)

  const getUserInfo = async (): Promise<Ref<UserInfo>> => {
    await userStore.getUserInfo()
    return userInfo
  }

  const getUserInfoV2 = async (): Promise<Ref<UserInfo>> => {
    await userStore.getUserInfoV2()
    return userInfoV2
  }

  return {
    userInfo,
    userInfoV2,
    getUserInfo,
    getUserInfoV2,
  }
}
