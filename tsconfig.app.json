{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"composite": true, "target": "es2022", "jsxImportSource": "vue", "lib": ["es2022", "dom"], "baseUrl": "./", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "allowJs": true}, "include": ["./src/typings/auto-imports.d.ts", "./src/typings/components.d.ts", "env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.ts"], "exclude": ["src/**/__tests__/*"]}