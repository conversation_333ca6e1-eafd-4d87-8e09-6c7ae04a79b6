<template>
  <template v-if="isPc">
    <Transition name="slide-fade">
      <div
        v-if="isVisible"
        ref="modelSelectRef"
        class="absolute z-10 w-55 rounded-2.5 bg-model-select-bg p-lg text-3.5 shadow-[0_0.31rem_1.25rem_0] shadow-#00000012 lt-md:(relative w-full)"
        :style="
          position
            ? { top: `${position.top}px`, left: `${position.left}px` }
            : {}
        "
      >
        <div>
          <h2 class="text-primary-300 font-600">
            {{ $t("modelSelect.modelName") }}
          </h2>
        </div>

        <div class="mt-4">
          <div
            v-if="showDeepThinking"
            class="grid grid-cols-[1fr_auto] items-center gap-1"
          >
            <label
              class="relative truncate text-primary-100 font-RobotoSlab"
            ><span class="text-xs">- </span>{{ $t("modelSelect.deepThinking") }}</label>
            <el-switch
              v-model="deepThinking"
              size="small"
              :style="bindSwitchStyle"
            />
          </div>
          <div
            v-for="model in usableTools"
            :key="model.id"
            class="grid grid-cols-[1fr_auto] items-center gap-1"
          >
            <label
              class="relative truncate text-primary-100 font-RobotoSlab"
              :for="model.id.toString()"
            ><span class="text-xs">- </span>{{ model.name }}</label>
            <el-switch
              v-model="model.isActive"
              size="small"
              :style="bindSwitchStyle"
            />
          </div>
        </div>
      </div>
    </Transition>
  </template>
  <template v-else>
    <div
      ref="modelSelectRef"
      class="absolute z-10 w-55 rounded-2.5 bg-model-select-bg p-lg text-3.5 shadow-[0_0.31rem_1.25rem_0] shadow-#00000012 lt-md:(relative w-full)"
      :style="
        position ? { top: `${position.top}px`, left: `${position.left}px` } : {}
      "
    >
      <div>
        <h2 class="text-primary-300 font-600">
          {{ $t("modelSelect.modelName") }}
        </h2>
      </div>
      <div class="mt-4 space-y-4">
        <div
          v-if="showDeepThinking"
          class="grid grid-cols-[1fr_auto] items-center gap-1"
        >
          <label
            class="relative truncate text-primary-100 font-RobotoSlab"
          ><span class="text-xs">- </span>{{ $t("modelSelect.deepThinking") }}</label>
          <el-switch
            v-model="deepThinking"
            size="small"
            :style="bindSwitchStyle"
          />
        </div>
        <div
          v-for="model in usableTools"
          :key="model.id"
          class="grid grid-cols-[1fr_auto] items-center gap-1"
        >
          <label
            class="relative truncate text-primary-100 font-RobotoSlab"
            :for="model.id.toString()"
          ><span class="text-xs">- </span>{{ model.name }}</label>
          <el-switch
            v-model="model.isActive"
            size="small"
            :style="bindSwitchStyle"
          />
        </div>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { useTools } from '@/stores/tools'

const props = defineProps<{
  isVisible: boolean
  position?: {
    top: number
    left: number
  }
}>()

const emit = defineEmits(['update:isVisible'])

const usableTools = computed(() => {
  // DEEPSEEK V3仅支持联网搜索，且默认关闭联网搜索
  if (selectedMode.value?.name.startsWith('deepseek') && selectedMode.value?.id === 35) {
    const res = tools.value
      .filter(({ id }) => id === 'BROWSING')
      .map((tool) => {
        tool.isActive = false
        return tool
      })
    return res
  }
  return tools.value
})

const { tools, deepThinking } = useTools()
const { isPc, isClient, selectedMode } = storeToRefs(useAppStore())

const modelSelectRef = ref<HTMLDivElement | null>(null)
onClickOutside(
  modelSelectRef,
  () => {
    emit('update:isVisible', false)
  },
  {
    capture: false,
  },
)

const showDeepThinking = computed(() => {
  return selectedMode.value?.name.startsWith('claude-3-5-sonnet')
})

const bindSwitchStyle = computed(() => {
  return {
    '--el-switch-on-color': '#48C154',
    '--el-switch-off-color': '#F4F4F5',
  }
})
</script>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-25%);
  opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  transform: translateX(0);
  opacity: 1;
}

:deep() .el-switch--small .el-switch__core {
  width: 22px;
  min-width: 22px;
  height: 12px;
}

:deep() .el-switch--small .el-switch__core .el-switch__action {
  width: 7px;
  height: 7px;
}

:deep() .el-switch--small.is-checked .el-switch__core .el-switch__action {
  left: 11px;
}
</style>
