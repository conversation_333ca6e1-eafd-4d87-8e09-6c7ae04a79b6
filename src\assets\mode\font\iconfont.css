@font-face {
  font-family: "iconfont"; /* Project id 4499433 */
  src: url('iconfont.woff2?t=1713187855777') format('woff2'),
       url('iconfont.woff?t=1713187855777') format('woff');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Mixtral:before {
  content: "\e60c";
}

.icon-OpenAI:before {
  content: "\e608";
}

.icon-Gemini:before {
  content: "\e607";
}

.icon-Voice:before {
  content: "\e609";
}

.icon-Claude:before {
  content: "\e60a";
}

.icon-Labs:before {
  content: "\e60b";
}

