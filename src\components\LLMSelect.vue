<template>
  <div ref="selectRef" class="relative">
    <div
      class="flex-y-c cursor-pointer select-none font-Lexend" :class="{ '!cursor-default': !canClick }"
      @click="changeSelectPanelVis"
    >
      <div class="flex-c text-18px" :class="modelSelectInfo.providerNameIcon"></div>
      <div class="llm-namespace ml-5px flex-c text-14px text-model-display-text font-Lexend space-x-7px">
        <template v-if="!isMobileInHomeTop">
          <span>{{ modelSelectInfo.serviceProviderName || '-' }}</span>
          <span class="text-#A8AFBF">/</span>
        </template>
        <span v-if="modelSelectInfo.serviceProviderName?.toLocaleLowerCase() !== 'voice'" class="llm-second-level">
          {{ modelSelectInfo.modelName || '-' }}
        </span>
        <div v-else class="divide-x divide-model-display-text">
          <span v-for="(item, index) in modelSelectInfo.modelName" :key="index" class="px-6px">
            {{ item }}
          </span>
        </div>
        <div
          v-if=" modelSelectInfo.maxToken !== '0' && modelSelectInfo.serviceProviderName !== 'VISION'"
          class="flex-y-c rounded-4px px-4px text-12px text-#A8B0C0 font-[var(--font-family)] font-Lexend"
          bg="#EDF0F5 dark:#363636"
        >
          {{ modelSelectInfo.maxToken }}
        </div>
        <el-popover
          placement="bottom-start"
          :width="popoverWidth"
          trigger="hover"
          :content="t('modelSelect.remainingCalls')"
          :show-arrow="false"
        >
          <template #reference>
            <div
              v-if="remainCount >= 0 && inHomeTop"
              class="flex-y-c rounded-4px px-4px text-12px text-#A8B0C0 font-[var(--font-family)] font-Lexend"
              bg="#EDF0F5 dark:#363636"
            >
              {{ remainCount >= 9999 ? '∞' : remainCount }}
            </div>
          </template>
        </el-popover>

        <i
          v-if="canClick" class="i-ri-arrow-down-s-line text-#CBD5E1 transition-cusbezier-300"
          :class="[LLMSelectPanelVis && 'rotate-180']"
        />
      </div>
    </div>

    <Transition name="down" mode="out-in">
      <LLMSelectPanel
        v-show="LLMSelectPanelVis" :llm-panel-width="LLMSelectPanelWidth"
        position="absolute z-100 left-0 top-[calc(100%+20px)] "
        class="lt-md:(top-40px -left-6) !lt-md:shadow-none"
        :class="[isMobileInHomeTop && 'fixed ']"
        :panel-in-home-top="inHomeTop"
        @close-panel="LLMSelectPanelVis = false"
      />
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { mixpanel } from '@/config/mixpanel'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { subtract } from 'lodash'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  canClick: {
    type: Boolean,
    default: true,
  },
  panelWidth: {
    type: String,
  },
  inHomeTop: {
    type: Boolean,
    default: false,
  },
})
const { modelSelectInfo, isPc } = storeToRefs(useAppStore())
const { userInfoV2 } = storeToRefs(useUserStore())

const remainCount = ref(0) // 剩余调用次数

// 监听模型名称和使用次数的变化
watch(
  [() => modelSelectInfo.value.modelName, () => userInfoV2.value?.modelUsed],
  ([modelName, modelUsed]) => {
    if (!modelUsed || !modelName) {
      remainCount.value = 0
      return
    }

    for (const category in modelUsed) {
      const models = modelUsed[category]
      const model = models.find(m => m.name === modelName)
      if (model) {
        // 如果模型本身限制次数大于等于9999，则显示9999(无限制)，否则显示剩余次数
        remainCount.value = model.limit >= 9999 ? 9999 : subtract(model.limit, model.used)
        return
      }
    }
    remainCount.value = 0
  },
  { immediate: true, deep: true },
)
const { getUserInfoV2 } = useUserInfo()
onMounted(() => {
  getUserInfoV2()
})

const LLMSelectPanelWidth = computed(() => {
  if (isPc.value) {
    return '0'
  }
  else {
    return `${props.panelWidth}`
  }
})

// 是移动端且在首页顶部
const isMobileInHomeTop = computed(() => {
  return !isPc.value && props.inHomeTop
})

const LLMSelectPanelVis = ref(false)
const selectRef = ref(null)

onClickOutside(selectRef, () => LLMSelectPanelVis.value = false)
function changeSelectPanelVis() {
  if (!props.canClick) {
    return
  }
  LLMSelectPanelVis.value = !LLMSelectPanelVis.value

  if (LLMSelectPanelVis.value) {
    mixpanel.openSelectModelPanel()
  }
}

// watch isPc，如果isPc由true变为false，关闭下拉框
watch(isPc, (newVal, oldVal) => {
  if (oldVal && !newVal) {
    LLMSelectPanelVis.value = false
  }
})

const { t, locale } = useI18n()

const popoverWidth = computed(() => {
  return locale.value === 'zhCn' ? 155 : 230
})
</script>

<style lang="scss" scoped></style>
