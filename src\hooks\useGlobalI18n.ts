import i18n from '@/i18n'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

export function useGlobalI18n() {
  const { t } = useI18n({ useScope: 'global' })

  return (key: string) => {
    const translation = ref('')
    const forceUpdate = ref(0)

    translation.value = t(key)

    watch(() => t(key), (newTranslation) => {
      translation.value = newTranslation
      forceUpdate.value++
    })

    return computed(() => translation.value)
  }
}

export function getI18nTranslate(key: any) {
  return i18n.global.t(key)
}
