// 本文件用于处理表格容器的宽度和横向滚动
import { debounce } from 'lodash'

// 声明全局Window接口的扩展
declare global {
  interface Window {
    updateTableContainerMaxWidth: () => void
  }
}

// 更新所有表格容器的 max-width
if (typeof window !== 'undefined') {
  window.updateTableContainerMaxWidth = function () {
    // 使用 chat-view__section__right 作为宽度计算的基准
    const rightSection = document.querySelector('.chat-view__section__right')
    if (rightSection) {
      const maxWidth = rightSection.clientWidth
      const tableContainers = document.querySelectorAll('.ju-markdown-table-container')
      tableContainers.forEach((container) => {
        const element = container as HTMLElement
        element.style.maxWidth = `${maxWidth}px`
      })
    }
  }

  // debounce 处理窗口大小变化事件
  const debouncedUpdateMaxWidth = debounce(window.updateTableContainerMaxWidth, 100)

  // 设置MutationObserver，监听DOM变化，等待.chat-view__section__right元素出现
  window.addEventListener('DOMContentLoaded', () => {
    // 初始尝试查找元素
    let rightSection = document.querySelector('.chat-view__section__right')

    // 如果元素已经存在，直接设置观察者
    if (rightSection) {
      const resizeObserver = new ResizeObserver(debouncedUpdateMaxWidth)
      resizeObserver.observe(rightSection)
      window.updateTableContainerMaxWidth()
      return
    }

    // 如果元素不存在，使用MutationObserver等待它出现
    const bodyObserver = new MutationObserver((mutations, observer) => {
      rightSection = document.querySelector('.chat-view__section__right')
      if (rightSection) {
        // 元素存在了，设置ResizeObserver
        const resizeObserver = new ResizeObserver(debouncedUpdateMaxWidth)
        resizeObserver.observe(rightSection)
        window.updateTableContainerMaxWidth()

        // 停止观察DOM变化
        observer.disconnect()
      }
    })

    // 开始观察body变化
    bodyObserver.observe(document.body, {
      childList: true,
      subtree: true,
    })
  })

  // 还需要监听表格的变化，因为表格可能是动态加载的
  window.addEventListener('DOMContentLoaded', () => {
    const contentObserver = new MutationObserver(() => {
      window.updateTableContainerMaxWidth()
    })

    // 监听内容区域的变化
    const contentArea = document.querySelector('#app') || document.body
    contentObserver.observe(contentArea, {
      childList: true,
      subtree: true,
    })
  })

  // 虽然有MutationObserver，但仍然保留窗口大小变化监听以防万一
  window.addEventListener('resize', debouncedUpdateMaxWidth)
}

// 导出空对象骗TypeScript将此文件视为模块而非全局脚本
export {}
