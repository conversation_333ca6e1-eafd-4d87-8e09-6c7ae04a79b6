<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
  },
  keyControl: {
    type: Boolean,
    default: true,
  },
  showCancel: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['update:visible'])
function cancelDialog() {
  emit('update:visible', false)
}

onMounted(() => {
  if (props.keyControl) {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        cancelDialog()
      }
    })
  }
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-show="visible" class="dialog fixed h-full w-full">
      <div class="flex items-center justify-center">
        <div class="relative">
          <slot></slot>
          <span v-if="showCancel" class="icon-close cursor-pointer" @click="cancelDialog"></span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped lang="scss">
.dialog {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(black, 0.3);
  backdrop-filter: blur(10px);
  background-size: cover;
  z-index: 1000;

  .icon-close {
    position: absolute;
    top: 0;
    right: 40px;
    font-size: 22px;
    transition: 0.3s;

    &:hover {
      transform: scale(1.2);
    }
  }

  > div {
    height: 100%;
  }
}
</style>
