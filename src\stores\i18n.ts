import { getDefualtLang } from '@/i18n'
import { defineStore } from 'pinia'

interface ILanguage {
  language: string
}

export const useLangStore = defineStore('lang', {
  state: (): ILanguage => {
    return {
      language: localStorage.getItem('localeLang') || getDefualtLang(),
    }
  },
  actions: {
    changeLang(data: 'zhCn' | 'en') {
      localStorage.setItem('localeLang', data)
      this.language = data
    },
  },
  // persist: true,
})
