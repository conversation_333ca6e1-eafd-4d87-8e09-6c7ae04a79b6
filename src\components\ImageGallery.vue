<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="img-gallery" :class="{ expand }">
      <div class="gallery-wrapper">
        <div class="gallery-box flex flex-row items-center justify-center">
          <i
            class="icon icon-download i-ri-download-line"
            @click="download"
          ></i>
          <i class="icon icon-close i-ri-close-line" @click="close"></i>
          <i
            class="icon icon-expand i-ri-notification-badge-line"
            @click="expand = !expand"
          ></i>
          <div class="gallery-box-inner">
            <i class="icon-paging prev i-ri-arrow-up-s-line" @click="prev"></i>
            <i
              class="icon-paging next i-ri-arrow-down-s-line"
              @click="next"
            ></i>
            <div ref="activeImageWrapper" class="active-image">
              <img
                ref="activeImage"
                :src="activeItem?.src"
                alt=""
                :style="imageStyle"
              />
            </div>
            <div ref="thumbnailBox" class="gallery-thumbnail no-scrollbar">
              <div class="thumbnail-wrapper">
                <div
                  v-for="(item, itemIndex) in imgs"
                  ref="thumbnails"
                  :key="`image-slider-${itemIndex}`"
                  class="thumbnail-item"
                  :class="{ active: currentIndex === itemIndex }"
                  @click="currentIndex = itemIndex"
                >
                  <img :src="item.src" :alt="item.text" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="gallery-text flex flex-col">
          <h3 class="mode-name">
            {{ modeName }}
          </h3>
          <h2 class="prompt-tip">- Prompt -</h2>
          <p class="img-text">{{ activeItem?.text }}</p>
          <div style="margin-top: 30px">
            <a href="javascript:;" class="copy-btn" @click="copyText">{{
              $t("imageGallery.copy")
            }}</a>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { downloadStaticFile } from '@/common/tools'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import copy from 'copy-to-clipboard'
import { ElNotification } from 'element-plus'

const props = defineProps({
  imgs: {
    type: Array<{ src: string, text: string }>,
    default: [],
  },
  index: { type: Number, default: 0 },
  modeName: {
    type: String,
    default: '',
  },
  visible: {
    type: Boolean,
  },
})
const emit = defineEmits(['update:visible'])
const t = useGlobalI18n()
const activeImageWrapper = ref<HTMLDivElement | null>(null)
const thumbnails = ref<HTMLDivElement[]>([])
const thumbnailBox = ref<HTMLDivElement>()
const expand = ref(false)

const currentIndex = ref(props.index)

const activeItem = computed(() => {
  return props.imgs[currentIndex.value]
})

const activeImage = ref<HTMLImageElement | null>(null)
const imageRatio = ref(1)

const imageStyle = computed(() => {
  return imageRatio.value > 1
    ? { height: '100%', width: 'auto' }
    : { width: '100%', height: 'auto' }
})

const updateImageRatio = () => {
  // 更新图片比例 每次选择图片 都让图片居于容器中间
  if (activeImage.value) {
    imageRatio.value
      = activeImage.value.naturalWidth / activeImage.value.naturalHeight
    nextTick(() => {
      if (activeImage.value && activeImageWrapper.value) {
        if (imageRatio.value > 1) {
          activeImageWrapper.value.scrollLeft
            = activeImage.value.naturalWidth / 4
        }
        else {
          activeImageWrapper.value.scrollTop
            = activeImage.value.naturalHeight / 4
        }
      }
    })
  }
}

watch(
  () => activeItem.value,
  () => {
    // activeItem 改变时，在下一个 tick 更新比例
    nextTick(updateImageRatio)
  },
)

function prev() {
  currentIndex.value--
  if (currentIndex.value <= 0) {
    currentIndex.value = props.imgs.length - 1
  }
}

function next() {
  currentIndex.value++
  if (currentIndex.value >= props.imgs.length) {
    currentIndex.value = 0
  }
}

const isVisible = computed(() => {
  return props.visible
})

watch(isVisible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      currentIndex.value = props.index
      updateImageRatio()
      scrolleThumbnail()
    })
  }
})

// watch(visible, () => {
//   if (props.visible) {
//     currentIndex.value = props.index
//   }
// })

onMounted(() => {
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      close()
    }
  })
})

function scrolleThumbnail() {
  if (thumbnails.value[currentIndex.value]) {
    const offsetTop = thumbnails.value[currentIndex.value].offsetTop
    thumbnailBox.value?.scrollTo({
      top: offsetTop,
      left: 0,
      behavior: 'smooth',
    })
  }
}

watch(currentIndex, scrolleThumbnail)

function download() {
  let fileName = ''
  let fullUrl = activeItem.value.src
  if (!fullUrl.includes('http')) {
    fullUrl = `${location.protocol}//${location.host}${fullUrl}`
  }
  if (
    activeItem.value.src.includes('base64')
    && activeItem.value.src.length > 200
  ) {
    fileName = 'img.jpg'
  }
  else {
    const exName = new URL(fullUrl).pathname.split('.')[1]
    fileName = `img.${exName}`
  }
  downloadStaticFile(fullUrl, fileName)
}

function close() {
  emit('update:visible', false)
}

function copyText() {
  copy(activeItem.value.text)
  ElNotification.success({
    title: t('imageGallery.copySuccess'),
  })
}
</script>

<style lang="scss" scope>
/* stylelint-disable font-family-no-missing-generic-family-keyword */
.img-gallery {
  position: fixed;
  z-index: 99;
  width: 100vw;
  height: 100dvh;
  left: 0;
  top: 0;
  background: black;
  animation-duration: 0.3s;
  box-sizing: border-box;
  padding-right: 0;
  transition: 0.3s;

  .thumbnail-item {
    width: 212px;
    height: 212px;
    margin-bottom: 40px;
    transition: 0.3s;
    opacity: 0.2;
    cursor: pointer;

    &:hover {
      opacity: 0.5;
    }

    &.active {
      opacity: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .gallery-text {
    width: 500px;
    color: white;
    box-sizing: border-box;
    padding: 10% 50px 0;
    position: absolute;
    top: 0;
    right: -500px;
    transition: 0.3s;

    .prompt-tip {
      text-align: center;
      font-family: Alegreya;
      margin: 20px 0;
      font-size: 16px;
    }

    .mode-name {
      font-family: Alegreya;
      text-transform: uppercase;
      font-size: 32px;
      text-align: center;
      font-weight: bold;
    }

    .img-text {
      font-size: 14px;
      color: #a8afbf;
    }

    .copy-btn {
      font-size: 13px;
      color: #fff;
      background: rgba(white, 0.2);
      border: 1px solid #a8afbf;
      padding: 0.2em 0.8em;
      line-height: 1;
      border-radius: 5px;
      transition: 0.3s;

      &:hover {
        background: rgba(white, 0.4);
      }
    }
  }

  &.expand {
    padding-right: 500px;

    .gallery-text {
      right: 0;
    }
  }

  .thumbnail-wrapper {
    margin-bottom: -40px;
  }

  .active-image {
    background: rgba(white, 0.1);
    height: 70vh;
    width: 70vh;
    overflow: auto;

    > div {
      height: 100%;
    }

    img {
      max-width: none;
      max-height: none;
      backdrop-filter: blur(40px);
    }
  }

  .gallery-thumbnail {
    position: absolute;
    min-width: 200px;
    overflow-y: auto;
    right: 0;
    top: 0;
    height: 100%;
  }

  .gallery-wrapper {
    height: 100%;
  }

  .gallery-box-inner {
    position: relative;
    padding-right: 240px;

    .icon-paging {
      position: absolute;
      color: #fff;
      font-size: 20px;
      right: 104px;
      transform: translateX(50%);
      z-index: 10;
      line-height: 1;
      background: rgba(white, 0.1);
      padding: 0 10px;
      border-radius: 10px;
      transition: 0.3s;
      cursor: pointer;

      &:hover {
        background: rgba(white, 0.3);
      }

      &.prev {
        top: -30px;
      }

      &.next {
        bottom: -30px;
      }
    }
  }

  .gallery-box {
    background: #141a23;
    padding: 0 140px;
    box-sizing: border-box;
    height: 100%;
    position: relative;
    transition: 0.3s;

    .icon {
      position: absolute;
      color: #cbd5e1;
      font-size: 18px;
      top: 40px;
      cursor: pointer;

      &-close {
        left: 40px;
        font-size: 24px;
      }

      &-expand {
        right: 40px;
      }

      &-download {
        right: 70px;
      }
    }
  }
}
</style>
