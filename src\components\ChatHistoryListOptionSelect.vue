<template>
  <div>
    <div
      v-for="option in filterOptions"
      :key="option.value"
      class="flex-y-c cursor-pointer justify-between px-13px py-5px hover:font-600"
      :class="{ 'font-600': modelValue === option.value }"
      @click="handleSelect(option.value)"
    >
      <span class="text-12px text-[var(--black)]">{{ option.label }}</span>
      <i
        v-if="modelValue === option.value"
        class="i-ri-check-fill text-16px"
      ></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const filterOptions = ref([
  { label: t('chatHistoryList.all'), value: 'all' },
  { label: t('chatHistoryList.today'), value: 'today' },
  { label: t('chatHistoryList.week'), value: 'week' },
  { label: t('chatHistoryList.month'), value: 'month' },
  { label: t('chatHistoryList.earlier'), value: 'earlier' },
])
const modelValue = defineModel<string | number>()

const handleSelect = (value: string | number) => {
  modelValue.value = value
}
</script>
