import { formatCitation, mendMarkdown } from '@herm-studio/tool.js'
import hljs from 'highlight.js'
import { Marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import XLSX from 'xlsx-js-style'
// 在文件顶部添加类型声明
// @unocss-include
import { escapeKatex, generateCodeTitleMarkup } from '@/common/common'
import markedKatex from '@/common/marked-katex-extension/index'
import { htmlEncode } from '@/common/tools'
import i18n from '@/i18n'
import { generateImageMarkup } from './imageHandler'
import { juchatsArtifacts } from './marked-juchats-artifacts'
import { artifactsTweak } from './marked-juchats-artifacts-tweak'
import { juchatsPdb } from './marked-juchats-pdb'
import { juchatsThinking } from './marked-juchats-thinking'
import 'katex/dist/katex.css'
import '@/assets/styles/notion-light-enhanced.scss'

const marked = new Marked(
  markedHighlight({
    highlight(code, lang: any) {
      let reciveCode = hljs.highlightAuto(code, [lang]).value
      if (lang === 'thinking') {
        const marked = new Marked()
        reciveCode = (marked.parse(reciveCode) as string).trim()
      }
      return `${generateCodeTitleMarkup(lang, code)}<span class="inline-block code-wrapper relative w-full"><span class="inline-block h-full w-full will-change-transform ${lang === 'smiles' ? 'min-h-60px' : ''}">${reciveCode}</span></span>`
    },
  }),
)

marked.use(
  markedKatex({
    throwOnError: false,
    strict: false,
  }),
  {
    extensions: [juchatsThinking, juchatsArtifacts, artifactsTweak, juchatsPdb],
    renderer: {
      image(href: string, title: string | null, text: string) {
        try {
          const url = new URL(href)
          if (url.origin === 'https://api.mapbox.com') {
            if (i18n.global.locale.value === 'zhCn') {
              url.searchParams.set('language', 'zh')
              url.pathname = url.pathname.replace('outdoors-v12', 'streets-zh-v1')
            }
            else {
              url.pathname = url.pathname.replace('outdoors-v12', 'streets-v12')
            }
            href = url.toString()
          }
        }
        catch { }
        return generateImageMarkup(href, title, text)
      },
      table(header: string, body: string) {
        return `
          <div class="ju-markdown-media-wrapper">
            <div class="ju-markdown-toolbar">
              <div class="ju-markdown-download-btn " onclick="window.downloadTable(this)">
                <i class="i-ju-download-in-markdown"></i>
              </div>
            </div>
            <div class="ju-markdown-table-container">
              <table>
                <thead>${header}</thead>
                <tbody>${body}</tbody>
              </table>
            </div>
          </div>
        `
      },
    },
  },
)

export const rerenderHTML = (html: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')

  // 「点击重试」按钮不加smooth-item class
  // 组件那边是使用classList.contains('retry-button')判断的，加span套一层就判断不到了
  if (doc.querySelector('.retry-button')) {
    return doc.body.innerHTML
  }

  // 如果已经包含 smooth-item 类，说明已经处理过，直接返回
  if (doc.querySelector('.smooth-item')) {
    return doc.body.innerHTML
  }

  // 定义需要跳过处理的类名列表
  const skipClassNames = ['code-wrapper', 'katex', 'juchats-thinking', 'juchats-code-wrapper', 'citation', 'juchats-pdb-btn']

  function traverseNode(node: Node) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
      // 检查父元素是否应该跳过处理
      let parent = node.parentNode as HTMLElement
      while (parent && parent !== doc.body) {
        if (parent.nodeType === Node.ELEMENT_NODE) {
          const shouldSkipParent = skipClassNames.some(className =>
            parent.classList?.contains(className),
          )
          if (shouldSkipParent) {
            return
          }
        }
        parent = parent.parentNode as HTMLElement
      }

      // 只处理纯文本节点，避免破坏HTML结构
      const textContent = node.textContent.trim()
      if (textContent && !textContent.includes('<') && !textContent.includes('>')) {
        const spanElement = doc.createElement('span')
        const words = textContent.split(/\s+/)
        spanElement.innerHTML = words.map((word) => {
          return `<span class="smooth-item">${word}</span>`
        }).join(' ')
        node.parentNode?.replaceChild(spanElement, node)
      }
    }
    else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement

      const shouldSkip = skipClassNames.some(className =>
        element.classList.contains(className),
      )

      if (shouldSkip) {
        return
      }

      Array.from(node.childNodes).forEach(traverseNode)
    }
  }

  traverseNode(doc.body)
  return doc.body.innerHTML
}

const formated = new Map<string, string>()

export const clearFormatedCache = () => {
  formated.clear()
}

export function formatMarkdown({ string, receiving }: any) {
  if (formated.has(string)) {
    return formated.get(string)
  }
  let str = string
  if (!receiving && !str.includes('``')) {
    str = marked.parse(htmlEncode(string)) as string
  }

  const mendMarkdownResult = mendMarkdown(str)
  const formatedCitation = formatCitation(mendMarkdownResult)
  const thinkTag = formatedCitation.replace(/<think>/g, '\n```thinking').replace(/<\/think>/g, '```')
  const addBreakLineInMermaid = thinkTag.replace(/```mermaid/g, '\n```mermaid')
  const addBreakLineInSmiles = addBreakLineInMermaid.replace(/```smiles/g, '\n```smiles')

  const parsedHtml = marked.parse(escapeKatex(addBreakLineInSmiles)) as string

  const parser = new DOMParser()
  const doc = parser.parseFromString(parsedHtml, 'text/html')
  doc.querySelectorAll('pre').forEach((pre) => {
    pre.classList.add('hljs')
    const code = pre.querySelector('code')
    let _lang = ''
    if (code) {
      const langClass = Array
        .from(code.classList)
        .find(c => c.startsWith('language-'))
      if (langClass) {
        _lang = langClass.substring('language-'.length)
      }
    }
  })

  const result = doc.body.innerHTML
    .replace(/<a[^>]*>.*?<\/a>/g, (match: any) => {
      if (!match.includes('target=')) {
        return match.replace('>', ' target="_new">')
      }
      else {
        return match
      }
    })

  const formatted = rerenderHTML(result)
  formated.set(str, formatted)
  return formatted
}

if (typeof window !== 'undefined') {
  window.downloadImage = async function (encodedUrl: string) {
    try {
      const { downloadImage } = await import('./imageHandler')
      await downloadImage(encodedUrl)
    }
    catch (error) {
      console.error('下载图片失败:', error)
    }
  }

  // 添加下载表格的全局函数
  window.downloadTable = function (button: HTMLElement) {
    const tableContainer = button.closest('.ju-markdown-media-wrapper')
    const table = tableContainer?.querySelector('table')
    if (!table) {
      console.error('找不到表格元素')
      return
    }

    // 获取表格数据
    const data: string[][] = []
    const rows = table.querySelectorAll('tr')

    rows.forEach((row) => {
      const rowData: string[] = []
      const cells = row.querySelectorAll('th, td')
      cells.forEach((cell) => {
        rowData.push(cell.textContent?.trim() || '')
      })
      data.push(rowData)
    })

    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.aoa_to_sheet(data)

      // 设置列宽
      const colWidths = data[0].map(cell => ({
        wch: Math.min(50, Math.max(10, cell.length * 2)),
      }))
      ws['!cols'] = colWidths

      // 设置表头样式
      const headerStyle = {
        fill: { fgColor: { rgb: '000000' } },
        font: {
          name: '阿里巴巴普惠体 2.0 45 Light',
          sz: 12,
          color: { rgb: 'FFFFFF' },
        },
      }

      // 设置全局字体
      const globalStyle = {
        font: {
          name: '阿里巴巴普惠体 2.0 45 Light',
          sz: 12,
        },
      }

      if (!ws['!ref']) {
        throw new Error('工作表没有引用范围')
      }

      // 应用表头样式
      if (data.length > 0) {
        const range = XLSX.utils.decode_range(ws['!ref'])
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C })
          if (!ws[cellAddress]) { continue }
          ws[cellAddress].s = headerStyle
        }
      }

      // 应用全局样式
      const range = XLSX.utils.decode_range(ws['!ref'])
      for (let R = range.s.r; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C })
          if (!ws[cellAddress]) {
            ws[cellAddress] = { t: 's', v: '' }
          }
          if (!ws[cellAddress].s) {
            ws[cellAddress].s = globalStyle
          }
          else {
            ws[cellAddress].s = { ...ws[cellAddress].s, ...globalStyle.font }
          }
        }
      }

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

      // 生成并下载文件
      XLSX.writeFile(wb, 'table.xlsx')
    }
    catch (error) {
      console.error('导出表格失败:', error)
    }
  }

  // 添加预览图片的全局函数
  window.previewImage = function (encodedUrl: string, encodedText: string) {
    const event = new CustomEvent('preview-image', {
      detail: {
        src: decodeURIComponent(encodedUrl),
        text: decodeURIComponent(encodedText),
      },
    })
    window.dispatchEvent(event)
  }
}
