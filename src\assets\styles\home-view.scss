// 好像没有用到这个文件
.home-view {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  top: 0;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;

  &__dragon {
    background: url('//hermosssvip.herm.tools/images/background.png') no-repeat center center fixed;
    background-size: cover;
  }

  &__galaxy {
    background: url('//hermosssvip.herm.tools/images/background_galaxy.png') no-repeat center center
      fixed;
    background-size: cover;
  }

  &__center {
    position: absolute;
    inset: 0;
    margin: auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__content {
    &__card {
      width: 1240px;
      display: flex;
      justify-content: space-between;

      > div {
        width: 280px;
        position: relative;

        img {
          &:first-child {
            transition: all 0.3s var(--ani-bezier);
            cursor: pointer;

            &:hover {
              filter: blur(3px);
            }
          }

          &:nth-child(2) {
            inset: 0;
            width: 170px;
            margin: auto;
            position: absolute;
            pointer-events: none;
          }

          &:last-child {
            position: absolute;
            bottom: 30px;
            pointer-events: none;
            left: 0;
            right: 0;
            margin: auto;
          }
        }

        &:first-child {
          img {
            &:last-child {
              width: 100px;
            }
          }
        }

        &:nth-child(2) {
          img {
            &:last-child {
              width: 70px;
            }
          }
        }

        &:nth-child(3) {
          img {
            &:last-child {
              width: 90px;
            }
          }
        }
      }
    }

    &__button {
      width: 150px;
      height: 38px;
      border: 1px solid #fff;
      color: #fff;
      transition: all 0.3s var(--ani-bezier);
      border-radius: 30px;
      margin: 20px auto;
      cursor: pointer;
      font-weight: 600;

      &:hover {
        box-shadow: 5px 20px 30px 0 #00000038;
      }
    }
  }
}
