// 本文件用于处理代码块的换行/不换行和宽度
import { debounce } from 'lodash'

// 更新所有代码块的 max-width 在 no-wrap 模式下
if (typeof window !== 'undefined') {
  window.updateCodeWrapperMaxWidth = function () {
    // 使用 chat-view__section__right 作为宽度计算的基准
    const rightSection = document.querySelector('.chat-view__section__right')
    if (rightSection) {
      const maxWidth = rightSection.clientWidth - 32
      const codeWrappers = document.querySelectorAll('.hljs.code-nowrap .code-wrapper')
      codeWrappers.forEach((wrapper) => {
        const element = wrapper as HTMLElement
        element.style.maxWidth = `${maxWidth}px`
      })
    }
  }

  // debounce 处理窗口大小变化事件
  const debouncedUpdateMaxWidth = debounce(window.updateCodeWrapperMaxWidth, 100)

  // 设置MutationObserver，监听DOM变化，等待.chat-view__section__right元素出现
  window.addEventListener('DOMContentLoaded', () => {
    // 初始尝试查找元素
    let rightSection = document.querySelector('.chat-view__section__right')

    // 如果元素已经存在，直接设置观察者
    if (rightSection) {
      const resizeObserver = new ResizeObserver(debouncedUpdateMaxWidth)
      resizeObserver.observe(rightSection)
      window.updateCodeWrapperMaxWidth()
      return
    }

    // 如果元素不存在，使用MutationObserver等待它出现
    const bodyObserver = new MutationObserver((mutations, observer) => {
      rightSection = document.querySelector('.chat-view__section__right')
      if (rightSection) {
        // 元素存在了，设置ResizeObserver
        const resizeObserver = new ResizeObserver(debouncedUpdateMaxWidth)
        resizeObserver.observe(rightSection)
        window.updateCodeWrapperMaxWidth()

        // 停止观察DOM变化
        observer.disconnect()
      }
    })

    // 开始观察body变化
    bodyObserver.observe(document.body, {
      childList: true,
      subtree: true,
    })
  })

  // 虽然有MutationObserver，但仍然保留窗口大小变化监听以防万一
  window.addEventListener('resize', debouncedUpdateMaxWidth)
}

// 导出空对象骗TypeScript将此文件视为模块而非全局脚本
export {}
