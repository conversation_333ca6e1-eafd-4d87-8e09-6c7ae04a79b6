// 从monaco-editor导入类型
import type { Environment } from 'monaco-editor'
// Monaco编辑器配置
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'
// Import HTML language support
import 'monaco-editor/esm/vs/basic-languages/html/html.contribution'
// Import CSS language support (often needed for HTML highlighting)
import 'monaco-editor/esm/vs/basic-languages/css/css.contribution'
// Import JavaScript language support (often used within HTML)
import 'monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution'
// Import Markdown language support
import 'monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution'

// Import Find functionality
import 'monaco-editor/esm/vs/editor/contrib/find/browser/findController'

// 扩展Window接口，添加Monaco环境配置
declare global {
  interface Window {
    MonacoEnvironment?: Environment
  }
}

// 创建Worker实例的安全工厂函数
function createEditorWorker(workerCtor: new () => Worker): Worker {
  return Reflect.construct(workerCtor, [])
}

// 配置Monaco环境
export function setupMonacoEnvironment(): void {
  if (window.MonacoEnvironment) {
    return // 避免重复配置
  }

  window.MonacoEnvironment = {
    getWorker: (moduleId: string, label: string): Worker => {
      if (label === 'json') {
        return createEditorWorker(jsonWorker)
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return createEditorWorker(cssWorker)
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return createEditorWorker(htmlWorker)
      }
      if (label === 'typescript' || label === 'javascript') {
        return createEditorWorker(tsWorker)
      }
      return createEditorWorker(editorWorker)
    },
  }
}

// 自动执行配置（仅在浏览器环境下）
if (typeof window !== 'undefined') {
  setupMonacoEnvironment()
}
