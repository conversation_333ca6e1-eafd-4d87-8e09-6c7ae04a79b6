import type { VNode } from 'vue'

declare module 'acorn-jsx-walk'
declare module 'vue-virtual-scroller'
interface Window {
  Intercom: any
  pakeToast: any
  __TAURI__: any
  toggleCodeWrap: (element: HTMLElement) => void
  updateCodeWrapperMaxWidth: () => void
  downloadImage: (encodedUrl: string) => Promise<void>
  downloadTable: (button: HTMLElement) => void
  previewImage: (encodedUrl: string, encodedText: string) => void
}
// declare module 'snabby'
interface JSX {
  Element: VNode
}

type chrome = import('@types/chrome').chrome
