interface UseSpeechRecognitionOptions {
  onSilence?: (blob: Blob) => void
}

export const useAudioWorkletRecorder = ({
  onSilence,
}: UseSpeechRecognitionOptions = {}) => {
  const status = ref<'inactive' | 'recording' | 'paused'>('inactive')
  const recorder = ref<MediaRecorder | null>(null)

  const start = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      recorder.value = mediaRecorder

      const audioChunks: BlobPart[] = []
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data)
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/mpeg' })
        onSilence?.(audioBlob)
      }

      mediaRecorder.start()
      status.value = 'recording'
    }
    catch (error) {
      console.error('Failed to start recording:', error)
      status.value = 'inactive'
    }
  }

  const stop = async () => {
    recorder.value?.stop()
    status.value = 'inactive'
    recorder.value = null
  }

  const pause = () => {
    recorder.value?.stop()
    status.value = 'paused'
  }

  const resume = () => {
    recorder.value?.resume()
    status.value = 'recording'
  }

  onUnmounted(() => {
    stop()
  })

  return {
    start,
    stop,
    pause,
    resume,
    status,
  }
}
