<template>
  <div class="flex space-x-1.25rem">
    <SendAttachmentFileIcon
      v-for="(item, index) in fileIconList"
      :key="index" class="file-icon"
      :file-name="item.fileName"
      :style="{ 'animation-delay': `${index * 45}ms` }"
    />
  </div>
</template>

<script setup lang="ts">
const fileIconList = [
  { fileName: 'test.xls' },
  { fileName: 'test.doc' },
  { fileName: 'test.pdf' },
  { fileName: '' },
]
</script>

<style lang="scss" scoped>
</style>
