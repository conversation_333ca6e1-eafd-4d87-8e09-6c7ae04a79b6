import katex from 'katex'

const inlineRule
  = /^(\${1,2})(?!\$)((?:\\.|[^\\\n])*?(?:\\.|[^\\\n$]))\1(?=[\s?!.,:？！。，：]|$)/
// eslint-disable-next-line regexp/no-super-linear-backtracking
const blockRule = /^\s*(\${1,2})\n\s*?((?:\\[\s\S]|[^\\])+?)\n\s*\1(?:\s|$)/

export default function (options = {}) {
  return {
    extensions: [
      inlineKatex(options, createRenderer(options, false)),
      blockKatex(options, createRenderer(options, true)),
    ],
  }
}

function createRenderer(options, newlineAfter) {
  return token =>
    katex.renderToString(token.text, { ...options, displayMode: token.displayMode })
    + (newlineAfter ? '\n' : '')
}

function inlineKatex(options, renderer) {
  return {
    name: 'inlineKatex',
    level: 'inline',
    start(src) {
      let index
      let indexSrc = src

      while (indexSrc) {
        index = indexSrc.indexOf('$')
        if (index === -1) {
          return
        }

        if (index === 0 || indexSrc.charAt(index - 1).trim() === '') {
          const possibleKatex = indexSrc.substring(index)

          if (possibleKatex.match(inlineRule)) {
            return index
          }
        }

        indexSrc = indexSrc.substring(index + 1).replace(/^\$+/, '')
      }
    },
    tokenizer(src) {
      const match = src.match(inlineRule)
      if (match) {
        return {
          type: 'inlineKatex',
          raw: match[0],
          text: match[2].trim(),
          displayMode: match[1].length === 2,
        }
      }
    },
    renderer,
  }
}

function blockKatex(options, renderer) {
  return {
    name: 'blockKatex',
    level: 'block',
    start(src) {
      let index
      let indexSrc = src

      while (indexSrc) {
        index = indexSrc.indexOf('$$')
        if (index === -1) {
          return
        }
        if (index === 0 || indexSrc.charAt(index - 1).trim() === '') {
          const possibleKatex = indexSrc.substring(index)
          if (possibleKatex.match(blockRule)) {
            return index
          }
        }

        indexSrc = indexSrc.substring(index + 1).replace(/^\$+/, '')
      }
    },
    tokenizer(src) {
      const match = src.match(blockRule)
      if (match) {
        return {
          type: 'blockKatex',
          raw: match[0],
          text: match[2].trim(),
          displayMode: match[1].length === 2,
        }
      }
    },
    renderer,
  }
}
