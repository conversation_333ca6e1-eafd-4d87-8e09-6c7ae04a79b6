// 当根元素有 hljs-light 类时使用亮色主题
:root.hljs-light {
  .hljs {
    color: #24292e;
    background: #fff
  }
  
  .hljs-doctag,
  .hljs-keyword,
  .hljs-meta .hljs-keyword,
  .hljs-template-tag,
  .hljs-template-variable,
  .hljs-type,
  .hljs-variable.language_ {
    color: #d73a49
  }
  
  .hljs-title,
  .hljs-title.class_,
  .hljs-title.class_.inherited__,
  .hljs-title.function_ {
    color: #6f42c1
  }
  
  .hljs-attr,
  .hljs-attribute,
  .hljs-literal,
  .hljs-meta,
  .hljs-number,
  .hljs-operator,
  .hljs-selector-attr,
  .hljs-selector-class,
  .hljs-selector-id,
  .hljs-variable {
    color: #005cc5
  }
  
  .hljs-meta .hljs-string,
  .hljs-regexp,
  .hljs-string {
    color: #032f62
  }
  
  .hljs-built_in,
  .hljs-symbol {
    color: #e36209
  }
  
  .hljs-code,
  .hljs-comment,
  .hljs-formula {
    color: #6a737d
  }
  
  .hljs-name,
  .hljs-quote,
  .hljs-selector-pseudo,
  .hljs-selector-tag {
    color: #22863a
  }
  
  .hljs-subst {
    color: #24292e
  }
  
  .hljs-section {
    color: #005cc5;
    font-weight: 700
  }
  
  .hljs-bullet {
    color: #735c0f
  }
  
  .hljs-emphasis {
    color: #24292e;
    font-style: italic
  }
  
  .hljs-strong {
    color: #24292e;
    font-weight: 700
  }
  
  .hljs-addition {
    color: #22863a;
    background-color: #f0fff4
  }
  
  .hljs-deletion {
    color: #b31d28;
    background-color: #ffeef0
  }
  
}

// 当根元素有 hljs-dark 类时使用暗色主题
:root.hljs-dark {
  .hljs {
    color: #abb2bf;
    background: #282c34
  }

  .hljs-comment,
  .hljs-quote {
    color: #5c6370;
    font-style: italic
  }

  .hljs-doctag,
  .hljs-keyword,
  .hljs-formula {
    color: #c678dd
  }

  .hljs-section,
  .hljs-name,
  .hljs-selector-tag,
  .hljs-deletion,
  .hljs-subst {
    color: #e06c75
  }

  .hljs-literal {
    color: #56b6c2
  }

  .hljs-string,
  .hljs-regexp,
  .hljs-addition,
  .hljs-attribute,
  .hljs-meta .hljs-string {
    color: #98c379
  }

  .hljs-attr,
  .hljs-variable,
  .hljs-template-variable,
  .hljs-type,
  .hljs-selector-class,
  .hljs-selector-attr,
  .hljs-selector-pseudo,
  .hljs-number {
    color: #d19a66
  }

  .hljs-symbol,
  .hljs-bullet,
  .hljs-link,
  .hljs-meta,
  .hljs-selector-id,
  .hljs-title {
    color: #61aeee
  }

  .hljs-built_in,
  .hljs-title.class_,
  .hljs-class .hljs-title {
    color: #e6c07b
  }

  .hljs-emphasis {
    font-style: italic
  }

  .hljs-strong {
    font-weight: bold
  }

  .hljs-link {
    text-decoration: underline
  }
} 
