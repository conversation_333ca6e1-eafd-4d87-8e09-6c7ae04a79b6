export const svgText = {
  renderMermaid: `<svg width="14px" height="16px" viewBox="0 0 14 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>三角形</title><g id="Juchats-Code-Block" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-888, -446)" id="三角形" stroke="#4C4C4C"><path d="M895.662711,449.312006 C896.036763,449.225686 896.443746,449.282062 896.794998,449.501595 C896.988152,449.622316 897.151276,449.78544 897.271997,449.978594 L902.663502,458.605002 C902.883035,458.956254 902.939411,459.363237 902.853092,459.737289 C902.766772,460.111342 902.537756,460.452465 902.186503,460.671997 C901.948106,460.820996 901.672634,460.9 901.391505,460.9 L890.608495,460.9 C890.194282,460.9 889.819282,460.732107 889.547835,460.46066 C889.276389,460.189214 889.108495,459.814214 889.108495,459.4 C889.108495,459.11887 889.187499,458.843399 889.336498,458.605002 L894.728003,449.978594 C894.947535,449.627342 895.288658,449.398326 895.662711,449.312006 Z" transform="translate(896, 454.2) rotate(90) translate(-896, -454.2)"></path></g></g></svg>`,
  reverseMeraid: `<svg width="14px" height="16px" viewBox="0 0 14 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>三角形</title><g id="Juchats-Code-Block" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-888, -446)" fill="#4C4C4C" id="三角形" stroke="#4C4C4C"><path d="M895.662711,449.312006 C896.036763,449.225686 896.443746,449.282062 896.794998,449.501595 C896.988152,449.622316 897.151276,449.78544 897.271997,449.978594 L902.663502,458.605002 C902.883035,458.956254 902.939411,459.363237 902.853092,459.737289 C902.766772,460.111342 902.537756,460.452465 902.186503,460.671997 C901.948106,460.820996 901.672634,460.9 901.391505,460.9 L890.608495,460.9 C890.194282,460.9 889.819282,460.732107 889.547835,460.46066 C889.276389,460.189214 889.108495,459.814214 889.108495,459.4 C889.108495,459.11887 889.187499,458.843399 889.336498,458.605002 L894.728003,449.978594 C894.947535,449.627342 895.288658,449.398326 895.662711,449.312006 Z" transform="translate(896, 454.2) rotate(90) translate(-896, -454.2)"></path></g></g></svg>`,
  expand: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 6"><path d="M4.243 5.657L0 1.414 1.414 0l2.829 2.828L7.07 0l1.414 1.414z" fill="#CDCDCD"/></svg>`,
  copy: `<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><g transform="translate(1 1)" stroke="#4C4C4C" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"><rect x="4.9" y="4.9" width="9.1" height="9.1" rx="2"/><path d="M2.1 9.1h-.7A1.4 1.4 0 0 1 0 7.7V1.4A1.4 1.4 0 0 1 1.4 0h6.3a1.4 1.4 0 0 1 1.4 1.4v.7"/></g></svg>`,
  check: `<svg width="11" height="8" stroke="#00AB3C" viewBox="0 0 11 8" xmlns="http://www.w3.org/2000/svg"><path d="M10 1L3.75 7.25 1 4.5" stroke="#00AB3C" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  codeWrap: `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path fill="currentColor" d="M15 18h1.5a2.5 2.5 0 0 0 0-5H3v-2h13.5a4.5 4.5 0 1 1 0 9H15v2l-4-3l4-3zM3 4h18v2H3zm6 14v2H3v-2z"/></svg>`,
  showMore: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="currentColor" d="m12 22l-6-6l1.425-1.425L12 19.15l4.575-4.575L18 16zM7.45 9.4L6 8l6-6l6 6l-1.45 1.4L12 4.85z"/></svg>`,
}
