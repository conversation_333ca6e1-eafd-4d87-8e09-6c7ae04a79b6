@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?7lcn9q');
  src:  url('fonts/icomoon.eot?7lcn9q#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?7lcn9q') format('truetype'),
    url('fonts/icomoon.woff?7lcn9q') format('woff'),
    url('fonts/icomoon.svg?7lcn9q#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-discord:before {
  content: "\e900";
}
.icon-gift:before {
  content: "\e901";
}
.icon-github:before {
  content: "\e902";
}
.icon-sun:before {
  content: "\e903";
}
.icon-arrow:before {
  content: "\e904";
}
.icon-close:before {
  content: "\e905";
}
.icon-google:before {
  content: "\e906";
}
.icon-lock:before {
  content: "\e907";
}
.icon-mail:before {
  content: "\e908";
}
.icon-moon:before {
  content: "\e909";
}
.icon-mouse:before {
  content: "\e90a";
}
.icon-x:before {
  content: "\e90b";
}
