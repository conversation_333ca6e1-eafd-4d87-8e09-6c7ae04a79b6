/* stylelint-disable font-family-name-quotes */
/* stylelint-disable font-family-no-missing-generic-family-keyword */
.share-footer {
  padding-bottom: 140px;

  &__mobile__data {
    display: none;
  }

  &__container {
    position: fixed;
    height: 140px;
    bottom: 0;
    background: #fff;
    width: 100%;
  }

  &__content {
    height: 100%;
    max-width: 1000px;
    margin: auto;
    font-size: 12px;
    font-family: 'Roboto Slab';
  }

  &__avatar {
    width: 48px;
    height: 48px;
  }

  &__name {
    font-size: 24px;
    font-family: 'Roboto Slab';
    font-weight: 800;
    position: relative;

    &__flag {
      color: #f65134;
      font-size: 18px;
      font-family: 'Damion';
    }
  }

  &__code {
    width: 80px;
  }
}

@media (width <= 750px) {
  .share-footer {
    &__content {
      justify-content: center;
    }

    &__data {
      display: none;
    }

    &__mobile__data {
      display: flex;
    }

    &__container {
      height: 40px;
      padding: 0 20px;
    }

    &__avatar {
      width: 35px;
      height: 35px;
    }

    .avatar-frame {
      width: 25px;
    }

    &__code {
      display: none;
    }
  }
}
