<script src="https://cdn.tailwindcss.com"></script>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MiniCPM 4.0 技术报告 | Technical Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
<script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@12.19.1/dist/framer-motion.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        .gradient-red {
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.8) 0%, rgba(227, 25, 55, 0.2) 100%);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #E31937 0%, #ff6b6b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(227, 25, 55, 0.2);
        }
        
        .metric-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(227, 25, 55, 0.3);
        }
        
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .bento-item {
            border-radius: 24px;
            padding: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .bento-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section id="hero-section-001" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-red-900/20 via-black to-black"></div>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-16 scroll-reveal">
                <h1 class="text-8xl md:text-9xl font-black mb-6">
                    <span class="text-gradient">MiniCPM</span>
                    <span class="text-white/80 text-6xl md:text-7xl block">4.0</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-400 font-light">
                    Next-Generation Large Language Model
                </p>
                <p class="text-lg text-gray-500 mt-4">
                    下一代大语言模型技术突破
                </p>
            </div>
            
            <!-- Core Metrics Grid -->
            <div class="bento-grid max-w-7xl mx-auto">
                <div id="metric-card-001" class="bento-item glass-effect metric-card col-span-2">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-6xl font-black text-gradient mb-2">22%</div>
                            <div class="text-xl font-bold text-white">训练开销</div>
                            <div class="text-sm text-gray-400">Training Overhead</div>
                            <div class="text-xs text-gray-500 mt-2">vs Qwen 3 (36T vs 8T tokens)</div>
                        </div>
                        <i class="fas fa-chart-line text-4xl text-red-500"></i>
                    </div>
                </div>
                
                <div id="metric-card-002" class="bento-item glass-effect metric-card">
                    <div class="text-center">
                        <div class="text-5xl font-black text-gradient mb-2">5×</div>
                        <div class="text-lg font-bold text-white">常规加速</div>
                        <div class="text-sm text-gray-400">Regular Speedup</div>
                    </div>
                </div>
                
                <div id="metric-card-003" class="bento-item glass-effect metric-card">
                    <div class="text-center">
                        <div class="text-5xl font-black text-gradient mb-2">100×</div>
                        <div class="text-lg font-bold text-white">极限加速</div>
                        <div class="text-sm text-gray-400">Maximum Speedup</div>
                    </div>
                </div>
                
                <div id="metric-card-004" class="bento-item glass-effect metric-card">
                    <div class="text-center">
                        <div class="text-5xl font-black text-gradient mb-2">8T</div>
                        <div class="text-lg font-bold text-white">词元训练</div>
                        <div class="text-sm text-gray-400">Token Training</div>
                    </div>
                </div>
                
                <div id="metric-card-005" class="bento-item glass-effect metric-card">
                    <div class="text-center">
                        <div class="text-5xl font-black text-gradient mb-2">60%</div>
                        <div class="text-lg font-bold text-white">计算节省</div>
                        <div class="text-sm text-gray-400">Compute Savings</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Innovations -->
    <section id="innovations-section-002" class="py-24 relative">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20 scroll-reveal">
                <h2 class="text-6xl font-black mb-6">
                    <span class="text-white">技术创新</span>
                    <span class="text-gray-500 text-3xl block mt-2">Technical Innovations</span>
                </h2>
            </div>
            
            <div class="bento-grid max-w-7xl mx-auto">
                <!-- Model Architecture -->
                <div id="arch-card-001" class="bento-item glass-effect metric-card col-span-2 scroll-reveal">
                    <div class="flex items-start justify-between mb-6">
                        <div>
                            <h3 class="text-3xl font-black text-white mb-2">模型架构</h3>
                            <p class="text-gray-400">Model Architecture</p>
                        </div>
                        <i class="fas fa-sitemap text-3xl text-red-500"></i>
                    </div>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-4 bg-black/30 rounded-lg">
                            <span class="text-white font-semibold">InfLLM v2 稀疏注意力</span>
                            <span class="text-red-400 text-sm">Native Sparse Attention</span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-black/30 rounded-lg">
                            <span class="text-white font-semibold">128K 上下文长度</span>
                            <span class="text-red-400 text-sm">Context Length</span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-black/30 rounded-lg">
                            <span class="text-white font-semibold">5% 稀疏度</span>
                            <span class="text-red-400 text-sm">Sparsity Rate</span>
                        </div>
                    </div>
                </div>
                
                <!-- Inference Efficiency -->
                <div id="inference-card-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <div class="text-center mb-6">
                        <i class="fas fa-rocket text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-2xl font-black text-white mb-2">推理效率</h3>
                        <p class="text-gray-400 text-sm">Inference Efficiency</p>
                    </div>
                    <div class="space-y-3">
                        <div class="text-center">
                            <div class="text-3xl font-black text-gradient">7×</div>
                            <div class="text-xs text-gray-400">解码加速 vs Qwen3-8B</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-white">CPM.cu</div>
                            <div class="text-xs text-gray-400">推理框架</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-white">ArkInfer</div>
                            <div class="text-xs text-gray-400">部署系统</div>
                        </div>
                    </div>
                </div>
                
                <!-- Data Efficiency -->
                <div id="data-card-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <div class="text-center mb-6">
                        <i class="fas fa-database text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-2xl font-black text-white mb-2">数据高效</h3>
                        <p class="text-gray-400 text-sm">Data Efficiency</p>
                    </div>
                    <div class="space-y-3">
                        <div class="text-center p-3 bg-black/30 rounded-lg">
                            <div class="text-lg font-bold text-white">UltraClean</div>
                            <div class="text-xs text-gray-400">数据筛选</div>
                        </div>
                        <div class="text-center p-3 bg-black/30 rounded-lg">
                            <div class="text-lg font-bold text-white">UltraChat-v2</div>
                            <div class="text-xs text-gray-400">数据合成</div>
                        </div>
                    </div>
                </div>
                
                <!-- Learning Efficiency -->
                <div id="learning-card-001" class="bento-item glass-effect metric-card col-span-2 scroll-reveal">
                    <div class="flex items-start justify-between mb-6">
                        <div>
                            <h3 class="text-3xl font-black text-white mb-2">学习高效</h3>
                            <p class="text-gray-400">Learning Efficiency</p>
                        </div>
                        <i class="fas fa-brain text-3xl text-red-500"></i>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <div class="text-lg font-bold text-white mb-1">ModelTunnel v2</div>
                            <div class="text-xs text-gray-400">训练风洞</div>
                        </div>
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <div class="text-lg font-bold text-white mb-1">Chunk-wise Rollout</div>
                            <div class="text-xs text-gray-400">强化学习</div>
                        </div>
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <div class="text-lg font-bold text-white mb-1">BitCPM</div>
                            <div class="text-xs text-gray-400">三值量化</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Metrics -->
    <section id="performance-section-003" class="py-24 relative">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20 scroll-reveal">
                <h2 class="text-6xl font-black mb-6">
                    <span class="text-white">性能表现</span>
                    <span class="text-gray-500 text-3xl block mt-2">Performance Metrics</span>
                </h2>
            </div>
            
            <div class="bento-grid max-w-7xl mx-auto">
                <!-- Efficiency Evaluation -->
                <div id="efficiency-chart-001" class="bento-item glass-effect metric-card col-span-2 scroll-reveal">
                    <h3 class="text-2xl font-black text-white mb-6">效率评测 Efficiency Evaluation</h3>
                    <canvas id="efficiency-chart" width="400" height="200"></canvas>
                </div>
                
                <!-- Standard Evaluation -->
                <div id="standard-eval-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <h3 class="text-xl font-black text-white mb-4">标准评测</h3>
                    <p class="text-gray-400 text-sm mb-6">Standard Evaluation</p>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-white">准确率保持</span>
                            <span class="text-gradient font-bold">99.2%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white">FR-Spec 输出头</span>
                            <span class="text-gradient font-bold">75%↓</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white">采样时间</span>
                            <span class="text-gradient font-bold">70%↓</span>
                        </div>
                    </div>
                </div>
                
                <!-- Long Context -->
                <div id="context-eval-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <h3 class="text-xl font-black text-white mb-4">长上下文</h3>
                    <p class="text-gray-400 text-sm mb-6">Long Context</p>
                    <div class="text-center">
                        <div class="text-4xl font-black text-gradient mb-2">128K</div>
                        <div class="text-sm text-gray-400 mb-4">Context Length</div>
                        <div class="text-2xl font-bold text-white">5%</div>
                        <div class="text-xs text-gray-400">稀疏度 Sparsity</div>
                    </div>
                </div>
                
                <!-- Technical Details -->
                <div id="tech-details-001" class="bento-item glass-effect metric-card col-span-3 scroll-reveal">
                    <h3 class="text-2xl font-black text-white mb-6">技术细节 Technical Details</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <i class="fas fa-microchip text-2xl text-red-500 mb-2"></i>
                            <div class="text-sm font-bold text-white">P-GPTQ</div>
                            <div class="text-xs text-gray-400">量化技术</div>
                        </div>
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <i class="fas fa-layer-group text-2xl text-red-500 mb-2"></i>
                            <div class="text-sm font-bold text-white">FR-Spec</div>
                            <div class="text-xs text-gray-400">投机采样</div>
                        </div>
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <i class="fas fa-cloud text-2xl text-red-500 mb-2"></i>
                            <div class="text-sm font-bold text-white">ArkInfer</div>
                            <div class="text-xs text-gray-400">跨平台部署</div>
                        </div>
                        <div class="text-center p-4 bg-black/30 rounded-lg">
                            <i class="fas fa-filter text-2xl text-red-500 mb-2"></i>
                            <div class="text-sm font-bold text-white">UltraClean</div>
                            <div class="text-xs text-gray-400">数据筛选</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Applications -->
    <section id="applications-section-004" class="py-24 relative">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20 scroll-reveal">
                <h2 class="text-6xl font-black mb-6">
                    <span class="text-white">应用场景</span>
                    <span class="text-gray-500 text-3xl block mt-2">Applications</span>
                </h2>
            </div>
            
            <div class="bento-grid max-w-5xl mx-auto">
                <div id="survey-app-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <div class="text-center">
                        <i class="fas fa-file-alt text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-2xl font-black text-white mb-2">Survey 生成</h3>
                        <p class="text-gray-400 text-sm mb-4">Survey Generation</p>
                        <p class="text-gray-300 text-sm">自动化学术调研报告生成，支持多领域知识整合</p>
                    </div>
                </div>
                
                <div id="mcp-app-001" class="bento-item glass-effect metric-card scroll-reveal">
                    <div class="text-center">
                        <i class="fas fa-tools text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-2xl font-black text-white mb-2">MCP 协议</h3>
                        <p class="text-gray-400 text-sm mb-4">Tool Calling</p>
                        <p class="text-gray-300 text-sm">模型控制协议工具调用，实现智能化任务执行</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // Chart.js efficiency chart
        const ctx = document.getElementById('efficiency-chart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['训练开销', '推理加速', '计算节省', '内存优化', '能耗降低'],
                datasets: [{
                    label: 'MiniCPM 4.0',
                    data: [22, 500, 60, 45, 35],
                    backgroundColor: 'rgba(227, 25, 55, 0.8)',
                    borderColor: '#E31937',
                    borderWidth: 2,
                    borderRadius: 8
                }, {
                    label: '基准模型',
                    data: [100, 100, 100, 100, 100],
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    borderWidth: 1,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff',
                            font: {
                                family: 'Inter',
                                weight: '600'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff',
                            font: {
                                family: 'Inter'
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff',
                            font: {
                                family: 'Inter'
                            }
                        }
                    }
                }
            }
        });

        // Smooth scrolling
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.getElementById('hero-section-001');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    </script>
</body>
</html>
