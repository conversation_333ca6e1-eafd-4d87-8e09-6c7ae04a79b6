<template>
  <el-upload
    ref="uploader"
    v-model:file-list="attachments"
    :accept="calcAccept"
    class="[&_.el-upload]:rounded-full"
    :limit="ATTACHMENTS_NUMBER_LIMIT"
    :multiple="true"
    :show-file-list="false"
    :on-exceed="onExceed"
    :on-success="onSuccess"
    :on-error="onError"
    :http-request="onRequest"
    :before-upload="beforeFileUpload"
  >
    <template #trigger>
      <el-tooltip
        effect="dark"
        :content="tooltipContent"
        placement="bottom"
        :disabled="canUploadAttachment && ![...onlyImageModels, ...onlyFileModels].includes(selectedMode?.type ?? 0)"
      >
        <button
          ref="uploadButtonRef"
          text="[var(--attachment-upload-icon)] hover:#fff"
          bg="[var(--attachment-upload-button-bg)] hover:[var(--attachment-upload-button-hover-bg)]"
          class="upload-button size-30px flex-c rounded-full border-none transition-cusbezier-150"
          :class="[
            !canUploadAttachment
              && 'cursor-not-allowed opacity-50 hover:(!bg-[var(--attachment-upload-button-bg)] !text-[var(--attachment-upload-icon)])',
          ]"
          :disabled="!canUploadAttachment"
          @click="handleUploadClick"
        >
          <i class="i-ju-upload text-1rem"></i>
        </button>
      </el-tooltip>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import type { AxiosProgressEvent } from 'axios'
import type {
  UploadFile,
  UploadProgressEvent,
  UploadProps,
  UploadRawFile,
  UploadRequestOptions,
} from 'element-plus'
import type { AttachmentUploadRes } from '@/model/attachmentModel'
import axios from 'axios'
import Compressor from 'compressorjs'
import {
  ElNotification,
} from 'element-plus'
import { isNil } from 'lodash'
import service from '@/common/axios'
import { accepts, ATTACHMENTS_NUMBER_LIMIT, file_accept, image_accept } from '@/common/constant'
import { checkImageDimensions, rebuildImage, sharpCompress } from '@/common/imageHandler'
import { getFileCategory, isAcceptedFileType, notify } from '@/common/tools'
import { mixpanel } from '@/config/mixpanel'
import { MODE_TYPE } from '@/enum'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { FILE_CATEGORY } from '@/model/common'
import router from '@/router'
import { useAppStore } from '@/stores/app'

declare global {
  interface Window {
    'pdfjs-dist/build/pdf': any
  }
}

defineProps({
  isSuccess: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:isSuccess', 'success', 'audioTranscription'])

const { canUploadAttachment, selectedMode, isClient, isPc, isIOS } = storeToRefs(useAppStore())

const isFirstClick = ref(true)
const uploadButtonRef = ref<HTMLElement | null>(null)

// 使用onClickOutside钩子监听点击外部元素的事件
onClickOutside(uploadButtonRef, () => {
  // 当点击外部元素时，重置为第一次点击状态
  isFirstClick.value = true
})

// 仅支持上传图片的模型
const onlyImageModels = [
  MODE_TYPE.ARTIFACTS,
  MODE_TYPE.GEMINI_EXP,
  MODE_TYPE.GPT_O3,
  MODE_TYPE.GPT_04_MINI,
  MODE_TYPE.GPT_IMAGE,
  MODE_TYPE.FLUX_KONTEXT_PRO,
]
// 仅支持上传文件的模型
const onlyFileModels = [
  MODE_TYPE.KIMI_K2_PREVIEW,
  MODE_TYPE.CLAUDE_DYNAMICS_PREVIEW,
]
// 计算 accept 属性, Gemini 2.0 Flash, Artifacts 模型只支持上传图片, Claude Haiku 模型只支持上传文件
const calcAccept = computed(() => {
  if (onlyImageModels.includes(selectedMode.value?.type ?? 0)) {
    return image_accept.join(',')
  }
  else if (onlyFileModels.includes(selectedMode.value?.type ?? 0)) {
    return file_accept.join(',')
  }
  else {
    return accepts.join(',')
  }
})

const t = useGlobalI18n()
const { attachments, uploader, attachmentAbortControllers }
  = storeToRefs(useAppStore())
const { subscribeRemoveFileEvent } = useAppStore()
const { uploadManually, updateAttachmentParseStatus } = useAppStore()

const actionURLFile = `${import.meta.env.VITE_APP_V1_API}/v1/app/file/upload`
const actionURLGptImage = `${import.meta.env.VITE_APP_BASE_URL}/file/upload`
const actionURLImage = computed(() => {
  if (isIOS.value) {
    return `/file/uploadCFByMobile`
  }
  else {
    return `/file/uploadFileToCloudflareR2`
  }
})
const transcriptioningFile = new Map<number, AbortController>()

function getActionURL(fileCategory: FILE_CATEGORY) {
  if (selectedMode.value?.type === MODE_TYPE.GPT_IMAGE) {
    return actionURLGptImage
  }
  else if (fileCategory === FILE_CATEGORY.IMAGE) {
    return actionURLImage.value
  }
  else {
    return actionURLFile
  }
}

// 计算 tooltip 内容
const tooltipContent = computed(() => {
  // 如果上传功能被禁用，则没有tooltip内容
  if (canUploadAttachment.value && ![...onlyImageModels, ...onlyFileModels].includes(selectedMode.value?.type ?? 0)) {
    return ''
  }

  // 根据模型类型返回对应的tooltip内容
  if (onlyImageModels.includes(selectedMode.value?.type ?? 0)) {
    return t('sendAttachment.onlyImage').value
  }
  else if (onlyFileModels.includes(selectedMode.value?.type ?? 0)) {
    return t('sendAttachment.onlyFile').value
  }
  else {
    return t('sendAttachment.notSupported').value
  }
})

// 判断是否有tooltip内容需要显示
const hasTooltipContent = computed(() => {
  return !!tooltipContent.value
})

const handleUploadClick = (e: MouseEvent) => {
  if (!canUploadAttachment.value) {
    e.preventDefault()
    e.stopPropagation()
    notify.warning({
      title: t('sendAttachment.notSupported').value,
    })
    return
  }

  // 在移动端下实现特殊的点击逻辑
  if (!isPc.value && hasTooltipContent.value) {
    if (isFirstClick.value) {
      // 第一次点击，阻止上传，只显示tooltip
      e.preventDefault()
      e.stopPropagation()
      // 设置为非第一次点击，下次点击将触发上传
      isFirstClick.value = false
    }
    else {
      // 第二次点击，重置状态，允许正常上传
      isFirstClick.value = true
    }
  }
}

const beforeFileUpload: UploadProps['beforeUpload'] = async (
  rawFile: UploadRawFile,
) => {
  emit('update:isSuccess', false)
  if (!canUploadAttachment.value) {
    return false
  }

  const isImageFile = (file: any) => {
    return file.type.startsWith('image/')
  }

  // const isPdfFile = (file: any) => {
  //   return file.type === 'application/pdf'
  // }

  if (!isAcceptedFileType(rawFile, accepts)) {
    notify.error({
      title: t('sendAttachment.unsupportedFormat').value,
    })
    return false
  }

  // if (isPdfFile(rawFile)) {
  //   try {
  //     const pageCount = await getPdfPageCount(rawFile)
  //     const maxPageCount = Infinity
  //     if (pageCount > maxPageCount) {
  //       let title = t('sendAttachment.pdfTooLong').value
  //       title = title.replace('%count', maxPageCount.toString())
  //       notify.error({ title })
  //       return false
  //     }
  //   }
  //   catch {
  //     // 如果前端无法处理 PDF 文件，直接返回 true，交给后端处理
  //     return true
  //   }
  // }

  const maxSize = 50
  if (isImageFile(rawFile)) {
    if (onlyFileModels.includes(selectedMode?.value?.type ?? 0)) {
      notify.error({
        title: t('sendAttachment.onlyFile').value,
      })
      return false
    }

    // Claude限制大小
    if ([MODE_TYPE.SONNET].includes(selectedMode?.value?.type || 0) && rawFile.size / 1024 / 1024 > maxSize) {
      notify.error({
        title: t('sendAttachment.imgExceed').value,
      })
      return false
    }

    // 检查图片尺寸并处理
    /*
      因为图片本身可能写着png，实际是个jpg，或者微信复制过来的图有问题，这里除了压缩，还承担着抹平图片格式的责任
      压缩完后依旧体积大这个没关系，因为如果还是大，后端会利用Cloudflare的图片压缩功能继续压缩
     */
    const dimensions = await checkImageDimensions(rawFile)
    try {
      if (isClient.value) {
        return await sharpCompress(rawFile)
      }
      else if (isIOS.value) {
        return true
      }
      else {
        // 不是ios的设备才重绘图片
        return await rebuildImage(rawFile, dimensions)
      }
    }
    catch {
      // 尝试压缩失败，直接上传，由后端处理
      return true
    }
  }

  if (rawFile.size / 1024 / 1024 > maxSize) {
    notify.error({
      title: t('sendAttachment.exceed').value,
    })
    return false
  }

  // ios设备的上传会走到这里，直接上传原图
  return true
}

// 已经注释掉，因为暂时不需要判断页数，没必要加载，相关引入在index.html中已经注释掉了
async function getPdfPageCount(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    const pdfjsLib = window['pdfjs-dist/build/pdf']
    reader.onload = async () => {
      try {
        const arrayBuffer = reader.result as ArrayBuffer
        const pdf = await pdfjsLib.getDocument(arrayBuffer).promise
        resolve(pdf.numPages)
      }
      catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('Failed to read PDF file'))
    reader.readAsArrayBuffer(file)
  })
}

async function onRequest(options: UploadRequestOptions) {
  const fileCategory = getFileCategory(options.file.name)
  if (fileCategory === FILE_CATEGORY.AUDIO) {
    return {
      success: true,
      data: {
        success: true,
        data: {
          text: 'test',
        },
      },
    }
  }

  // 此处 URL 需要根据文件类型选择
  const url = getActionURL(fileCategory)

  const formData = new FormData()
  formData.append(options.filename, options.file, options.file.name)
  mixpanel.uploadFile(fileCategory)

  const localeLang = localStorage.getItem('localeLang') || ''
  // 对于GPT_IMAGE模式，需要设置baseURL为空，避免URL路径重复
  const config: any = {
    onUploadProgress(evt: AxiosProgressEvent) {
      const progressEvt = evt as unknown as UploadProgressEvent
      if (evt.total) {
        progressEvt.percent
          = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0
        options.onProgress(progressEvt)
      }
    },
    headers: {
      'Jtoken': getToken(),
      'Content-Type': 'multipart/form-data',
      'noninductive': true,
      'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
    },
  }

  // 如果是GPT_IMAGE模式且URL已经包含完整路径，则设置baseURL为空
  if (selectedMode.value?.type === MODE_TYPE.GPT_IMAGE) {
    config.baseURL = ''
  }

  const data = await service.post<any, any>(url, formData, config)

  return { success: !isNil(data), data }
}

function onError() {
  notify.error({ title: t('sendAttachment.uploadFail').value })
}

async function onSuccess(
  response: any,
  uploadFile: UploadFile & { isParsing?: boolean },
) {
  if (response.success) {
    emit('update:isSuccess', true)
    emit('success')
    const fileCategory = getFileCategory(uploadFile.name)
    if (fileCategory === FILE_CATEGORY.TEXT) {
      const responseData: AttachmentUploadRes = response.data
      await startParseFile(uploadFile, responseData.fileId)
    }
    else if (fileCategory === FILE_CATEGORY.AUDIO) {
      if (transcriptioningFile.has(uploadFile.uid)) {
        attachments.value = attachments.value.filter(item => item.uid !== uploadFile.uid)
        return
      }
      const formData = new FormData()
      formData.append('file', uploadFile.raw!)
      uploadFile.isParsing = true
      const abortController = new AbortController()
      transcriptioningFile.set(uploadFile.uid, abortController)
      try {
        const data = await service.post<any, any>('gpt/transcriptions', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          baseURL: '/gw/chatgpt',
          signal: abortController.signal,
        })
        uploadFile.isParsing = false
        attachments.value = attachments.value.filter(item => item.uid !== uploadFile.uid)
        emit('audioTranscription', data)
      }
      finally {
        uploadFile.isParsing = false
        transcriptioningFile.delete(uploadFile.uid)
      }
    }
  }
  else {
    if (response.msg) {
      ElNotification.error({ title: response.msg })
    }
    if ([401].includes(response.code)) {
      ElNotification.error({
        title: t('sendAttachment.loginFail').value,
      })
      localStorage.removeItem('token')
      const query: any = {
        isLogin: true,
      }
      setTimeout(() => {
        router.replace({
          name: 'Home',
          query,
        })
      }, 600)
    }
  }
}

async function startParseFile(uploadFile: UploadFile, fileId: string) {
  if (!fileId) {
    console.error('无文件ID')
    return
  }

  const controller = new AbortController()
  attachmentAbortControllers.value.set(fileId, controller)

  try {
    await parseFile(uploadFile, fileId, controller)
  }
  catch (error) {
    console.error('解析文件出错:', error)
  }
}

const removeFileEvent = subscribeRemoveFileEvent((uid) => {
})

onUnmounted(() => {
  attachmentAbortControllers.value.forEach(controller => controller.abort())
  removeFileEvent()
})

// 这里需要可以取消请求
async function parseFile(
  uploadFile: UploadFile & { isParsing?: boolean },
  fileId: string,
  abortController: AbortController,
) {
  let isCancelled = false

  abortController.signal.addEventListener('abort', () => {
    isCancelled = true
  })

  try {
    const parseURL = `${import.meta.env.VITE_APP_V1_API}/v1/app/file/parse`
    uploadFile.isParsing = true

    const parseRes = await service.post<any, { success: boolean }>(
      parseURL,
      {
        fileId,
      },
      {
        headers: {
          noninductive: true,
        },
        signal: abortController.signal,
        cancelable: true, // 添加请求取消标记
      },
    )

    if (parseRes && parseRes.success) {
      updateAttachmentParseStatus((uploadFile.response as any).data.fileId, true)
    }
  }
  catch (error) {
    if (isCancelled || axios.isCancel(error)) {
      ElNotification.success({
        title: t('sendAttachment.cancelParsing', {
          filename: 'uploadFile.name',
        }),
      })
    }
    else {
      updateAttachmentParseStatus((uploadFile.response as any).data.fileId, false)
    }
  }
  finally {
    if (!isCancelled) {
      uploadFile.isParsing = false
    }
    attachmentAbortControllers.value.delete(fileId)
  }
}

function onExceed(files: File[]) {
  uploadManually(files)
}

function getToken() {
  return localStorage.getItem('token')
}
</script>

<style lang="scss">
.el-upload:focus {
  color: inherit !important;
}
</style>
