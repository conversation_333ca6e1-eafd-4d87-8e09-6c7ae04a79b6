<script lang="tsx" setup>
import type { ThemeMode } from '@/hooks/useThemeChange'
import { getCurrentInstance } from 'vue'
import { expire, formatMobile, getFlag } from '@/common'
import services from '@/common/axios'
import { newFeatures } from '@/common/constant'
import { mixpanel } from '@/config/mixpanel'
import { LOGIN_TYPE } from '@/enum'
import { getI18nTranslate, useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useModal } from '@/hooks/useModal/useModal'
import { useModalPosition } from '@/hooks/useModalPosition'
import { THEME_MAP, useThemeChange } from '@/hooks/useThemeChange'
import { useAppStore } from '@/stores/app'
import { useLangStore } from '@/stores/i18n'
import { useModalsStore } from '@/stores/modals'
import { useUserStore } from '@/stores/user'
import EggRedemptionVue from './EggRedemption.vue'
import NewPackageBox from './NewPackageBox.vue'
import PersonalCenterLayout from './PersonalCenter/PersonalCenterLayout.vue'

const t = useGlobalI18n()
const { setTheme } = useThemeChange()
export interface Menus {
  group: string
  groupTitle: string
  children?: MenuChildren[]
}
export interface MenuChildren {
  icon: string
  name: string
  action?: any
  remark?: string
  children?: Menus[]
  class?: string
  theme?: string
}

const [fcModal] = useModal()
const router = useRouter()
const userStore = useUserStore()
const { expandSidebar, isPc, isClient } = storeToRefs(useAppStore())
const modalsStore = useModalsStore()
const { userInfoV2 } = storeToRefs(userStore)

const linkUrl = (type: string) => {
  const map = {
    'client': 'www.baidu.com',
    'landing page': 'https://www.baidu.com',
    'help': 'https://support.hermchats.com',
    'appleSilicon': 'https://download-client-app.juchats.com/download/mac/applechip',
    'appleInter': 'https://download-client-app.juchats.com/download/mac/interchip',
    'windows': 'https://download-client-app.juchats.com/download/win',
    'realtimeStatus': 'https://status.juchats.com',
  }
  const url = map[type as keyof typeof map]
  if (url) {
    window.open(url, '_blank')
  }
}
const exitFc = () => {
  mixpanel.logout()
  localStorage.removeItem('token')
  nextTick(() => {
    router.push({ name: 'Home' })
  })
}
const popoverRef = ref()

const { modalStyle } = useModalPosition()
const getModalPosition = computed(() => {
  if (isPc.value) {
    // 这里本身的样式在useModalPosition.ts中处理好了，以style的方式添加到modal上，所以不需要返回任何class了
    return ''
  }
  else {
    return 'left-0 bg-[var(--white)]'
  }
})
const closeModal = () => {
  if (!fcModal._modalInstance.value) { return }
  if (!fcModal._modalInstance.value.exposed) { return }
  fcModal._modalInstance.value!.exposed!.setProps({ visible: false })
}
const openModal = (val: boolean) => {
  try {
    if (!val) {
      fcModal._modalInstance.value!.exposed!.modalRef.style.left = '0px'
    }
    else {
      fcModal._modalInstance.value!.exposed!.modalRef.style.left = '260px'
    }
  }
  catch {

  }
}
const store = useLangStore()
const { proxy } = getCurrentInstance() as any
const handleLanguage = (value: 'zhCn' | 'en') => {
  proxy.$i18n.locale = value
  store.changeLang(value)
}
watch(() => expandSidebar.value, (val) => {
  openModal(val)
})
const ModalHeader = ({ title }: { title: string }) => {
  return (
    <div class="relative min-h-60px w-full flex-y-c">
      <div class="absolute-center flex-1">
        {title?.value || title}
      </div>
      <div class="absolute right-20px cursor-pointer" onClick={closeModal}>
        <i class="i-ju-close" />
      </div>
    </div>
  )
}
const getVariantAction = () => {
  let result = []
  result = isPc.value
    ? [{
        icon: 'i-ri-apps-2-line',
        name: t('userInfo.userCenter'),
        action: 'userCenter',
        children: [
          {
            group: 'UserCenter',
            children: [
              {
                icon: 'i-ri-pass-pending-line',
                name: t('userInfo.myProfile'),
                action: async () => {
                  mixpanel.myProfile()
                  await nextTick()
                  fcModal.show({
                    modalClass: `${getModalPosition.value}`,
                    content: () => <PersonalCenterLayout active="info" />,
                    headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.myProfile')} />,
                    style: modalStyle.value,
                  })
                  modalsStore.pushModal(fcModal)
                  unref(popoverRef).hide()
                },
              },
              {
                icon: 'i-ri-gift-line',
                name: t('userInfo.codeExchange'),
                action: async () => {
                  mixpanel.eggExchange()
                  await nextTick()
                  fcModal.show({
                    modalClass: `${getModalPosition.value}`,
                    content: () => <EggRedemptionVue close={closeModal} />,
                    headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.codeExchange')} />,
                    style: modalStyle.value,
                  })
                  modalsStore.pushModal(fcModal)
                  unref(popoverRef).hide()
                },
              },
              {
                icon: 'i-ri-exchange-2-line',
                name: t('userInfo.myInvite'),
                action: async () => {
                  mixpanel.myInvite()
                  await nextTick()
                  fcModal.show({
                    modalClass: `${getModalPosition.value}`,
                    content: () => <PersonalCenterLayout active="invite" />,
                    headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.myInvite').value} />,
                    style: modalStyle.value,
                  })
                  modalsStore.pushModal(fcModal)
                  unref(popoverRef).hide()
                },
              },
              { icon: 'i-ju-exit !text-14px', name: t('userInfo.layOut'), action: exitFc },
            ],
          },
        ],
      }, {
        icon: 'i-ri-coupon-3-line',
        name: t('userInfo.detail'),
        action: async () => {
          mixpanel.packageDetail()
          await nextTick()
          fcModal.show({
            modalClass: `${getModalPosition.value}`,
            content: () => <NewPackageBox />,
            headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.detail').value} />,
            style: modalStyle.value,
          })
          modalsStore.pushModal(fcModal)
          unref(popoverRef).hide()
        },
      }, {
        icon: 'i-ri-translate-ai text-[16px]',
        name: t('userInfo.languageSettings'),
        action: 'language',
        children: [
          {
            group: 'Language',
            children: [
              {
                icon: 'i-ju-chinese text-[16px]',
                name: t('userInfo.simplifiedChinese'),
                action: () => {
                  handleLanguage('zhCn')
                  mixpanel.selectLanguage('zh-CN')
                },
              },
              {
                icon: 'i-ju-english text-[16px]',
                name: t('userInfo.english'),
                action: () => {
                  handleLanguage('en')
                  mixpanel.selectLanguage('en')
                },
              },
            ],
          },
        ],
      }, {
        icon: 'i-ri-contrast-line',
        name: t('userInfo.themeSetting'),
        action: 'theme',
        children: [
          {
            group: 'Theme',
            children: [
              {
                icon: 'i-ju-theme-sun !text-14px',
                name: t('userInfo.light'),
                action: () => {
                  mixpanel.lightTheme()
                  handleSetTheme('light')
                },
                theme: 'light',
              },
              {
                icon: 'i-ju-theme-moon !text-14px',
                name: t('userInfo.dark'),
                action: () => {
                  mixpanel.darkTheme()
                  handleSetTheme('dark')
                },
                theme: 'dark',
              },
              {
                icon: 'i-ju-theme-system !text-14px',
                name: t('userInfo.followSystem'),
                action: () => {
                  mixpanel.followSystem()
                  handleSetTheme('auto')
                },
                theme: 'auto',
              },
            ],
          },
        ],
      }, {
        icon: 'i-ri-question-line',
        name: t('userInfo.helpCenter'),
        action: 'helpCenter',
        version: '2025-03-05 02:00:00',
        children: [
          {
            groupTitle: '',
            children: [
              // { icon: 'i-ri-book-line', name: t('userInfo.helpDocs'), action: () => linkUrl('help') },
              { icon: 'i-ri-pulse-line', name: t('userInfo.realtimeStatus'), action: () => linkUrl('realtimeStatus') },
            ],
          },
        ],
      }, {
        icon: 'i-ri-computer-line',
        name: t('userInfo.client'),
        action: 'clientDownload',
        children: [
          {
            group: 'ClientDownload',
            children: [
              {
                icon: 'i-ri-apple-fill mb-2px',
                name: t('userInfo.appleSilicon'),
                action: () => linkUrl('appleSilicon'),
              },
              {
                icon: 'i-ri-apple-fill mb-2px',
                name: t('userInfo.appleInter'),
                action: () => linkUrl('appleInter'),
              },
              {
                icon: 'i-ri-windows-fill',
                name: t('userInfo.windows'),
                action: () => linkUrl('windows'),
              },
            ],
          },
        ],
      }]
    : [
        {
          icon: 'i-ri-pass-pending-line',
          name: t('userInfo.myProfile'),
          action: async () => {
            mixpanel.myProfile()
            await nextTick()
            fcModal.show({
              modalClass: `${getModalPosition.value}`,
              content: () => <PersonalCenterLayout active="info" />,
              headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.myProfile')} />,
              style: modalStyle.value,
            })
            modalsStore.pushModal(fcModal)
            unref(popoverRef).hide()
          },
        },
        {
          icon: 'i-ri-coupon-3-line',
          name: t('userInfo.detail'),
          action: async () => {
            mixpanel.packageDetail()
            await nextTick()
            fcModal.show({
              modalClass: `${getModalPosition.value}`,
              content: () => <NewPackageBox />,
              headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.detail').value} />,
              style: modalStyle.value,
            })
            modalsStore.pushModal(fcModal)
            unref(popoverRef).hide()
          },
        },
        {
          icon: 'i-ri-gift-line',
          name: t('userInfo.codeExchange'),
          action: async () => {
            mixpanel.eggExchange()
            await nextTick()
            fcModal.show({
              modalClass: `${getModalPosition.value}`,
              content: () => <EggRedemptionVue close={closeModal} />,
              headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.codeExchange')} />,
              style: modalStyle.value,
            })
            modalsStore.pushModal(fcModal)
            unref(popoverRef).hide()
          },
        },
        {
          icon: 'i-ri-exchange-2-line',
          name: t('userInfo.myInvite'),
          action: async () => {
            mixpanel.myInvite()
            await nextTick()
            fcModal.show({
              modalClass: `${getModalPosition.value}`,
              content: () => <PersonalCenterLayout active="invite" />,
              headerContent: isPc.value ? '' : () => <ModalHeader title={t('userInfo.myInvite').value} />,
              style: modalStyle.value,
            })
            modalsStore.pushModal(fcModal)
            unref(popoverRef).hide()
          },
        },
        { icon: 'i-ju-help !text-14px', name: t('userInfo.helpCenter'), action: () => linkUrl('help') },
        { icon: 'i-ju-exit !text-14px', name: t('userInfo.layOut'), action: exitFc },

      ]
  return result
}
const getEmailVariant = () => {
  const result = [{
    group: 'Email',
    groupTitle: '',
    children: [
      { icon: 'i-ju-email', class: 'dark:text-secondary-foreground', name: userInfoV2.value.email || '-', action: () => void 0 },
    ],
  }]
  if (userInfoV2.value.loginType === LOGIN_TYPE.Github) {
    result[0].children[0].icon = 'i-ju-github'
    result[0].children[0].name = userInfoV2.value.githubUserName || '-'
  }
  else if (userInfoV2.value.loginType === LOGIN_TYPE.Google) {
    result[0].children[0].icon = 'i-ju-google'
    result[0].children[0].name = userInfoV2.value.googleEmail || '-'
  }
  return result
}
const menus = computed<Menus[]>(() => {
  const array = [
    ...getEmailVariant(),
    {
      group: 'Options',
      groupTitle: '',
      children: [

        ...getVariantAction(),
      ],
    },

  ]
  // 为客户端时隐藏套餐详情
  if (isClient.value) {
    array.forEach((element) => {
      if (element.group === 'Options') {
        element.children = element.children.filter(({ name }) => name !== t('userInfo.detail').value)
      }
    })
  }
  return array
})
const onActionTrigger = () => {
  unref(popoverRef).hide()
}

const usageStatisticRef = ref()
const updateLogRef = ref()
const commomUploadPopover = ref()
const UpdateLog = () => {
  return (
    <div class="min-w-[380px] rounded-2.5 bg-[var(--white)] p-5 shadow-[0_0_5rem_0_#00000012]">
      <dl>
        <dt class="flex items-center border-b-1 border-[#FAFAFA] pb-4">
          <span class="text-[var(--black)]">
            ✨
            {getI18nTranslate('userInfo.updateLog')}
          </span>
          <div class="grow"></div>
          <span class="i-ri-close-line cursor-pointer text-xl text-[#CBD5E1]" onClick={() => commomUploadPopover.value.hide()}></span>
        </dt>
        <dt class="px-7 py-4 space-y-2">
          {
            newFeatures.map(item => <dd class="text-3.5 text-[#575F72] before:(mr-2 content-['✨'])" v-html={item}></dd>)
          }
        </dt>
      </dl>
    </div>
  )
}

const upgrade = () => {
  const options = menus.value.find(item => item.group === 'Options')?.children
  options?.find(item => item.icon === 'i-ri-coupon-3-line')?.action()
}

// 设置用户主题样式到后端
const setUserStyle = async (style: number) => {
  try {
    await services.post('/user/setStyle', { style })
  }
  catch (error) {
    console.error('设置用户主题样式失败:', error)
  }
}

// 根据主题字符串返回对应的style值
const getStyleValueByTheme = (theme: ThemeMode): number => {
  return THEME_MAP[theme]
}

// 根据数字获取主题类型
const getThemeByStyleValue = (style: number): ThemeMode => {
  const invertedThemeMap = Object.entries(THEME_MAP).reduce((acc, [key, value]) => {
    acc[value] = key
    return acc
  }, {} as Record<number, string>) as Record<number, ThemeMode>

  return invertedThemeMap[style] || 'auto'
}

const handleSetTheme = (theme: ThemeMode) => {
  // 先调用setTheme修改UI
  setTheme(theme)

  // 调用后端API更新用户主题设置
  const styleValue = getStyleValueByTheme(theme)
  setUserStyle(styleValue)
}

// 监听用户主题设置并应用
watch(
  () => userInfoV2.value.style,
  (newStyle) => {
    if (newStyle === undefined) {
      return
    }
    const theme = getThemeByStyleValue(newStyle)
    setTheme(theme)
  },
  { immediate: true },
)

defineExpose({
  upgrade,
})

onMounted(() => {
  // 查询过期时间
  expire()
})
</script>

<template>
  <div class="w-full whitespace-nowrap px-3.75 lt-md:px-0">
    <div
      class="mb-2.5 mt-3 w-full flex justify-right text-sm transition-cusbezier-400 space-x-2"
    >
      <el-popover
        popper-class="min-w-auto! w-auto! p-0! z-99999!"
        :virtual-ref="usageStatisticRef"
        trigger="click"
        placement="right-start"
        :offset="85"
        :persistent="false"
        :hide-after="0"
        :show-arrow="false"
        virtual-triggering
      >
        <template #default>
          <UsageStatistics />
        </template>
      </el-popover>
      <el-popover
        ref="commomUploadPopover"
        popper-class="min-w-auto! w-auto! p-0! z-99999! animate-ju-wobble"
        :virtual-ref="updateLogRef"
        trigger="click"
        placement="right-start"
        :persistent="false"
        :hide-after="0"
        :show-arrow="false"
        virtual-triggering
      >
        <template #default>
          <UpdateLog />
        </template>
      </el-popover>
      <!-- <template v-if="isPc">
        <div ref="usageStatisticRef" class="flex-y-c cursor-pointer rounded-md bg-destructive px-3 py-1 text-primary space-x-1 dark:text-primary-foreground">
          <div class="h-4.38 w-4.38">
            <img class="h-full w-full" src="@/assets/images/statistics.png" />
          </div>
          <span>用量</span>
        </div>
        <div ref="updateLogRef" class="flex-y-c cursor-pointer rounded-md bg-accent px-3 py-1 text-accent-100 space-x-1 dark:text-primary-foreground">
          <div class="h-4.38 w-4.38">
            <img class="h-full w-full" src="@/assets/images/changelog.png" />
          </div>
          <span>更新</span>
        </div>
      </template> -->
    </div>
    <el-popover
      ref="popoverRef"
      popper-class="min-w-auto! w-57.5! lt-md:w-82.5! shadow-[0_5px_20px_0_#00000012]! p-0! lt-md:(left-0! shadow-none)!"
      trigger="click"
      placement="top"
      :offset="10"
      :persistent="false"
      :show-arrow="false"
    >
      <template #default>
        <UserProfile
          :menus="menus"
          @handle-action="onActionTrigger"
        ></UserProfile>
      </template>
      <template #reference>
        <div
          class="mb-2.5 w-full flex cursor-pointer items-center justify-between rounded-lg p-2.5 text-xl font-Lexend lt-md:(w-full p-6 hover:bg-[#F9F9F9] dark:hover:bg-primary-200) hover:bg-primary-200"
        >
          <div class="min-w-0 flex-y-c flex-1">
            <AvatarFrame
              :src="userInfoV2.thumbnailUrl"
              name="size-36px rounded-15px transition-cusbezier-300"
            >
            </AvatarFrame>
            <div
              class="overflow-hidden text-ellipsis px-8px text-18px text-primary-400 font-700 leading-normal font-Lexend"
            >
              {{ formatMobile(userInfoV2.name || userInfoV2.phone) }}
            </div>
          </div>
          <div class="flex-none" v-html="getFlag()"></div>
        </div>
      </template>
    </el-popover>
  </div>
</template>
