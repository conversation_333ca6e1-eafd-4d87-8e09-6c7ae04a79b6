import services from '@/common/axios'
import { unionBy } from 'lodash'

export interface SendMessageParams {
  prompt: string
  sendDirectly?: boolean
  attachments?: Array<{
    fileId: string
    fileName: string
    fileType: string
  }>
}

type SendMessage = (params: SendMessageParams) => void

export const useChatStore = defineStore('chat', {
  state: () => {
    return {
      sendMessageFn: (() => {}) as SendMessage,
      messageCounter: 0,
      dialogs: [] as ChatDialog[]
    }
  },
  getters: {
    generalDialogs(state) {
      return state.dialogs.filter((dialog) => Boolean(dialog.groupId) === false)
    }
  },
  actions: {
    registerSendMessageFn(fn: SendMessage) {
      this.sendMessageFn = fn
    },
    sendMessage(params: SendMessageParams) {
      this.messageCounter++
      this.sendMessageFn(params)
    },
    async loadDialogs(){
      const messagesData = await services.post<{}, ChatDialog[]>('/gpt/dialogs', {
        headers: {
          noninductive: true,
        },
      })
      this.dialogs = messagesData.reverse()
    },
    getShareCodeById(id: number) {
      const dialog = this.dialogs.find((dialog) => dialog.id === id)
      if (dialog) {
        return dialog.shareCode
      }
      return null
    },
    appendDialogs(dialogs: ChatDialog[]) {
      const newDialogs = unionBy(dialogs, this.dialogs, 'id')
      this.dialogs = newDialogs
    }
  },
})
