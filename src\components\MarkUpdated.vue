<template>
  <span class="mark-updated-container" @click="handleClick">
    <slot />
  </span>
  <span ref="mark" v-if="showMark" class="inline-block w-10px h-10px bg-[rgb(255,28,28)] rounded-full vertical-middle ml-10px pointer-events-none mark-updated" />
</template>

<script setup>
import { ref, computed , onMounted} from 'vue'

const mark = ref(null)

const props = defineProps({
  mark: {
    type: String,
    required: true
  },
  version: {
    type: String,
  },
  closest: {
    type: String,
    default: ''
  }
})

const showMark = computed(() => {
  const markDate = (new Date(props.version)).getTime()
  if (Date.now() > markDate) {
    return false
  }
  return Boolean(localStorage.getItem(`${props.mark}-${props.version}`)) === false
})

const handleClick = () => {
  localStorage.setItem(`${props.mark}-${props.version}`, 'yes')
  mark.value && mark.value.remove()
}

onMounted(() => {
  if (props.closest && mark.value) {
    mark.value.closest(props.closest) && mark.value.closest(props.closest).addEventListener('pointerdown', handleClick)
  }
})
</script>
