const messageDB: any = {
  db: null,
  init() {
    const request = indexedDB.open('messages', 1)
    request.onerror = function (event: any) {
      console.error('IndexedDB Error', event.target.errorCode)
    }
    request.onupgradeneeded = function (event: any) {
      const db = event.target.result
      db.createObjectStore('messages', { keyPath: 'id', autoIncrement: true })
    }
    request.onsuccess = function (event: any) {
      messageDB.db = event.target.result
    }
  },

  // 添加或更新消息到数据库
  addOrUpdateMessage({ messageID, messageContent, messageFile, uuid, thumbnailUrls, attachments }: any) {
    const transaction = messageDB.db.transaction(['messages'], 'readwrite')
    const objectStore = transaction.objectStore('messages')
    try {
      objectStore.put({ id: messageID, content: messageContent, uuid, file: JSON.stringify(messageFile), thumbnailUrls: JSON.stringify(thumbnailUrls), attachments: JSON.stringify(attachments) })
    }
    catch {
      console.error('put message error')
    }
  },

  // 根据ID获取消息内容
  getMessageById(messageID: any) {
    return new Promise((resolve, reject) => {
      const transaction = messageDB.db.transaction(['messages'], 'readonly')
      const objectStore = transaction.objectStore('messages')
      const request = objectStore.get(messageID)

      request.onsuccess = function ({ target: { result } }: any) {
        if (result) {
          resolve({
            ...result,
            file: JSON.parse(result.file || '[]'),
            thumbnailUrls: JSON.parse(result.thumbnailUrls || '[]'),
            attachments: JSON.parse(result.attachments || '[]'),
          })
        }
        else {
          resolve({ content: '', file: [] })
        }
      }

      request.onerror = function (event: any) {
        reject(new Error(`获取消息时发生错误：${event.target.errorCode}`))
      }
    })
  },

  // 删除指定ID
  deleteMessageById(messageID: any) {
    const transaction = messageDB.db.transaction(['messages'], 'readwrite')
    const objectStore = transaction.objectStore('messages')
    objectStore.delete(messageID)
  },
}

export default messageDB
