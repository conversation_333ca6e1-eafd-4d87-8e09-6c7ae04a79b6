import fs from 'node:fs'
import path from 'node:path'
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import { cssBreakPoints } from './src/config/cssBreakPoints'

function generateSvgMap() {
  const svgDir = path.join(__dirname, '/src/assets', 'svg')
  const svgFiles = fs.readdirSync(svgDir).filter(file => file.endsWith('.svg'))

  const map = svgFiles.reduce((acc, file) => {
    const key = path.parse(file).name
    const svg = fs.readFileSync(path.join(svgDir, file), 'utf-8')
    acc[key] = svg
    return acc
  }, {})
  return map
}
const svgMap = generateSvgMap()

export default defineConfig({
  shortcuts: [
    {
      'flex-c': 'flex items-center justify-center',
      'flex-y-c': 'flex items-center',
      'grid-c': 'grid place-items-center',
      'absolute-x-center': 'absolute left-0 right-0 m-inline-auto w-fit',
      'absolute-y-center': 'absolute top-0 bottom-0 m-block-auto h-fit',
      'absolute-center': 'absolute inset-0 m-auto size-fit',
    },
  ],
  rules: [
    [
      /^transition-cusbezier-(\d+)$/,
      ([, d]) => ({ transition: `all ${d}ms var(--ani-bezier)` }),
      { autocomplete: 'transition-cusbezier-300' },
    ],
    [
      /^animate-ju-([a-zA-Z]+)(?:-(\d+))?$/,
      ([, name, duration]) => ({ animation: `${name} ${duration || 300}ms var(--ani-bezier)` }),
      { autocomplete: ['animate-ju-foolishOut', 'animate-ju-foolishIn-500'] },
    ],
  ],
  presets: [
    presetUno(),
    presetWebFonts({
      fonts: {
        // Alegreya 使用 @fontsource/alegreya 进行引入，需要手动 pnpm i
        Damion: [{ name: 'Damion' }],
        // EXEPixelPerfect 无资源，使用项目中的字体文件
        Inter: [
          { name: 'Inter', weights: ['400', '500', '600', '700', '800'] },
          { name: 'PingFang SC' },
          { name: 'HarmonyOS Sans SC' },
        ],
        Kanit: [
          { name: 'Kanit', weights: ['300', '400', '500', '600', '700', '800'] },
          { name: 'Kanit', weights: ['300', '400', '500', '600', '700', '800'], italic: true },
        ],
        RobotoMono: [{ name: 'Roboto Mono', weights: ['400', '500', '600', '700', '800'] }],
        RobotoSlab: [
          { name: 'Roboto Slab', weights: ['400', '500', '600', '700', '800'] },
          { name: 'Pingfang SC', weights: ['400', '500', '600', '700', '800'] },
          { name: 'HarmonyOS Sans SC' },
        ],
        BebasNeue: [{ name: 'Bebas Neue' }],
        Lexend: [
          { name: 'Lexend', weights: ['300', '400', '500', '600', '700', '800'] },
          { name: 'Pingfang SC', weights: ['400', '500', '600', '700', '800'] },
          { name: 'HarmonyOS Sans SC' },
        ],
      },
    }),
    presetAttributify(),
    presetIcons({
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
      collections: {
        ju: {
          ...svgMap,
        },
      },
      customizations: {
        transform(svg) {
          const svgTagWithFillRegex = /<svg[^>]*\sfill="[^"]*"[^>]*>/
          const svgTagRegex = /<svg([^>]*)(?<!fill="currentColor")>/
          const fillAttribute = ' fill="currentColor"'
          let result = svg
          if (svgTagWithFillRegex.test(svg)) {
            // 如果 <svg> 标签已有 fill 属性，直接返回内容
            return svg
          }
          else if (svgTagRegex.test(svg)) {
            result = svg.replace(svgTagRegex, `<svg$1${fillAttribute}>`)
          }
          // console.log('svg', result)
          return result
        },
      },
    }),
  ],
  theme: {
    colors: {
      'primary': 'var(--p)',
      'primary-100': 'var(--p1)',
      'primary-200': 'var(--p2)',
      'primary-300': 'var(--p3)',
      'primary-400': 'var(--p4)',
      'primary-500': 'var(--p5)',
      'primary-600': 'var(--p6)',
      'primary-700': 'var(--p7)',
      'primary-800': 'var(--p8)',
      'primary-900': 'var(--p9)',
      'primary-foreground': 'var(--pf)',
      'secondary': 'var(--s)',
      'secondary-100': 'var(--s1)',
      'secondary-200': 'var(--s2)',
      'secondary-300': 'var(--s3)',
      'secondary-400': 'var(--s4)',
      'secondary-foreground': 'var(--sf)',
      'accent': 'var(--a)',
      'accent-100': 'var(--a1)',
      'accent-foreground': 'var(--af)',
      'destructive': 'var(--d)',
      'destructive-100': 'var(--d1)',
      'destructive-200': 'var(--d2)',
      'destructive-300': 'var(--d3)',
      'destructive-foreground': 'var(--df)',
      'neutral': 'var(--n)',
      'neutral-foreground': 'var(--nf)',
      'border': 'var(--border)',
      'border-100': 'var(--border1)',
      'dialog': 'var(--dialog)',
      'dialog-100': 'var(--dialog1)',
      'model-select-bg': 'var(--model-select-bg)',
      'model-select-text': 'var(--model-select-text)',
      'model-title-text': 'var(--model-title-text)',
      'model-display-text': 'var(--model-display-text)',
      'network-tab-active': 'var(--network-tab-active)',
      'network-tab-divider': 'var(--network-tab-divider)',
      'send-box-character-limit': 'var(--send-box-character-limit)',
      'control-button-group-bg': 'var(--control-button-group-bg)',
      'control-button-group-text-hover': 'var(--control-button-group-text-hover)',
      'control-button-group-text-selected': 'var(--control-button-group-text-selected)',
    },
    breakpoints: cssBreakPoints,
  },
  transformers: [
    transformerDirectives(), // 支持@apply text-center my-0 font-medium;这种写法
    transformerVariantGroup(), // <div class="hover:(bg-gray-400 font-medium) font-(light mono)"/>
  ],
  variants: [
    // 因为.scss文件里面,uno不能写"@apply !text-center"去表示 !importannt ,会报错
    // 这里为important写一种变体, 让以"I_"开头的class也是important, 兼容scss文件
    // 如：@apply I_text-center; 会被转换成css: text-align: center !important;
    (matcher) => {
      if (!matcher.startsWith('I_')) {
        return matcher
      }
      return {
        matcher: matcher.slice(2),
        body: (body) => {
          body.forEach(v => v[1] && (v[1] += ' !important'))
          return body
        },
      }
    },
  ],
  safelist: [
    'hidden',
  ],
})
