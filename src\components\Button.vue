<template>
  <button
    type="button"
    class="rounded px-4 py-2 font-bold"
    :class="[
      primary ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800',
      size === 'small' ? 'text-sm' : size === 'large' ? 'text-lg' : 'text-base',
    ]"
    :style="style"
    @click="onClick"
  >
    {{ label }}
  </button>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  /**
   * The label of the button
   */
  label: string
  /**
   * primary or secondary button
   */
  primary?: boolean
  /**
   * size of the button
   */
  size?: 'small' | 'medium' | 'large'
  /**
   * background color of the button
   */
  backgroundColor?: string

}>(), { primary: false })

const emit = defineEmits<{
  (e: 'click', id: number): void
}>()

const style = computed(() => ({
  backgroundColor: props.backgroundColor,
}))

const onClick = () => {
  emit('click', 1)
}
</script>
