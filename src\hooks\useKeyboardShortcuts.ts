import hotkeys from 'hotkeys-js'

export function useKeyboardShortcuts(shortcuts: string, cb: () => void) {
  // const keys = useMagicKeys()
  // const shortCuts = keys[shortcuts]
  // const stop = watch(shortCuts, (v) => {
  //   if (v) {
  //     cb()
  //     console.log(`${shortcuts} is pressed`)
  //   }
  // })

  // onBeforeUnmount(() => stop())
  hotkeys(shortcuts, (event) => {
    event.preventDefault()
    cb()
  })
}
