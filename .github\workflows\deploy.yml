name: <PERSON><PERSON>ts Multi-Environment Deployment

on:
  push:
    branches:
      - main
      - test
      - staging
      - preview
      - eaas-test

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy-test-or-staging:
    runs-on: ubuntu-latest
    steps:
      - name: Set timezone
        run: sudo timedatectl set-timezone Asia/Shanghai

      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install dependencies
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm install

      - name: Test
        run: pnpm test

      - name: Build test env
        if: github.ref == 'refs/heads/test'
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm run build:test

      - name: Build eaas-test env
        if: github.ref == 'refs/heads/eaas-test'
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm run build:test

      - name: Build staging env
        if: github.ref == 'refs/heads/staging'
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm run build:staging

      - name: Build preview env
        if: github.ref == 'refs/heads/preview'
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm run build:preview

      - name: Build Production env
        if: github.ref == 'refs/heads/main'
        run: NODE_OPTIONS=--max-old-space-size=4096 pnpm run build:prd

      - name: Compress build directory
        run: |
          DATE=$(date +%Y%m%d-%H%M%S)
          tar -czvf juchats-dist-${DATE}.tar.gz dist

      - name: List files
        run: ls -la

      - name: Copy compressed file to the server
        uses: appleboy/scp-action@master
        with:
          host: >-
            ${{
              github.ref == 'refs/heads/test' && secrets.TEST_SERVER_HOST ||
              github.ref == 'refs/heads/staging' && secrets.STAGING_SERVER_HOST ||
              github.ref == 'refs/heads/preview' && secrets.PRO_SERVER_HOST ||
              github.ref == 'refs/heads/main' && secrets.PRODUCTION_SERVER_HOST ||
              github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_HOST
            }}
          username: >-
            ${{
              github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_SERVER_USER ||
              secrets.SERVER_USER
            }}
          key: ${{ secrets.SSH_KEY }}
          source: 'juchats-dist-*.tar.gz'
          target: /home/<USER>

      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: >-
            ${{
              github.ref == 'refs/heads/test' && secrets.TEST_SERVER_HOST ||
              github.ref == 'refs/heads/staging' && secrets.STAGING_SERVER_HOST ||
              github.ref == 'refs/heads/preview' && secrets.PRO_SERVER_HOST ||
              github.ref == 'refs/heads/main' && secrets.PRODUCTION_SERVER_HOST ||
              github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_HOST
              }}
          username: >-
            ${{
              github.ref == 'refs/heads/eaas-test' && secrets.EAAS_TEST_SERVER_USER ||
              secrets.SERVER_USER
            }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            sudo mkdir -p /opt/bak
            cd ${{ github.ref == 'refs/heads/main' && '/opt/project/juchats-front/' || '/opt/project/juchats-ms/web/' }}
            sudo tar -czvf /opt/bak/juchats_web_archive_$(date +%Y-%m-%d-%H%M%S).tar.gz ./* && echo "File backup succeeded."
            sudo find . -mindepth 1 -delete
            sudo mv /home/<USER>/juchats-dist-*.tar.gz ./
            sudo tar -xzvf juchats-dist-*.tar.gz
            sudo rm -rf juchats-dist-*.tar.gz
            if [[ ${{ github.ref }} != 'refs/heads/main' ]]; then
              sudo rm -rf ./dist/sitemap.xml ./dist/robots.txt
            fi
            sudo mv ./dist/* ./
            sudo rm -rf dist
