<template>
  <div
    class="theme-transition"
    :class="{
      'animation-ready': isAnimationReady,
      'current-dark': currentDark,
    }"
  >
    <div id="dappled-light">
      <div id="glow"></div>
      <div id="glow-bounce"></div>
      <div class="perspective">
        <div id="leaves">
          <svg style="width: 0; height: 0; position: absolute;">
            <defs>
              <filter id="wind" x="-20%" y="-20%" width="140%" height="140%">
                <feTurbulence type="fractalNoise" numOctaves="2" seed="1">
                  <animate
                    attributeName="baseFrequency" dur="16s" keyTimes="0;0.33;0.66;1"
                    values="0.005 0.003;0.01 0.009;0.008 0.004;0.005 0.003" repeatCount="indefinite"
                  />
                </feTurbulence>
                <feDisplacementMap in="SourceGraphic">
                  <animate
                    attributeName="scale" dur="20s" keyTimes="0;0.25;0.5;0.75;1" values="45;55;75;55;45"
                    repeatCount="indefinite"
                  />
                </feDisplacementMap>
              </filter>
            </defs>
          </svg>
        </div>
        <div id="blinds">
          <div class="shutters">
            <div v-for="i in 15" :key="i" class="shutter"></div>
          </div>
          <div class="vertical">
            <div v-for="i in 2" :key="i" class="bar"></div>
          </div>
        </div>
      </div>
      <div id="progressive-blur">
        <div v-for="i in 4" :key="i"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  currentIsDark: boolean
}>()

const currentDark = ref(props.currentIsDark)
const isAnimationReady = ref(true)

watch(() => props.currentIsDark, (newVal) => {
  // 1. 先移除动画状态
  isAnimationReady.value = false

  // 2. 在下一个微任务中设置初始状态
  Promise.resolve().then(() => {
    currentDark.value = newVal
    // 3. 在下一帧添加动画状态
    requestAnimationFrame(() => {
      isAnimationReady.value = true
    })
  })
})
</script>

<style lang="scss" scoped>
.theme-transition {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;

  --day: #fffdfa;
  --evening: #fccc83;
  --dusk: #db7a2a;
  --night: #0f131c;
  --dawn: #16132b;
  --morning: #9fb3bf;
  --light: var(--day);
  --dark: var(--night);
  --shadow: #1a1917;
  --bounce-light: #f5d7a6;
  --timing-fn: cubic-bezier(0.455, 0.190, 0.000, 0.985);

  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: var(--light);
  animation-duration: 0s;
  animation-fill-mode: forwards;
  animation-timing-function: linear;
}

.theme-transition.animation-ready {
  animation-play-state: running;
  animation-duration: 1s;
  animation-name: sunrise;
}

.theme-transition.animation-ready.current-dark {
  animation-duration: 1.7s;
  animation-name: sunset;
}

.theme-transition.current-dark {
  --light: var(--night);
  --dark: var(--day);
  --shadow: #030307;
  --bounce-light: #1b293f;
}

@keyframes sunrise {
  0% {
    background-color: var(--night);
  }

  10% {
    background-color: var(--dawn);
  }

  35% {
    background-color: var(--morning);
  }

  100% {
    background-color: var(--day);
  }
}

@keyframes sunset {
  0% {
    background-color: var(--day);
  }

  30% {
    background-color: var(--evening);
  }

  60% {
    background-color: var(--dusk);
  }

  90% {
    background-color: var(--dawn);
  }

  100% {
    background-color: var(--night);
  }
}

#dappled-light {
  pointer-events: none;
  position: fixed;
  height: 100vh;
  width: 100vw;
}

#progressive-blur {
  position: absolute;
  height: 100%;
  width: 100%;
}

#progressive-blur > div {
  position: absolute;
  height: 100%;
  width: 100%;
  inset: 0;
  backdrop-filter: blur(var(--blur-amount));
  mask-image: linear-gradient(252deg, transparent, transparent var(--stop1), black var(--stop2), black);
}

#progressive-blur > div:nth-child(1) {
  --blur-amount: 6px;
  --stop1: 0%;
  --stop2: 0%;
}

#progressive-blur > div:nth-child(2) {
  --blur-amount: 12px;
  --stop1: 40%;
  --stop2: 80%;
}

#progressive-blur > div:nth-child(3) {
  --blur-amount: 48px;
  --stop1: 40%;
  --stop2: 70%;
}

#progressive-blur > div:nth-child(4) {
  --blur-amount: 96px;
  --stop1: 70%;
  --stop2: 80%;
}

#glow {
  position: absolute;
  background: linear-gradient(309deg, var(--bounce-light), var(--bounce-light) 20%, transparent);
  transition: background 1.0s var(--timing-fn);
  height: 100%;
  width: 100%;
  opacity: 0.5;
}

#glow-bounce {
  content: "";
  position: absolute;
  background: linear-gradient(355deg, var(--bounce-light) 0%, transparent 30%, transparent 100%);
  transition: background 1.0s var(--timing-fn);
  opacity: 0.5;
  height: 100%;
  width: 100%;
  bottom: 0;
}

.perspective {
  position: absolute;
  transition: transform 1.7s var(--timing-fn), opacity 4s ease;
  top: -30vh;
  right: 0;
  width: 80vw;
  height: 130vh;
  opacity: 0.07;
  background-blend-mode: darken;
  transform-origin: top right;
  transform-style: preserve-3d;
  transform: matrix3d(0.7500, -0.0625, 0.0000, 0.0008,
  0.0000, 1.0000, 0.0000, 0.0000,
  0.0000, 0.0000, 1.0000, 0.0000,
  0.0000, 0.0000, 0.0000, 1.0000);
}

.current-dark .perspective {
  opacity: 0.3;
  transform: matrix3d(0.8333, 0.0833, 0.0000, 0.0003,
  0.0000, 1.0000, 0.0000, 0.0000,
  0.0000, 0.0000, 1.0000, 0.0000,
  0.0000, 0.0000, 0.0000, 1.0000);
}

#leaves {
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  bottom: -20px;
  right: -700px;
  width: 1600px;
  height: 1400px;
  background-image: url("@/assets/images/leaves.png");
  filter: url("#wind");
  animation: billow 8s ease-in-out infinite;
}

#blinds {
  position: relative;
  width: 100%;
}

#blinds .shutter,
#blinds .bar {
  background-color: var(--shadow);
}

#blinds > .shutters {
  display: flex;
  flex-direction: column;
  align-items: end;
  gap: 60px;
  transition: gap 1.0s var(--timing-fn);
}

.current-dark #blinds > .shutters {
  gap: 20px;
}

#blinds > .vertical {
  top: 0;
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.vertical > .bar {
  width: 5px;
  height: 100%;
}

.shutter {
  width: 100%;
  height: 40px;
  transition: height 1.0s var(--timing-fn);
}

.current-dark .shutter {
  height: 80px;
}

@keyframes billow {
  0% {
    transform: perspective(400px) rotateX(0deg) rotateY(0deg) scale(1);
  }

  25% {
    transform: perspective(400px) rotateX(1deg) rotateY(2deg) scale(1.02);
  }

  50% {
    transform: perspective(400px) rotateX(-4deg) rotateY(-2deg) scale(0.97);
  }

  75% {
    transform: perspective(400px) rotateX(1deg) rotateY(-1deg) scale(1.04);
  }

  100% {
    transform: perspective(400px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}
</style>
