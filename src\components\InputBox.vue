<template>
  <div
    v-bind="$attrs"
    border="~ #EFEFEF dark:primary-500 solid"
    class="input-box mt-6 w-320px flex items-center overflow-hidden rounded-6px bg-[var(--egg-redemption-input-bg)] transition-cusbezier-300"
    :class="{
      'input-box--focus': focus,
      'input-box--error': error,
    }"
  >
    <i v-if="icon" :class="icon"></i>
    <div v-if="label" class="flex items-center whitespace-nowrap pl-20px text-sm">
      <div class="text-[var(--input-label-color)]">{{ label }}</div>
      <div class="mx-10px h-15px w-1px bg-primary-500 text-primary-500"></div>
    </div>
    <input
      :autofocus="autofocus"
      :value="modelValue"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
      type="text"
      class="outline-none"
      @input="onInput"
      @focus="onFocus"
      @blur="onFocus"
    />
    <div class="input-box__suffix whitespace-nowrap text-sm">
      <slot name="suffix"></slot>
    </div>
  </div>
  <div v-if="error" class="mt-[10px] text-right text-[12px] text-[#ff0000]">{{ error }}</div>
</template>

<script setup lang="ts">
defineProps({
  icon: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  maxlength: {
    type: Number,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  },
  autofocus: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['update:modelValue', 'input'])
const focus = ref(false)

const onInput = ({ target: { value } }: any) => {
  emits('update:modelValue', value)
  emits('input', value)
}
const onFocus = () => {
  focus.value = !focus.value
}
</script>

<style lang="scss" scoped>
.input-box {
  i {
    @apply pl-10px text-20px;
  }

  input {
    @apply py-2 px-3.5 text-3.5 lh-20px w-full;
  }
}

.input-box--focus {
  border: 1px solid #000;
}

.input-box__suffix {
  @apply pr-10px text-[#005ea9] cursor-pointer;
}

.input-box--error {
  border: 1px solid #f00;

  .input-box__suffix {
    @apply I_text-#f00;
  }
}
</style>
