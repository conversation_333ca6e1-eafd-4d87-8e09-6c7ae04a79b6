/**
 * 存储HTML预览所需的模板
 */

/**
 * 预设的CDN链接数组
 */
export const CDN_SCRIPTS = [
  'https://cdn.tailwindcss.com',
  // 将来可以添加更多CDN，例如:
  // 'https://cdnjs.cloudflare.com/ajax/libs/echarts/5.6.0/echarts.min.js',
]

/**
 * 生成所有CDN脚本标签
 * @returns 所有CDN的script标签字符串
 */
export function getAllScriptTags(): string {
  return CDN_SCRIPTS.map(src => `<script src="${src}"><\/script>`).join('\n  ')
}

/**
 * 获取包含所有CDN的完整HTML模板
 * @param content HTML内容
 * @returns 包装后的HTML
 */
export function getFullHtmlTemplate(content: string): string {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${getAllScriptTags()}
</head>
<body>
  ${content}
</body>
</html>`
}

/**
 * 获取只有head部分的HTML模板
 * @returns head标签内容
 */
export function getHeadTemplate(): string {
  return `<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${getAllScriptTags()}
</head>`
}

/**
 * 检查HTML是否包含预设的CDN脚本
 * @param html HTML内容
 * @returns 是否包含任一CDN
 */
export function containsAnyPresetCDN(html: string): boolean {
  return CDN_SCRIPTS.some((cdn) => {
    const domainPart = cdn.split('//')[1]?.split('/')[0]
    return domainPart && html.includes(domainPart)
  })
}
