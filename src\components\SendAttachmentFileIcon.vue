<script setup lang="ts">
import type { UploadStatus } from 'element-plus'
import { getFileExt, getFileShowType } from '@/common/tools'
import { FILE_TYPE } from '@/model/common'

const props = defineProps({
  fileName: {
    type: String,
    default: 'filename',
  },
  alwaysShowIcon: {
    type: Boolean,
    default: true,
  },
  uploadStatus: {
    type: String as PropType<UploadStatus>,
    default: 'ready',
  },
})
const showSuccess = ref(false)
const fileShowType = computed(() => {
  return getFileShowType(props.fileName)
})
watch(props, (newVal) => {
  if (newVal.uploadStatus === 'success') {
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 2000)
  }
})
</script>

<template>
  <div
    :class="[
      FILE_TYPE.PDF === fileShowType ? 'bg-#D21A1A shadow-[0_0.31rem_0.31rem_0_#d21a1a38]'
      : FILE_TYPE.EXCEL === fileShowType ? 'bg-#2AB411 shadow-[0_0.31rem_0.31rem_0_#2ab41138]'
        : FILE_TYPE.WORD === fileShowType ? 'bg-#1284FF shadow-[0_0.31rem_0.31rem_0_#0c56a538]'
          : FILE_TYPE.PPT === fileShowType ? 'bg-#D34626 shadow-[0_0.31rem_0.31rem_0_#D3462638]'
            : 'bg-#384A67 shadow-[0_0.31rem_0.31rem_0_#3949673d]',
    ]"
    class="relative h-[2.5em] min-w-[2.5em] w-[2.5em] flex-c overflow-hidden b-rd-[0.31rem]"
  >
    <Transition
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div v-if="(uploadStatus === 'success' || uploadStatus === 'fail' || uploadStatus === 'ready') && !showSuccess || alwaysShowIcon" class="animate-duration-0.2s!">
        <i v-if="getFileExt(fileName) === 'csv'" class="i-ju-file-attachment-csv text-[1.4rem] c-white"></i>
        <i v-else-if="getFileExt(fileName) === 'xml'" class="i-ju-file-attachment-xml text-[1.4rem] c-white"></i>
        <i v-else-if="getFileShowType(fileName) === FILE_TYPE.EXCEL" class="i-ju-file-attachment-xls text-[1.4rem] c-white"></i>
        <i v-else-if="getFileShowType(fileName) === FILE_TYPE.PPT" class="i-ju-file-attachment-ppt text-[1.4rem] c-white"></i>
        <i v-else-if="getFileShowType(fileName) === FILE_TYPE.WORD" class="i-ju-file-attachment-doc text-[1.4rem] c-white"></i>
        <i v-else-if="getFileShowType(fileName) === FILE_TYPE.PDF" class="i-ju-file-attachment-pdf text-[1.4rem] c-white"></i>
        <i v-else class="i-ju-file-attachment text-[1.4rem] c-white"></i>
      </div>
    </Transition>
    <Transition
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <slot v-if="uploadStatus === 'uploading'" name="progress"></slot>
    </Transition>
    <Transition
      enter-active-class="animate__animated animate__bounceIn"
      leave-active-class="animate__animated animate__bounceOut"
    >
      <slot v-if="showSuccess" name="success"></slot>
    </Transition>
  </div>
</template>

<style scoped lang="scss">

</style>
