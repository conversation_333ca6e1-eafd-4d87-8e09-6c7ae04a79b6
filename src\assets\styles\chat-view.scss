// 类名与ShareView组件的样式类名有重复，已在组件中@import，而不是main.scss中引入
.chat-view {
  width: 100%;
  height: calc(100dvh - var(--header-bar-height));

  @include narrow {
    height: calc(100dvh - 50px);
  }
}

.chat-view__scene {
  left: 0;
  right: 0;
  bottom: 140px;
  margin: auto;
  position: absolute;
  transition: bottom 0.3s var(--ani-bezier);
}

.chat-view__scene--active {
  bottom: 140px;
}

.chat-view__send {
  left: 0;
  right: 0;
  bottom: 25px;
  margin: auto;
  position: absolute;
  transition: all 0.3s var(--ani-bezier);

  @apply lt-md:bottom-40px lt-md:sticky;
}

.chat-view__send--active {
  // bottom: 50px;bottombottombottom
}

.chat-view__send--scene {
  box-shadow: none !important;
  background: #fff !important;
}


.chat-view__footer {
  bottom: 100px !important;
  position: absolute !important;
}

.chat-view__content .text-sm {
  font-size: 15px;
  line-height: 1.2;
}

.chat-view__content__bot {
  // background: #f9f9f9;backgroundbackgroundbackground
}

.chat-view__header {
  height: 110px;
}

.chat-view__header__button {
  margin-left: 30px;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 5px 20px 0 #00000012;
  border-radius: 24px;
  position: relative;
}

.chat-view__header__type {
  font-family: "Roboto Slab";
  font-weight: 660;
  font-size: 16px;
  padding: 0 5px;
  padding-right: 10px;
  border-right: 1.5px solid #d1d1d1;
  line-height: 15px;
  margin-right: 10px;
}

.chat-view__header__describe {
  font-family: "Roboto Slab";
  font-weight: 400 !important;
  line-height: 50px;
  font-size: 13px;
}

.chat-view__header__describe div:last-child {
  font-size: 20px;
}

.chat-view__section {
  width: 100%;
  height: 100%;
  background: var(--app-bg);
  line-height: 30px;
  overflow: auto;
  user-select: text;
}

.chat-view__section__shadow {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background: linear-gradient(to bottom, transparent, transparent, transparent, var(--sidebar-bg));
  pointer-events: none;
}

.chat-view__section__avatar {
  width: 48px;
  height: 48px;
  border-radius: 15px;
}

// 聊天内容头像与昵称之间的间距, 因为头像项目还有一个操作按钮，也需要间距，所以margin-right单独抽出来一个
.chat-view__section__avatar__margin {
  margin-right: 15px;
}

.chat-view__section__placeholder {
  height: 200px;
}

.chat-view__networking {
  position: relative;
  background: var(--network-card-bg);
  transition: all 0.3s var(--ani-bezier);
  border-radius: 5px;
  padding: 15px;
  font-size: 14px;
  border: 1px solid var(--network-card-border);
}

.chat-view__networking__label {
  transition: all 0.3s var(--ani-bezier);
  font-size: 12px;
  font-family: "Roboto Slab";
  color: #b3b3b3;
  align-items: center;
}

.chat-view__networking__label img {
  width: 15px !important;
}

.chat-view__networking i-ri-arrow-right-line {
  flex-shrink: 0;
  transition: all 0.3s var(--ani-bezier);
  opacity: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #cbd5e1;
  border-radius: 5px;
  color: #cbd5e1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-view__networking:hover {
  box-shadow: 0 5px 20px 0 #00000012;
}

.chat-view__networking:hover i-ri-arrow-right-line {
  opacity: 1;
}

.chat-view__markdown img.chat-img.hover {
  cursor: pointer;
}

.chat-view__markdown img.chat-img.hover:hover {
  box-shadow: 5px 5px 20px rgba(0, 0, 0, 20%);
}

.chat-view__markdown img:not(.net-icon,.att-img,.artifacts-icon) {
  max-width: 100%;
  max-height: 60vh;
  border-radius: 24px;
  transition: 0.3s;
  margin: 0 auto;
  cursor: pointer;
}

.chat-view__section__name {
  display: none;

  @include narrow {
    display: block;
    font-weight: bold;
    font-size: 1em;
    line-height: 36px;
  }
}

@include narrow {
  .chat-view__section__avatar {
    width: 28px;
    height: 28px;
  }

  .chat-view__section__avatar__margin {
    margin-right: 5px;
  }

  .chat-view__content__self > div,
  .chat-view__content__bot > div,
  .chat_item-style {
    padding: 20px 18px 20px 22px !important;
    width: 100%;
    word-break: break-all;
  }
}
