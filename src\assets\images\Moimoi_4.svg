<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="400px" viewBox="0 0 400 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 3</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="radialGradient-1">
            <stop stop-color="#F87171" offset="0%"></stop>
            <stop stop-color="#FDBA74" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="50%" cy="48.7180769%" fx="50%" fy="48.7180769%" r="51.2819231%" gradientTransform="translate(0.5, 0.4872), scale(0.8333, 1), rotate(90), translate(-0.5, -0.4872)" id="radialGradient-2">
            <stop stop-color="#FDE68A" offset="0%"></stop>
            <stop stop-color="#FDA4AF" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#000000" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.8016096%" y1="39.0088177%" x2="51.5618326%" y2="58.565569%" id="linearGradient-4">
            <stop stop-color="#000000" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="51.1669083%" y1="40.1097789%" x2="49.5682193%" y2="60.4912133%" id="linearGradient-5">
            <stop stop-color="#000000" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="moimoi-sticker-pack" transform="translate(-1577, -627)">
            <g id="编组" transform="translate(1577, 627)">
                <rect id="矩形" fill="url(#radialGradient-1)" fill-rule="nonzero" x="0" y="0" width="400" height="400" rx="200"></rect>
                <rect id="矩形" stroke="#000000" stroke-width="12.8" x="6.4" y="6.4" width="387.2" height="387.2" rx="193.6"></rect>
                <ellipse id="椭圆形" fill="url(#radialGradient-2)" fill-rule="nonzero" cx="180" cy="170" rx="156" ry="130"></ellipse>
                <ellipse id="椭圆形" fill="url(#linearGradient-3)" fill-rule="nonzero" transform="translate(74.35, 129.527) rotate(2) translate(-74.35, -129.527)" cx="74.35" cy="129.527" rx="40" ry="48"></ellipse>
                <path d="M112.33,130.853 C111.43,156.599 93.39,176.22 72.74,175.499 C52.09,174.778 35.47,153.947 36.37,128.201 C37.27,102.454 55.31,82.834 75.95,83.555 C96.6,84.276 113.22,105.107 112.33,130.853 Z" id="路径" stroke="#000000" stroke-width="4"></path>
                <ellipse id="椭圆形" fill="url(#linearGradient-3)" fill-rule="nonzero" transform="translate(230.21, 133.347) rotate(2) translate(-230.21, -133.347)" cx="230.21" cy="133.347" rx="40" ry="48"></ellipse>
                <path d="M268.19,134.673 C267.29,160.42 249.25,180.04 228.6,179.319 C207.96,178.598 191.33,157.767 192.23,132.021 C193.13,106.275 211.17,86.654 231.82,87.375 C252.46,88.096 269.09,108.927 268.19,134.673 Z" id="路径" stroke="#000000" stroke-width="4"></path>
                <circle id="椭圆形" fill="#FFFFFF" fill-rule="nonzero" cx="212" cy="120" r="8"></circle>
                <circle id="椭圆形" fill="#FFFFFF" fill-rule="nonzero" cx="52" cy="120" r="8"></circle>
                <ellipse id="椭圆形" fill="#000000" fill-rule="nonzero" transform="translate(157.2, 278.51) rotate(2) translate(-157.2, -278.51)" cx="157.2" cy="278.51" rx="64" ry="78"></ellipse>
                <path d="M63.11,56.826 C50.96,61.527 41.73,71.807 37.12,79.924 C34.39,84.727 28.29,86.411 23.49,83.685 C18.68,80.959 17,74.856 19.72,70.052 C25.91,59.15 38.29,44.988 55.89,38.174 C74.29,31.052 96.85,32.581 121.03,50.806 C125.44,54.13 126.32,60.4 123,64.81 C119.67,69.221 113.4,70.101 108.99,66.777 C89.8,52.314 74.46,52.432 63.11,56.826 Z" id="路径" fill="url(#linearGradient-4)"></path>
                <path d="M62.39,54.96 C49.7,59.873 40.14,70.54 35.38,78.937 C33.2,82.779 28.32,84.126 24.47,81.946 C20.63,79.765 19.28,74.882 21.46,71.039 C27.49,60.415 39.55,46.641 56.61,40.039 C74.31,33.19 96.15,34.553 119.83,52.403 C123.35,55.062 124.06,60.078 121.4,63.606 C118.74,67.135 113.72,67.839 110.2,65.18 C90.51,50.34 74.45,50.294 62.39,54.96 Z" id="路径" stroke="#000000" stroke-width="4"></path>
                <path d="M225.35,60.077 C214.96,62.497 205.69,70.701 200.45,78.01 C197.22,82.496 190.98,83.522 186.49,80.301 C182,77.079 180.98,70.831 184.2,66.345 C191.1,56.735 204.05,44.502 220.81,40.598 C229.42,38.593 238.91,38.835 248.65,42.693 C258.31,46.517 267.64,53.663 276.43,64.561 C279.9,68.859 279.23,75.154 274.93,78.623 C270.63,82.091 264.34,81.418 260.87,77.12 C253.71,68.249 247.04,63.563 241.29,61.288 C235.63,59.045 230.31,58.921 225.35,60.077 Z" id="路径" fill="url(#linearGradient-5)"></path>
                <path d="M224.89,58.129 C213.87,60.697 204.24,69.303 198.82,76.844 C196.24,80.432 191.25,81.253 187.66,78.676 C184.07,76.099 183.25,71.101 185.82,67.512 C192.56,58.13 205.14,46.301 221.26,42.546 C229.51,40.626 238.58,40.856 247.92,44.552 C257.19,48.222 266.25,55.122 274.88,65.817 C277.65,69.255 277.11,74.292 273.67,77.066 C270.24,79.841 265.2,79.303 262.42,75.864 C255.1,66.79 248.16,61.858 242.03,59.428 C235.96,57.025 230.22,56.888 224.89,58.129 Z" id="路径" stroke="#000000" stroke-width="4"></path>
            </g>
        </g>
    </g>
</svg>