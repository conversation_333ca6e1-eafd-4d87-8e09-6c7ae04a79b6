<template>
  <div class="grid grid-cols-2 size-full gap-20px font-[var(--font-family)] lt-lg:grid-cols-1">
    <div v-if="!ltLg" class="relative flex-c flex-col">
      <div class="mb-133px lt-2xl:mb-93px">
        <Loginlogo class="mb-48px" />
        <LoginTipWithLogo class="flex-c" tip="One Window, Infinite Worlds!" />
      </div>
      <LoginSteps
        @success="handleSuccess"
        @google-error="handleGoogleError"
      />
    </div>
    <div>
      <LoginSwiper />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

const { breakpoints } = useAppStore()
const ltLg = breakpoints.smaller('lg') // 小于LG尺寸
// eslint-disable-next-line no-restricted-syntax
const enum STEP {
  LOGIN,
  INVITATION,
  SUCCESS,
}

const step = ref(STEP.LOGIN)
const inviteStatus = ref('')
const googleInfo = ref({})
const giftInfo = ref({})

const handleSuccess = ({ newUser, exchangeInfo }: { newUser: boolean, exchangeInfo?: any }) => {
  if (newUser) {
    step.value = STEP.SUCCESS
    if (exchangeInfo) {
      giftInfo.value = exchangeInfo
    }
  }
}

const handleGoogleError = (data: any) => {
  googleInfo.value = data
  inviteStatus.value = 'GOOGLE'
}
</script>
