<template>
  <div
    :key="rndKey"
    class="w-500px rounded-20px bg-[var(--login-card-bg)] p-50px shadow-[0_0_40px_0_#00000012] lt-lg:(w-full shadow-none) space-y-25px"
  >
    <template v-if="currentStep === 'home'">
      <div v-if="isClient" @click="clientGoogleLogin">
        <div ref="googleRef" class="login_way_btn">
          <div class="icon-box flex-c">
            <span class="icon-google"></span>
          </div>
          <span class="login-btn-txt">{{ GoogleText }}</span>
        </div>
      </div>

      <GoogleLogin v-else :callback="chooseGoogleLogin">
        <div ref="googleRef" class="login_way_btn">
          <div class="icon-box flex-c">
            <span class="icon-google"></span>
          </div>
          <span class="login-btn-txt">{{ GoogleText }}</span>
        </div>
      </GoogleLogin>

      <div
        class="login_way_btn"
        @click="chooseGithubLogin"
      >
        <div class="icon-box flex-c">
          <span class="icon-github"></span>
        </div>
        <span class="login-btn-txt">{{ GithubText }}</span>
      </div>

      <div class="text-center text-#727272">OR</div>

      <div
        border="~ #F1F1F1 dark:none"
        class="input-wrapper h-52px flex-y-c rounded-10px shadow-[0_0_0_2px_#0000] transition-all dark:(bg-#363636)"
        :class="{ '!shadow-[0_0_0_3px_#000] dark:!shadow-[0_0_0_3px_#CDCDCD]': emailFocus }"
      >
        <el-input
          ref="emailInput"
          v-model.trim="email"
          class="input-box outline-none"
          size="large"
          placeholder="Enter your personal or work email"
          type="email"
          @focus="focusEmail"
          @blur="blurEmail"
          @keydown.enter="getEmailCode"
        />
      </div>

      <LoginConfirmButton
        :disabled="isSubmitting || !email"
        :loading="isSubmitting"
        @click="getEmailCode"
      >
        <span>Continue with email</span>
      </LoginConfirmButton>

      <p class="break-all text-12px text-#727272">
        By continuing, you agree to Hermstudio's
        <router-link to="/policy/consumer-terms" class="text-#727272 underline">Consumer Terms</router-link> and
        <router-link to="/policy/usage-policy" class="text-#727272 underline">Usage Policy</router-link>, and acknowledge their
        <router-link to="/policy/privacy-policy" class="text-#727272 underline">Privacy Policy</router-link>.
      </p>
    </template>

    <template v-if="currentStep === 'code'">
      <div class="text-#727272 dark:text-#A8AFBF">
        Have a verification code instead?
      </div>
      <div class="text-20px">
        <p>Enter the verification code you</p>
        <p>received in your email</p>
      </div>

      <div>
        {{ email }}
      </div>

      <div class="text-12px text-#727272">
        Enter verification code
      </div>

      <div>
        <VerificationCodeInput
          ref="verificationCodeInputRef"
          v-model="emailCode"
        />
      </div>

      <LoginConfirmButton
        :disabled="isSubmitting || emailCode.length !== 6"
        :loading="isSubmitting"
        @click="emailSubmit"
      >
        Verify Email Address
      </LoginConfirmButton>

      <div class="text-12px text-#727272">
        Not seeing the email in your inbox? Try
        <span class="cursor-pointer text-#727272 underline" @click="getEmailCode">sending again</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import services from '@/common/axios'
import { notify } from '@/common/tools'
import { useComposition } from '@/hooks/useComposition'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { encode } from 'base-64'
import { debounce } from 'lodash'
import { storeToRefs } from 'pinia'

const emit = defineEmits([
  'success',
  'currentStep',
  'googleError',
])
const route = useRoute()
const invitationCode = computed(() => route.query.code || route.params.code || '')

const t = useGlobalI18n()
const GoogleText = ref(t('loginWays.googleText'))
const GithubText = ref(t('loginWays.githubText'))

const emailCodeGot = ref(false)
const isSubmitting = ref(false)
const email = ref('')
const rndKey = ref(Math.random())
const googleRef = ref()
const currentStep = ref('home')
const emailCode = ref('')
const userStore = useUserStore()
const router = useRouter()
const { isClient } = storeToRefs(useAppStore())

watchEffect(() => {
  emit('currentStep', currentStep.value)
})
let delayTimer: ReturnType<typeof setTimeout> | number

const emailFocus = ref(false)

function focusEmail() {
  emailFocus.value = true
}

function blurEmail() {
  emailFocus.value = false
}

function delayTrigger() {
  return new Promise<void>((resolve) => {
    if (delayTimer) {
      clearTimeout(delayTimer)
    }
    delayTimer = setTimeout(async () => {
      resolve()
    }, 600)
  })
}

const getInfo = async () => {
  await userStore.getUserInfo()
}

const chooseGoogleLogin = async (params: any) => {
  try {
    const { token, exchangeInfo, newUser }: any = await services.post(
      '/user/third/party/regLogin',
      {
        googleCode: params.code,
        googleRedirectUri: location.origin,
        inviteCode: invitationCode.value,
        googleUserInfo: params.googleUserInfo,
      },
    )
    localStorage.setItem('token', token)
    await getInfo()
    emit('success', { newUser, exchangeInfo })
    router.replace({ name: 'Chat' })
  }
  catch (error: any) {
    if ([402].includes(error.code)) {
      emit('googleError', error.data)
    }
  }
}

const clientGoogleLogin = async () => {
  // https://developers.google.cn/identity/protocols/oauth2/javascript-implicit-flow?hl=ro#obtainingaccesstokens
  const ORIGIN = location.origin
  const GOOGLE_CLIENT_ID = import.meta.env.VITE_APP_GOOGLE_CLIENT
  const REDIRECT_URI = `${ORIGIN}/gw/chatweb/user/google/oauth/callback`
  const SCOPE = 'openid email profile'
  const STATE = encode(JSON.stringify({ from: 'LOGIN', inviteCode: invitationCode.value, Jtoken: '' }))
  const RESPONSE_TYPE = 'code'
  location.href = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=${RESPONSE_TYPE}&scope=${SCOPE}&state=${STATE}`
}

const chooseGithubLogin = () => {
  location.href = `https://github.com/login/oauth/authorize?client_id=${
    import.meta.env.VITE_APP_GITHUB_CLIENT
  }&redirect_uri=${`${location.origin}/oauth?inviteCode=${invitationCode.value}`}`
}
// .
const getCode = async () => {
  try {
    if (isSubmitting.value || !email.value) { return false }

    isSubmitting.value = true
    const res: any = await services.post('/user/email/sendCaptcha', {
      email: email.value,
      type: 1,
    }, {
      headers: { noninductive: true },
    })
    isSubmitting.value = false
    notify.info({
      title: t('loginWays.sendEmailMessage').value,
    })

    return res.code === 200
  }
  catch {
    isSubmitting.value = false
    return false
  }
}
const emailInput = ref()
const verificationCodeInputRef = ref()
const { isComposing } = useComposition(emailInput)

async function getEmailCode() {
  if (isComposing.value) {
    return
  }
  // 邮箱格式验证
  const emailRegex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i
  if (!emailRegex.test(email.value)) {
    notify.error({
      title: t('loginWays.emailFormatTip').value,
    })
    return
  }

  const success = await getCode()
  if (success) {
    emailCodeGot.value = true
    currentStep.value = 'code'
  }
}

watch(emailCode, async (newVal, oldVal) => {
  await delayTrigger()
  if (newVal !== oldVal && emailCode.value.length === 6) {
    await emailSubmit()
  }
})

const emailSubmit = debounce(async () => {
  try {
    isSubmitting.value = true
    const { token, exchangeInfo, newUser }: any = await services.post(
      '/user/email/regLogin',
      {
        email: email.value,
        code: emailCode.value,
        inviteCode: invitationCode.value,
      },
      {
        headers: {
          noninductive: true,
        },
      },
    )

    localStorage.setItem('token', token)
    await getInfo()
    emit('success', { newUser, exchangeInfo })
    isSubmitting.value = false
    router.replace({ name: 'Chat' })
  }
  catch {
    isSubmitting.value = false
  }
}, 600)

defineExpose({
  emailFocus,
  verificationCodeInputRef,
})
</script>

<style lang="scss" scoped>
.g-btn-wrapper {
  width: 100%;
}

.login_way_btn {
  @apply flex-c cursor-pointer select-none rounded-10px h-52px space-x-8px border-#F1F1F1 border-1px font-bold transition-all text-14px shadow-[0_0_0_2px_#0000] hover:(shadow-[0_0_0_3px_#000]) dark:(border-none bg-#1e1e1e hover:shadow-[0_0_0_3px_#CDCDCD]);
}

.input-wrapper {
  :deep(.el-input__wrapper) {
    @apply I_shadow-none;
  }

  :deep(input) {
    height: 0;
    padding: 1em 0;
    background-clip: content-box;

    ::first-line {
      color: var(--el-input-text-color, var(--el-text-color-regular));

    }
  }

  :deep(.el-input__inner) {
    @apply dark:!text-#f1f1f1;
  }
}
</style>
