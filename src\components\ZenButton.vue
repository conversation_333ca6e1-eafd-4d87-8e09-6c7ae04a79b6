<template>
  <button class="h-40px w-60px rounded-5px bg-control-button-group-bg text-14px text-#929292 font-Lexend shadow-[0px_5px_20px_rgba(0,0,0,0.07)] transition-cusbezier-300 dark:text-#F1F1F1 hover:(text-[var(--black)] font-800)" :class="{ 'mt-6': isClient }" @click="toggleZenMode">
    ZEN
  </button>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
const { isZenMode, isClient } = storeToRefs(appStore)

function toggleZenMode() {
  isZenMode.value = true
}
</script>

<style lang="scss" scoped>
</style>
