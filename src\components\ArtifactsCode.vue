<template>
  <!-- code -->
  <Teleport to=".app-view">
    <div class="h-100vh duration-500" :class="{ 'w-700px': visible, 'w-0px': !visible }"></div>
    <div
      class="fixed z-10 block h-100vh flex shrink-0 flex-col overflow-hidden border-[var(--artifacts-code-border)] rounded-tl-25px bg-[var(--artifacts-code-bg)] shadow-[0_0_50px_0_#0000000a] duration-500 !right-0"
      :class="{
        '!fixed !right-0 !top-0px !w-full !h-full !z-[100]': visible && fullVisible,
        'w-700px border-l-1px': visible,
        'full-panel': !isPc,
        '!w-0px border-l-0px': !visible,
      }"
    >
      <div
        class="flex justify-between border-b border-[var(--artifacts-code-border)] bg-[var(--artifacts-code-head-bg)] p-20px"
        :class="{
          'pt-[calc(1.5rem+20px)]': isClient,
        }"
      >
        <div class="flex items-center gap-10px">
          <div
            class="h-36px w-36px flex cursor-pointer items-center justify-center border border-[var(--artifacts-code-border)] rounded-8px bg-[var(--artifacts-code-bg)]"
            @click="setVisible()"
          >
            <i class="i-ju-indent text-10px text-#000000 text-#727272"></i>
          </div>

          <div
            class="h-36px w-36px flex cursor-pointer items-center justify-center border border-[var(--artifacts-code-border)] rounded-8px bg-[var(--artifacts-code-bg)]"
            @click="setFullVisible()"
          >
            <i v-if="!fullVisible" class="i-ri-fullscreen-line text-16px text-#000000 text-#727272"></i>
            <i v-else class="i-ri-fullscreen-exit-line text-16px text-#000000 text-#727272"></i>
          </div>
        </div>

        <div
          class="relative h-36px flex items-center border border-[var(--artifacts-code-tabs-border)] rounded-8px bg-[var(--artifacts-code-tabs)] md:ml-30px"
        >
          <div
            class="absolute left-5px top-5px h-[calc(100%-10px)] w-80px rounded-6px bg-#fff transition-all"
            :class="{ 'left-95px': activeTab === 'preview' }"
          ></div>
          <div
            class="relative h-full w-90px flex cursor-pointer items-center justify-center gap-10px text-center text-12px text-#727272 font-400 leading-36px"
            :class="{ '!text-#000000': activeTab === 'code' }"
            @click="activeTab = 'code'"
          >
            <div>Code</div>
            <Loading
              v-if="
                (loading || !data.code || !iframeLoaded) && activeTab === 'code' && !deployLoading
              "
              class="mb-3px mr-8px"
            />
          </div>
          <div
            class="relative h-full w-90px flex cursor-pointer items-center justify-center gap-5px text-center text-12px text-#727272 font-400 leading-36px"
            :class="{ '!text-#000000': activeTab === 'preview' }"
            @click="getTmpUrl"
          >
            <div>Preview</div>
            <Loading
              v-if="
                (loading || !data.code || !iframeLoaded)
                  && activeTab === 'preview' && !deployLoading
              "
              class="mb-3px mr-8px"
            />
          </div>
        </div>
        <div>
          <div
            v-debounce="() => getDeployUrl()"
            class="h-36px flex cursor-pointer items-center gap-8px rounded-8px bg-[var(--artifacts-code-deploy-bg)] px-10px transition-all hover:shadow-[0_0_15px_0_#00000060]"
            :class="{
              'contrast-30':
                deployUrl
                || !data.id
                || loading
                || !data.template
                || data.template !== vercelTemp,
              'fixed right-[30px] bottom-[20px]': !isPc && visible,
            }"
          >
            <i
              class="i-ju-code text-18px text-[var(--artifacts-code-deploy-text)]"
            ></i>
            <div
              class="flex items-center gap-5px text-12px text-[var(--artifacts-code-deploy-text)] font-700"
            >
              <div>Deployment</div>
              <Loading
                v-if="deployLoading"
                class="mb-3px mr-8px"
              />
            </div>
          </div>
        </div>
      </div>

      <div
        class="absolute left-0px top-77px w-full flex items-center justify-between p-20px"
      >
        <div
          v-if="isFilePathVisible && data.filePath"
          class="h-30px flex items-center border border-[var(--artifacts-code-border)] rounded-7px bg-[var(--artifacts-code-path-bg)] p-10px leading-30px"
          :class="{ 'mt-6': isClient }"
        >
          <i class="i-ju-source text-12px"></i>
          <div class="pl-7px text-12px text-#727272 font-400">
            {{ data.filePath }}
          </div>
        </div>

        <div
          v-if="activeTab === 'code'"
          class="flex items-center gap-15px text-16px text-#CBD5E1"
          :class="{ 'mt-6': isClient }"
        >
          <i class="i-ju-copy2 cursor-pointer" @click="copyCode"></i>
          <i class="i-ju-download cursor-pointer" @click="downloadCode"></i>
        </div>
      </div>

      <div class="flex-1 overflow-auto">
        <!-- v-show 不重新加载iframe -->
        <div
          v-show="activeTab === 'code'"
          class="whitespace-pre px-20px pb-40px pt-70px text-12px"
        >
          <code v-if="data.code" v-html="formatCode(data.code)"></code>
        </div>
        <div v-show="activeTab === 'preview'" class="h-full w-full">
          <!-- interpreter -->
          <div
            v-if="
              ['custom-codeinterpreter', 'code-interpreter-v1'].includes(
                data.template || '',
              )
            "
            class="px-20px pb-40px pt-70px"
          >
            <div v-if="cellRes.length">
              <div v-for="(item, index) in cellRes" :key="index">
                <img
                  class="w-full"
                  :src="`data:image/png;base64,${item.png}`"
                  alt=""
                />
              </div>
            </div>
            <div
              v-if="runtimeErr.name"
              class="absolute right-20px top-20px pt-130px"
            >
              <div class="text-12px text-#727272 font-400">
                {{ runtimeErr.name }}:{{ runtimeErr.value }}
              </div>
              <div class="text-12px text-#727272 font-400">
                {{ runtimeErr.traceback }}
              </div>
            </div>
            <code>
              <pre v-for="(item, index) in stdOut" :key="index">{{ item }}</pre>
              <pre v-for="(item, index) in stdErr" :key="index">{{ item }}</pre>
            </code>
          </div>
          <!-- iframe -->
          <iframe
            v-else
            class="h-full w-full"
            :src="tmpUrl || deployUrl"
            @load="onLoadIframe"
          ></iframe>
          <div
            v-if="deployUrl"
            class="absolute bottom-20px left-20px rounded-50px bg-#EDF0F5 p-5px px-10px dark:bg-#1E293B"
          >
            <div class="text-14px font-400">
              <span class="pr-5px">🚧</span>
              <span> {{ deployUrl }}</span>
              <i class="i-ju-copy2 ml-5px cursor-pointer" @click="copyDeployUrl"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { copy } from '@/common'
import services from '@/common/axios'
import { notify } from '@/common/tools'
import { mixpanel } from '@/config/mixpanel'
import { useAppStore } from '@/stores/app'
import hljs from 'highlight.js'

const props = defineProps<{
  visible: boolean
  data: {
    id?: string
    title?: string
    artifacts?: {
      url?: string
    }
    description?: string
    code?: string
    filePath?: string
    template?: string
  }
}>()

const emits = defineEmits(['update:visible', 'deploy'])
const { isPc, isClient } = storeToRefs(useAppStore())
const loading = ref(false)
const deployLoading = ref(false)
const fullVisible = ref(false)
const activeTab = ref('code')
const tmpUrl = ref('')
const deployUrl = ref('')
const stdOut = ref<string[]>([])
const stdErr = ref<string[]>([])
const cellRes = ref<any[]>([])
const runtimeErr = ref<{ name?: string, value?: string, traceback?: string }>(
  {},
)
const iframeLoaded = ref(false)

const vercelTemp = computed(() => {
  if (import.meta.env.MODE === 'production') {
    return 'nextjs-developer-vercel'
  }
  return 'nextjs-developer-vercel-test'
})

// filePath组件是否显示
// tab为code时，filePath组件显示
// tab为preview时，iframe的src有值且未加载完成时显示
const isFilePathVisible = computed(
  () =>
    (activeTab.value === 'preview'
      && !iframeLoaded.value
      && (tmpUrl.value || deployUrl.value))
    || activeTab.value === 'code',
)

// 当链接tmpUrl或deployUrl有值传入时，iframeLoaded变为加载中状态
watch(
  () => [tmpUrl.value, deployUrl.value],
  () => {
    if (tmpUrl.value || deployUrl.value) {
      iframeLoaded.value = false
    }
  },
)

// 每次Code组件的data发生变化时
watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      initData(newVal)
    }
  },
)

const setVisible = () => {
  if (fullVisible.value) {
    fullVisible.value = false
    return
  }
  emits('update:visible', false)
}

const setFullVisible = () => {
  fullVisible.value = !fullVisible.value
}

const initData = (data: any) => {
  // 生成中时，显示code
  if (!data.code || !data.id) {
    activeTab.value = 'code'
  }
  // 历史记录或生成过程中获取到code或id，则显示preview
  else {
    tmpUrl.value = ''
    deployUrl.value = data.artifacts?.url || ''
    stdOut.value = []
    stdErr.value = []
    cellRes.value = []
    runtimeErr.value = {}
    getTmpUrl()
  }
}

const copyDeployUrl = () => {
  if (deployUrl.value) {
    copy(deployUrl.value)
    mixpanel.artifacts('copy deploy url')
  }
}

const getTmpUrl = async () => {
  if (!props.data.id) {
    notify.error({
      message: '请先等待数据生成后预览',
    })
    activeTab.value = 'code'
    return
  }
  activeTab.value = 'preview'
  // 如果已经获取过url，则不再获取
  if (deployUrl.value || tmpUrl.value) {
    return
  }
  // 如果已经获取过结果，则不再获取
  if (stdOut.value.length || stdErr.value.length) {
    return
  }
  loading.value = true
  try {
    const {
      url,
      stdout,
      stderr,
      cellResults,
      runtimeError,
    }: {
      url: string
      stdout: string[]
      stderr: string[]
      cellResults: any[]
      runtimeError: { name: string, value: string, traceback: string }
    } = await services.post(
      `${import.meta.env.VITE_APP_GPT_URL}/gpt/artifacts/sandbox`,
      {
        id: props.data.id,
      },
    )
    tmpUrl.value = url
    stdOut.value = stdout
    stdErr.value = stderr
    cellRes.value = cellResults
    runtimeErr.value = runtimeError || {}
    mixpanel.artifacts('preview')
  }
  catch {
  }
  finally {
    loading.value = false
  }
}

const getDeployUrl = async () => {
  if (!props.data.id) {
    return
  }
  if (deployUrl.value) {
    return
  }
  if (loading.value) {
    return
  }
  if (props.data.template !== vercelTemp.value) {
    return
  }

  activeTab.value = 'preview'
  loading.value = true
  deployLoading.value = true
  try {
    const { url }: { url: string } = await services.post(
      `${import.meta.env.VITE_APP_GPT_URL}/gpt/artifacts/deploy`,
      {
        id: props.data.id,
      },
    )
    deployUrl.value = url
    notify.success({
      message: '部署成功',
    })
    mixpanel.artifacts('deploy')
    emits('deploy')
  }
  catch {
  }
  finally {
    deployLoading.value = false
    loading.value = false
  }
}

const formatCode = (code: string) => {
  return hljs.highlightAuto(code).value
}

const downloadCode = () => {
  if (props.data.code && props.data.filePath) {
    const filename = props.data.filePath.split('/').pop() || ''
    const blob = new Blob([props.data.code], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }
}

const copyCode = () => {
  if (props.data.code) {
    copy(props.data.code)
    mixpanel.artifacts('copy code')
  }
}

const onLoadIframe = () => {
  iframeLoaded.value = true
}
</script>

<style lang="scss" scoped>
.full-panel {
  position: fixed;
  width: 100%;
  top: 60px;
  z-index: 99;
}

code,
kbd,
samp,
pre {
  white-space: pre-wrap;
}
</style>
