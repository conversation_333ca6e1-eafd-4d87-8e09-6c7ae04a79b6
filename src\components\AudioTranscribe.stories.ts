import type { Meta, StoryObj } from '@storybook/vue3'
import AudioTranscribe from './AudioTranscribe.vue'

const meta: Meta<typeof AudioTranscribe> = {
  title: 'component/AudioTranscribe',
  component: AudioTranscribe,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  args: {
    "onRecording-complete": (file: File) => {
      const a = document.createElement('a')
      a.href = URL.createObjectURL(file)
      a.download = file.name
      a.click()
      a.remove()
    },
    onError: (error: string) => {
      console.log(error)
    },
    onStart: () => {
      console.log('start')
    },
    onStop: () => {
      console.log('stop')
    }
  },
}

export const Loading: Story = {
  args: {
    loading: true
  }
}

