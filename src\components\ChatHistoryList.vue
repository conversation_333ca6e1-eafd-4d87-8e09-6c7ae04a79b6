<template>
  <div
    ref="sideScroll"
    class="sidebar-list h-full grow overflow-y-auto pb-9.5 lt-md:px-6"
  >
    <!-- regular -->
    <template v-if="selected === 0 || inPanel">
      <!-- LIST -->
      <div
        v-for="item in menuData"
        :key="item.key"
        class="relative transition-cusbezier-300 space-y-2.5"
      >
        <div
          v-if="formatMessage[item.data]?.length"
          class="group sticky top-0 z-20 min-w-10 w-full pt-5 text-12px text-primary-100 font-400 font-Lexend"
          :class="[
            getBackground,
            inPanel && '-top-1px',
          ]"
        >
          <div
            class="flex cursor-pointer items-center justify-between overflow-x-hidden"
            @click="keyVisible(item, 'visible', true)"
          >
            <div class="flex items-center space-x-2">
              <span>{{ item.label }}</span>

              <el-popover
                v-if="!item.visibleEdit"
                :ref="(el) => { popoverRef['chat-group'] = el }"
                :key="popoverRandomKey"
                data-id="chat-group"
                popper-class="bg-primary-500! min-w-auto! w-auto! p-0! !z-20100"
                trigger="click"
                placement="bottom-start"
                :offset="-5"
                :persistent="false"
                :show-arrow="false"
              >
                <template #default>
                  <div
                    text="13px primary-300"
                    class="group_options min-w-160px flex flex-col"
                  >
                    <section
                      text="10px [var(--p1)]"
                      class="h-26px flex-y-c select-none bg-[var(--ratio-button-bg)] pl-13px font-Lexend"
                    >
                      Filter
                    </section>
                    <ChatHistoryListOptionSelect
                      :key="chatHistoryOptionSelectKey"
                      v-model="filterOption"
                    />
                    <section
                      text="10px [var(--p1)]"
                      class="h-26px flex-y-c select-none bg-[var(--ratio-button-bg)] pl-13px font-Lexend"
                    >
                      Operation
                    </section>
                    <div
                      class="flex-y-c cursor-pointer justify-between px-13px py-5px hover:font-600"
                      @click="(e) => handleDeleteGroup(e, { type: 'delete-all', list: formatMessage[item.data], groupInfo: item })"
                    >
                      {{ $t("sideBar.clear") }}
                    </div>
                    <div
                      class="flex-y-c cursor-pointer justify-between px-13px py-5px pb-10px hover:font-600"
                      @click="(e) => handleDeleteGroup(e, { type: 'delete-select', groupInfo: item })"
                    >
                      {{ $t("sideBar.edit") }}
                    </div>
                  </div>
                </template>
                <template #reference>
                  <i
                    class="i-ri-more-fill invisible cursor-pointer text-4 group-hover:visible"
                    @click.stop="emitHideModal"
                  />
                </template>
              </el-popover>
            </div>
            <div class="flex items-center gap-x-1">
              <div
                v-if="item.visibleEdit"
                class="flex items-center space-x-2"
              >
                <span @click="(e) => handleOperationGroup(e, 'cancel', item)">
                  {{ $t("sideBar.cancel") }}</span>
                <span
                  v-if="editSelects.length"
                  class="text-destructive-200"
                  @click="(e) => handleOperationGroup(e, 'delete', item)"
                >
                  {{ $t("sideBar.remove") }}
                </span>
              </div>
              <i
                :class="`i-ri-arrow-down-s-line transition-transform duration-500 ${
                  item.visible ? 'rotate-180' : ''
                }`"
              ></i>
            </div>
          </div>
        </div>
        <template v-if="item.visible">
          <template v-if="item.visibleEdit">
            <div
              v-for="(chatMessage, i) in currentMessageList"
              :key="i"
              :class="`group space-x-2 cursor-pointer hover:bg-primary-200 rounded-1.25 transition-cusbezier-300 relative flex items-center justify-between h-10 p-2 mx-2 ${
                messageId === chatMessage.id ? 'bg-primary-200 dark:(text-#fff)' : ''
              }`"
              @click="emitSelectGroupMessage(chatMessage)"
              @touchstart="emitSelectGroupMessage(chatMessage)"
            >
              <svg class="h-[16px] w-[16px] text-[var(--app-bg)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="11" :fill="chatMessage.selected ? 'var(--black)' : 'none'" :stroke="chatMessage.selected ? 'none' : 'gray'" stroke-width="2" />
                <path v-if="chatMessage.selected" class="animate-check" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.5 12l3 3L16.5 9" stroke-dasharray="45" stroke-dashoffset="0"></path>
              </svg>
              <input
                v-model="chatMessage.selected"
                style="display: none"
                type="checkbox"
              />
              <div class="line-clamp-1 flex-1 select-none break-all text-sm">
                {{ chatMessage.name }}
              </div>
            </div>
          </template>
          <template v-else>
            <div
              v-for="({ name, id, ttsLanguageTypeId, ttsType, modeId, editing, groupId }, index) in filteredChatHistory(item.data)"
              :key="id"
              class="group relative h-10 min-w-10 flex-y-c cursor-pointer justify-between rounded-1.43 px-3 py-2 text-sm transition-cusbezier-300 focus:bg-primary-200 hover:bg-primary-200 dark:(active:text-[var(--p1)] hover:text-#E7E9ED)"
              :class="{
                'bg-primary-200 font-500 dark:(text-#fff)': messageId === id,
              }"
              :style="{
                animation: item.visible
                  ? `fadeInDown ${
                    Math.min((index + 1) * 100, 1800) // 最大延迟为1800ms，防止列表项很多时，后面的项动画延迟时间过长
                  }ms var(--ani-bezier)`
                  : '',
              }"
              @click="emitSelectMessage({ id, ttsLanguageTypeId, ttsType, modeId, editing, groupId })"
            >
              <template v-if="!editing">
                <!-- 这里是Chat历史每一项历史对话 -->
                <div class="line-clamp-1 flex-1 select-none break-all text-sm">
                  {{ name }}
                </div>
                <template v-if="!loading">
                  <el-popover
                    :ref="(el) => { popoverRef[id] = el }"
                    :data-id="id"
                    popper-class="bg-primary-500! min-w-auto! w-auto! p-0! !z-20100"
                    trigger="click"
                    placement="bottom-start"
                    :offset="-5"
                    :persistent="false"
                    :show-arrow="false"
                  >
                    <template #default>
                      <div
                        class="flex flex-col px-6 py-4 text-3.25 text-primary-300 [&>*:hover]:(cursor-pointer text-primary-400 font-600) space-y-2"
                      >
                        <div @click=" handleOperation({ type: 'share', id, mode: 'MESSAGE', name })">
                          {{ $t("sideBar.share") }}
                        </div>
                        <div @click="handleOperation({ type: 'edit', id, mode: 'MESSAGE', name })">
                          {{ $t("sideBar.update") }}
                        </div>
                        <div @click="handleOperation({ type: 'delete', id, mode: 'MESSAGE', name })">
                          {{ $t("sideBar.remove") }}
                        </div>
                        <div @click="emitResetTitle(id)">
                          {{ $t("sideBar.resetTitle") }}
                        </div>
                        <el-dropdown
                          v-if="isPc"
                          placement="right-start"
                          trigger="click"
                          popper-class="!z-20105"
                          @command="editMoveGroup"
                        >
                          <div class="flex items-center justify-between text-13px text-primary-300 leading-[1.4] outline-none">
                            <span>{{ $t("sideBar.moveToGroup") }}</span>
                            <i class="i-icon-park-outline-right" />
                          </div>
                          <template #dropdown>
                            <el-dropdown-menu class="group_dropdown !bg-primary-500">
                              <el-dropdown-item
                                v-for="(groupsItem, groupsIdx) in childGroups" :key="groupsIdx"
                                :command="{
                                  groupId: groupsItem.id,
                                  dialogId: id,
                                }"
                              >
                                <span
                                  :class="{
                                    'font-600 !text-control-button-group-text-selected':
                                      groupId === groupsItem.id,
                                  }"
                                >
                                  {{ groupsItem.name }}
                                </span>
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                    </template>
                    <template #reference>
                      <div
                        class="hidden items-center justify-center group-hover:flex"
                        @click.stop="emitHideModal"
                      >
                        <div
                          class="i-ri-more-fill text-lg text-primary-100"
                        />
                      </div>
                    </template>
                  </el-popover>
                </template>
              </template>
              <div
                v-else
                class="w-full flex items-center justify-between"
              >
                <input
                  ref="chatInputRef"
                  v-model="messageName"
                  :data-id="id"
                  class="w-28 text-sm"
                  :placeholder="$t('sideBar.dialogPlaceholder')"
                  type="text"
                  :maxlength="50"
                  @keydown.enter="emitEditMessage({ messageId: id, type: 'MESSAGE' })"
                />
                <div class="flex">
                  <i
                    class="i-ri-close-line text-lg text-primary-800"
                    @click.stop="emitBlurMessage({ messageId: id, type: 'MESSAGE' })"
                  />
                  <i
                    class="i-ri-check-line text-lg dark:text-#E7E9ED"
                    @click.stop="emitEditMessage({ messageId: id, type: 'MESSAGE' })"
                  />
                </div>
              </div>
            </div>
          </template>
        </template>
      </div>
    </template>
    <!-- sense -->
    <template v-else-if="selected === 1 && !inPanel">
      <div
        class="whitespace-nowrap pt-5 transition-cusbezier-300 space-y-2.5"
      >
        <div
          v-for="({ id: groupId, name, starFlag, defaultFlag, visible, array: parentGroupsArray, groupType }, index) in parentGroups"
          :key="index"
        >
          <div
            class="group h-10 w-full flex cursor-pointer items-center justify-between gap-2 border-0.3 border-primary-200 rounded-1.25 bg-model-select-bg p-2 transition-cusbezier-300"
            @click="emitSwitchMessage({ groupId, defaultFlag, reload: false })"
          >
            <i v-if="starFlag" class="i-ju-star"></i>
            <i v-else-if="defaultFlag" class="i-ju-group"></i>
            <i v-else class="i-ju-scene"></i>
            <div class="line-clamp-1 flex-1 select-none break-all text-sm">{{ name }}</div>
            <!-- <div
              class="pr-1 text-sm text-secondary-200 font-RobotoMono"
            >
              <template v-if="starFlag">
                {{
                  levelLimitVo.starGroupDialogLimit < 999999
                    ? `${levelLimitVo.starGroupDialogUsed}/${levelLimitVo.starGroupDialogLimit}`
                    : "∞"
                }}
              </template>
              <template v-else>
                {{
                  levelLimitVo.groupLimit < 999999
                    ? `${levelLimitVo.groupUsed}/${levelLimitVo.groupLimit}`
                    : "∞"
                }}
              </template>
            </div> -->
            <i
              :class="`i-ri-arrow-down-s-line transition-transform duration-500 ${
                visible ? 'rotate-180' : ''
              }`"
            ></i>
          </div>
          <template v-if="visible">
            <template v-if="groupType === 'scenceGroup'">
              <div
                v-for="{ id: groupId, name, visible, array: childGroupsArray, editing2 } in childGroups"
                :key="groupId"
                class="relative ml-10 mt-5 text-sm space-y-2.5"
              >
                <div
                  :class="
                    editing2 ? 'rounded-1.25 border-[2px_solid]' : ''
                  "
                  class="group h-10 w-full flex cursor-pointer items-center justify-between border-0.25 border-primary-200 rounded-sm bg-model-select-bg px-4 transition-cusbezier-300"
                  @click.prevent="emitSwitchMessage({ groupId, reload: false, editing2 })"
                >
                  <div
                    v-if="!editing2"
                    class="flex-between w-full flex items-center gap-2"
                  >
                    <div class="flex-1 select-none of-hidden text-ellipsis">
                      {{ name }}
                    </div>
                    <div
                      v-if="!loading"
                      class="hidden group-hover:flex"
                    >
                      <div
                        v-if="!['默认分组'].includes(name)"
                        class="i-ri-pencil-line mr-1 text-lg"
                        @click.stop="emitRenameGroup({ groupId, editing2: false })"
                      ></div>
                      <el-popconfirm
                        v-if="!['默认分组'].includes(name)"
                        :offset="20"
                        :show-arrow="false"
                        :width="200"
                        hide-icon
                        :title="$t('sideBar.removeTitle')"
                        :confirm-button-text="$t('sideBar.confirm')"
                        :cancel-button-text="$t('sideBar.cancel')"
                        :hide-after="0"
                        @confirm="deleteGroup({ groupId })"
                      >
                        <template #reference>
                          <div
                            class="flex items-center justify-center"
                            @click.stop
                          >
                            <div class="i-ri-delete-bin-line mr-1 text-lg"></div>
                          </div>
                        </template>
                      </el-popconfirm>
                    </div>
                    <i
                      :class="`i-ri-arrow-down-s-line transition-transform duration-500 ${
                        visible ? 'rotate-180' : ''
                      }`"
                    ></i>
                  </div>
                  <div
                    v-else
                    class="w-full flex items-center justify-between"
                  >
                    <!-- @TODO: tabs补全 @keydown.prevent="(e) => handleInputKeydown(e, name, groupId)"。但是会导致groupName公用同一个需要解决  -->
                    <input
                      ref="chatInputRef"
                      v-model="groupName"
                      :data-id="groupId"
                      class="w-28 text-sm"
                      :maxlength="10"
                      :placeholder="editing2 ? name : $t('sideBar.groupPlaceholder')"
                      type="text"
                    />
                    <div class="flex">
                      <i
                        class="i-ri-close-line text-lg text-primary-800"
                        @click.stop="emitRenameGroup({ groupId, editing2: false })"
                      ></i>
                      <i
                        class="i-ri-check-line text-lg dark:text-#E7E9ED"
                        @click.stop="emitFocusGroup({ groupId, editing2: false })"
                      ></i>
                    </div>
                  </div>
                </div>
                <div v-if="visible" class="ml-4.5">
                  <div
                    v-draggable="[
                      childGroupsArray,
                      {
                        animation: 150,
                        onUpdate: ({ data }: any) => {
                          emitDragUpdate({ array: childGroupsArray, data })
                        },
                      },
                    ]"
                  >
                    <div
                      v-for="({ id, name, ttsLanguageTypeId, ttsType, modeId, editing, groupId }, index) in childGroupsArray"
                      :key="index"
                      :class="`group cursor-pointer focus:bg-primary-200 text-sm hover:bg-primary-200 rounded-1.43 transition-cusbezier-300 relative flex items-center justify-between w-full h-10 px-3 py-2 ${
                        messageId === id ? 'bg-primary-200 font-500 dark:(text-#fff)' : ''
                      }`"
                      @click="emitSelectMessage({ id, ttsLanguageTypeId, ttsType, modeId, editing, groupId })"
                    >
                      <template v-if="!editing">
                        <div class="line-clamp-1 flex-1 select-none break-all text-sm">
                          {{ name }}
                        </div>
                        <template v-if="!loading">
                          <el-popover
                            :ref="(el: any) => { popoverRef[id] = el }"
                            :data-id="id"
                            popper-class="bg-primary-500! min-w-auto! w-auto! p-0! !z-20100"
                            trigger="click"
                            placement="bottom-start"
                            :offset="-5"
                            :persistent="false"
                            :show-arrow="false"
                          >
                            <template #default>
                              <div
                                class="flex flex-col px-6 py-4 text-3.25 text-primary-300 [&>*:hover]:(cursor-pointer text-primary-400 font-600) space-y-2"
                              >
                                <div @click="handleOperation({ type: 'share', id, mode: 'GROUP', name })">
                                  {{ $t("sideBar.share") }}
                                </div>
                                <div @click="handleOperation({ type: 'edit', id, mode: 'GROUP', name })">
                                  {{ $t("sideBar.update") }}
                                </div>
                                <div @click="handleOperation({ type: 'delete', id, mode: 'GROUP', name })">
                                  {{ $t("sideBar.remove") }}
                                </div>
                                <el-dropdown
                                  v-if="isPc"
                                  placement="right-start"
                                  trigger="click"
                                  popper-class="!z-20105"
                                  @command="editMoveGroup"
                                >
                                  <div class="flex items-center justify-between text-13px text-primary-300 leading-[1.4] outline-none">
                                    <span>{{ $t("sideBar.moveToGroup") }}</span>
                                    <i class="i-icon-park-outline-right" />
                                  </div>
                                  <template #dropdown>
                                    <el-dropdown-menu class="group_dropdown !bg-primary-500">
                                      <el-dropdown-item
                                        v-for="(groupsItem, groupsIdx) in childGroups" :key="groupsIdx"
                                        :command="{
                                          groupId: groupsItem.id,
                                          dialogId: id,
                                        }"
                                      >
                                        <span
                                          :class="{
                                            'font-600 !text-control-button-group-text-selected':
                                              groupId === groupsItem.id,
                                          }"
                                        >
                                          {{ groupsItem.name }}
                                        </span>
                                      </el-dropdown-item>
                                    </el-dropdown-menu>
                                  </template>
                                </el-dropdown>
                              </div>
                            </template>
                            <template #reference>
                              <div
                                class="hidden items-center justify-center group-hover:flex"
                                @click.stop="emitHideModal"
                              >
                                <div
                                  class="i-ri-more-fill text-lg text-primary-100"
                                ></div>
                              </div>
                            </template>
                          </el-popover>
                        </template>
                      </template>
                      <div
                        v-else
                        class="w-full flex items-center justify-between"
                      >
                        <input
                          ref="chatInputRef"
                          v-model="messageName"
                          :data-id="id"
                          class="w-28 text-sm"
                          :maxlength="50"
                          :placeholder="$t('sideBar.dialogPlaceholder')"
                          type="text"
                        />
                        <div class="flex">
                          <i
                            class="i-ri-close-line text-lg text-primary-800"
                            @click.stop="emitBlurMessage({ messageId: id, type: 'GROUP' })"
                          ></i>
                          <i
                            class="i-ri-check-line text-lg dark:text-#E7E9ED"
                            @click.stop="emitEditMessage({ messageId: id, type: 'GROUP' })"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="ml-10 mt-5">
                <div
                  class="h-10 w-full flex cursor-pointer select-none items-center justify-between border-1px border-primary-200 rounded-sm border-style-dashed bg-model-select-bg pl-4 pr-2 text-[var(--sidebar-add-group-color)] transition-cusbezier-300"
                >
                  <div v-if="!editing" @click="emitEditGroup">
                    + {{ $t("sideBar.addGroup") }}
                  </div>
                  <div v-else class="w-full flex-y-c justify-between">
                    <input
                      v-model="groupName"
                      class="w-28 flex-1 text-sm"
                      :maxlength="10"
                      :placeholder="$t('sideBar.groupPlaceholder')"
                      type="text"
                    />
                    <div
                      class="i-ri-check-line bg-[var(--black)] text-lg"
                      @click="emitEditGroup"
                    ></div>
                  </div>
                </div>
              </div>
            </template>
            <div
              v-for="({ id: arrayId, name, ttsLanguageTypeId, ttsType, modeId, editing, groupId }, index) in parentGroupsArray"
              v-else
              :key="index"
              :data-id="arrayId"
              :class="`group cursor-pointer focus:bg-primary-200 text-sm hover:bg-primary-200 rounded-1.43 transition-cusbezier-300 relative flex items-center justify-between w-56 h-10 m-x-auto mt-2.5 px-3 py-2 ${
                messageId === arrayId ? 'bg-primary-200 font-500 dark:(text-#fff)' : ''
              }`"
              @pointerdown="emitSelectMessage({ id: arrayId, ttsLanguageTypeId, ttsType, modeId, editing, groupId })"
            >
              <template v-if="!editing">
                <div class="line-clamp-1 flex-1 break-all text-primary-foreground">
                  {{ name }}
                </div>
                <template v-if="!loading">
                  <el-popover
                    :ref="(el) => { popoverRef[arrayId] = el }"
                    :data-id="arrayId"
                    popper-class="bg-primary-500! min-w-auto! w-auto! p-0! !z-20100"
                    trigger="click"
                    placement="bottom-start"
                    :offset="-5"
                    :persistent="false"
                    :show-arrow="false"
                  >
                    <template #default>
                      <div
                        class="flex flex-col px-6 py-4 text-3.25 text-primary-300 [&>*:hover]:(cursor-pointer text-primary-400 font-600) space-y-2"
                      >
                        <div @click="handleOperation({ type: 'share', id: arrayId, mode: 'GROUP', name })">
                          {{ $t("sideBar.share") }}
                        </div>
                        <div @click="handleOperation({ type: 'edit', id: arrayId, mode: 'GROUP', name })">
                          {{ $t("sideBar.update") }}
                        </div>
                        <div @click="handleOperation({ type: 'delete', id: arrayId, mode: 'GROUP', name })">
                          {{ $t("sideBar.remove") }}
                        </div>
                        <el-dropdown
                          v-if="isPc"
                          placement="right-start"
                          trigger="click"
                          popper-class="!z-20105"
                          @command="editMoveGroup"
                        >
                          <div class="flex items-center justify-between text-13px text-primary-300 leading-[1.4] outline-none">
                            <span>{{ $t("sideBar.moveToGroup") }}</span>
                            <i class="i-icon-park-outline-right" />
                          </div>
                          <template #dropdown>
                            <el-dropdown-menu class="group_dropdown !bg-primary-500">
                              <el-dropdown-item
                                v-for="(groupsItem, groupsIdx) in childGroups" :key="groupsIdx"
                                :command="{
                                  groupId: groupsItem.id,
                                  dialogId: id,
                                }"
                              >
                                <span
                                  :class="{
                                    'font-600 !text-control-button-group-text-selected':
                                      groupId === groupsItem.id,
                                  }"
                                >
                                  {{ groupsItem.name }}
                                </span>
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                    </template>
                    <template #reference>
                      <div
                        class="hidden items-center justify-center group-hover:flex"
                        @click.stop="emitHideModal"
                      >
                        <div
                          class="i-ri-more-fill text-lg text-primary-100"
                        ></div>
                      </div>
                    </template>
                  </el-popover>
                </template>
              </template>
              <template v-else>
                <input
                  ref="chatInputRef"
                  v-model="messageName"
                  :data-id="arrayId"
                  class="w-28 text-sm"
                  :maxlength="50"
                  :placeholder="$t('sideBar.dialogPlaceholder')"
                  type="text"
                />
                <div
                  class="i-ri-close-line mr-2 text-lg text-primary-800"
                  @click.stop="emitBlurMessage({ messageId: arrayId, type: 'GROUP' })"
                ></div>
                <div
                  class="i-ri-check-line text-lg dark:text-#E7E9ED"
                  @click.stop="emitEditMessage({ messageId: arrayId, type: 'GROUP' })
                  "
                ></div>
              </template>
            </div>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { nanoid } from 'nanoid'
import { vDraggable } from 'vue-draggable-plus'
import { formatTimestampCategory, notify, TimeCategory } from '@/common/tools'
import i18n from '@/i18n'
import { useAppStore } from '@/stores/app'

const props = defineProps<{
  selected: number
  menuData: any[]
  messageId: number
  loading: boolean
  groups: any[]
  editing: boolean
  currentMessageList: any[]
  messages: any[]
  inPanel?: boolean
  formatMessage: any
}>()
const emits = defineEmits([
  'deleteGroup',
  'deleteGroup2',
  'hideModal',
  'operationGroup',
  'selectGroupMessage',
  'selectMessage',
  'operation',
  'resetTitle',
  'moveGroup',
  'editMessage',
  'blurMessage',
  'switchMessage',
  'renameGroup',
  'focusGroup',
  'dragUpdate',
  'editGroup',
])
const groupName = defineModel<string>('groupName')
const messageName = defineModel<string>('messageName')
const appStore = useAppStore()
const { isPc, isZenMode } = storeToRefs(appStore)
const popoverRef = ref<Record<number | string, any>>({})
const chatInputRef = ref<HTMLInputElement[]>([])
const popoverRandomKey = ref(nanoid())
// 筛选选项相关
const chatHistoryOptionSelectKey = ref(nanoid())
const filterOption = ref('all')
watch(filterOption, () => {
  // 之前这里设计的popover隐藏方式过于复杂难以操作，所以直接更换key实现隐藏
  popoverRandomKey.value = nanoid()
})

function emitSelectGroupMessage(item: any) {
  emits('selectGroupMessage', item)
}

function editMoveGroup(param: { groupId: number, dialogId: number }) {
  emits('moveGroup', param)
}

function handleOperation(operation: {
  id: number
  type: string
  mode: 'MESSAGE' | 'GROUP'
  name: string
}) {
  emits('operation', operation)
  nextTick(() => {
    chatInputRef.value
      .find(item => Number(item.getAttribute('data-id')) === operation.id)
      ?.focus()
  })
}

const sideScroll = ref()

const scrollTop = () => {
  sideScroll.value.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

const childGroups: any = computed(() => {
  return props.groups.filter(
    (element: any) => !element.starFlag && !element.defaultFlag,
  )
})

const keyVisible = (
  item: any,
  key: 'visible' | 'visibleEdit',
  needUpdateMenus = false,
) => {
  for (let i = 0; i < props.menuData.length; i++) {
    const m = props.menuData[i]
    if (m.key === item.key) {
      item[key] = !item[key]
      if (needUpdateMenus) {
        const list = props.menuData.map((it: any) => {
          Reflect.deleteProperty(it, 'visibleEdit')
          return it
        })
        localStorage.setItem('menus', JSON.stringify(list))
      }
      break
    }
  }
}

const parentGroups: any = computed(() => {
  return props.groups.filter(
    (element: any) => element.starFlag || element.defaultFlag,
  )
})

function handleDeleteGroup(
  event: Event,
  config: {
    type: 'delete-all' | 'delete-select'
    list?: any[]
    groupInfo?: any
  },
) {
  if (config.type === 'delete-all' && !config.list?.length) {
    notify.error({
      title: i18n.global.t('sideBar.deleteAllListEmpty'),
    })
    return
  }
  emits('deleteGroup', event, config)
}

function emitSelectMessage({ id, ttsLanguageTypeId, ttsType, modeId, editing, groupId }: any) {
  emits('selectMessage', { id, ttsLanguageTypeId, ttsType, modeId, editing, groupId })
}

function emitHideModal() {
  emits('hideModal')
}

function emitResetTitle(id: string) {
  emits('resetTitle', id)
}

function emitEditMessage({ messageId, type }: any) {
  emits('editMessage', { messageId, type })
}

function emitBlurMessage({ messageId, type }: any) {
  emits('blurMessage', { messageId, type })
}

function handleOperationGroup(
  event: Event,
  type: 'cancel' | 'delete',
  item: any,
) {
  emits('operationGroup', event, type, item)
}

function emitSwitchMessage({ groupId, reload, editing2 }: any) {
  emits('switchMessage', { groupId, reload, editing2 })
}

function emitRenameGroup({ groupId, editing2 }: any) {
  emits('renameGroup', { groupId, editing2 })
}

function emitFocusGroup({ groupId, editing2 }: any) {
  emits('focusGroup', { groupId, editing2 })
}

const editSelects = computed(() => {
  return props.currentMessageList
    .filter((it: any) => it.selected)
    .map((it: any) => it.id)
})

// 根据筛选选项过滤消息
const filteredChatHistory = computed(() => {
  return (dataType: string) => {
    const messages = props.formatMessage[dataType] || []
    if (filterOption.value === 'all') {
      return messages
    }

    // 这个的key要和ChatHistoryListOptionSelect.vue中的v-model的值可能出现的值一致
    const filterMap: Record<string, (typeof TimeCategory)[keyof typeof TimeCategory][]> = {
      today: [TimeCategory.TODAY],
      // 本周的要包含今天
      week: [TimeCategory.TODAY, TimeCategory.WITHIN_7_DAYS],
      // 本月的要包含今天和本周
      month: [TimeCategory.TODAY, TimeCategory.WITHIN_7_DAYS, TimeCategory.WITHIN_31_DAYS],
      earlier: [TimeCategory.EARLIER],
    }

    const allowedCategories = filterMap[filterOption.value] || []
    console.log('allowedCategories: ', allowedCategories)

    return messages.filter((message: any) => {
      const category = formatTimestampCategory(message.createTime)
      return allowedCategories.includes(category)
    })
  }
})

function emitDragUpdate({ array, data }: any) {
  emits('dragUpdate', { array, data })
}

const deleteGroup = ({ groupId }: any) => {
  emits('deleteGroup2', { id: groupId })
}

function emitEditGroup() {
  emits('editGroup')
}

const getBackground = computed(() => {
  if (isZenMode.value) {
    return 'bg-#fff dark:bg-#272727'
  }
  else if (isPc.value) {
    return 'bg-primary-600'
  }
  else {
    return 'bg-[var(--white)]'
  }
})
defineExpose({
  scrollTop,
  keyVisible,
})
</script>

<style lang="scss" scoped>
.sidebar-list {
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }
}

.group_dropdown {
  :deep(.el-dropdown-menu__item) {
    background: transparent !important;
    font-size: 13px;
    color: inherit;

    &:hover {
      font-weight: 600;
    }
  }
}
</style>
