<template>
  <div>
    <div
      v-if="[1].includes(stripeStatus)"
      class="fixed h-full w-full flex items-center justify-center"
    >
      <div ref="lottieElement" class="absolute top-[-100px] h-full w-[800px]" />
      <div>
        <div>{{ $t("stripeView.payNormalTip") }}</div>
        <div
          class="bg-destructive-300 relative m-auto mb-[60px] mt-[30px] w-[200px] cursor-pointer rounded-[5px] p-[10px] text-center text-[12px] text-[#FFFFFF] shadow-[0_0_50px_0_rgba(246,81,52,0.46)]"
          @click="goHome"
        >
          {{ $t("stripeView.buttonText") }}
        </div>
      </div>
    </div>
    <div
      v-if="[2, 3, 4, 5, 6].includes(stripeStatus)"
      class="fixed h-full w-full flex items-center justify-center"
    >
      <div ref="lottieElement" class="absolute top-[-100px] h-full w-[800px]" />
      <div>
        <div>{{ $t("stripeView.payErrorTip") }}</div>
        <div
          class="bg-destructive-300 relative m-auto mb-[60px] mt-[30px] w-[200px] cursor-pointer rounded-[5px] p-[10px] text-center text-[12px] text-[#FFFFFF] shadow-[0_0_50px_0_rgba(246,81,52,0.46)]"
          @click="goHome"
        >
          {{ $t("stripeView.buttonText") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import services from "@/common/axios";
import { lottieAnimation } from "@/common";

const route = useRoute();
const router = useRouter();

const interval = ref();
const stripeStatus = ref(0);
const lottieElement = ref();
function getStatus() {
  if (!route.query.orderNo) {
    return;
  }
  interval.value = setInterval(async () => {
    try {
      const { status }: any = await services.post(
        `${import.meta.env.VITE_APP_PAY_URL}/order/detail`,
        {
          orderNo: route.query.orderNo,
        },
        {
          headers: {
            noninductive: true,
          },
        }
      );
      if ([1].includes(status)) {
        clearInterval(interval.value);
        stripeStatus.value = status;
        nextTick(() => {
          lottieAnimation(lottieElement.value);
        });
      }
    } catch {
      clearInterval(interval.value);
    }
  }, 1000);
}

function goHome() {
  router.replace({ name: "Chat" });
}

onMounted(() => {
  getStatus();
});
</script>
