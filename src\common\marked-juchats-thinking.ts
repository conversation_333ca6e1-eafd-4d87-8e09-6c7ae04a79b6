// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'
import { collapseBtnMarkup } from './common'

export const juchatsThinking: TokenizerAndRendererExtension = {
  name: 'juchatsThinking',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-thinking>/)?.index
  },
  tokenizer(src: string) {
    // 修改规则以匹配未闭合的标签
    const rule = /^<juchats-thinking>([\s\S]*?)(?:<\/juchats-thinking>|$)/
    const match = rule.exec(src)
    if (match) {
      const token = {
        type: 'juchatsThinking',
        raw: match[0],
        text: match[1].trim(),
        tokens: [],
      }
      this.lexer.blockTokens(token.text, token.tokens)
      return token
    }
  },
  renderer(token: Tokens.Generic) {
    return `<div class='juchats-thinking transition-all duration-100'>
      ${collapseBtnMarkup}
    <div class='juchats-code-wrapper transition-height duration-200'>
    ${this.parser.parse(token.tokens ?? [])}
    </div>
    </div>`
  },
}
