// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const artifactsTweak: TokenizerAndRendererExtension = {
  name: 'artifactsTweak',
  level: 'block',
  start(src: string) {
    return src.match(/<artifacts-tweak>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<artifacts-tweak>([\s\S]*?)(?:<\/artifacts-tweak>|$)/
    const match = rule.exec(src)
    if (match) {
      const token = {
        type: 'artifactsTweak',
        raw: match[0],
        text: match[1].trim(),
      }
      return token
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    try {
      // 解析JSON数据
      const artifactsData = JSON.parse(token.text)

      return `
      <p>${artifactsData.userPrompt}</p>
      <div class="artifacts-tweak card-button overflow-hidden transition-all cursor-default!" data-artifacts-id="${artifactsData.id}">
        <section class="artifacts-card">
          <div class="i-carbon-edit text-14px text-[var(--artifacts-card-desc)]"></div>
        </section>
        <div class="flex-1 pl-10px py-2px flex flex-col justify-between overflow-hidden">
          <div class="text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab text-12px">
            <b>Artifacts Editing : </b>${artifactsData.artifactsTitle}-(ID:${artifactsData.artifactsId})
          </div>
          <div class="mt-10px text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab text-12px">
            <b>Modified Element : </b>
          </div>
          <div class="mt-10px text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab text-12px">
            <el-scrollbar><pre class="c-scrollbar overflow-auto w-100%">${artifactsData.elementHtml.replace(/</g, '&lt;').replace(/>/g, '&gt;').trim()}</pre></el-scrollbar>
          </div>
        </div>
      </div>
      `
    }
    catch (error) {
      console.error('artifactsTweak error', error)
      return token.text
    }
  },
}
