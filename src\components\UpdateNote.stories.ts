import type { <PERSON>a, StoryObj } from '@storybook/vue3'
import UpdateNote from './UpdateNote.vue'

const meta: Meta<typeof UpdateNote> = {
  title: 'component/UpdateNote',
  component: UpdateNote,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  render: () => ({
    components: { UpdateNote },
    template: `
          <div class="fixed top-0 left-0 w-full" style="height: 100vh; display: flex;">
            <div style="width: 300px; display: flex; justify-content: center; align-items: flex-end; padding-bottom: 20px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"><UpdateNote  /></div>
            <div style="flex: 1; padding: 50px; overflow-y: auto;">
                <img  style="max-width: 60%; display: block; margin: 0 auto;" src="/demo.png" alt="random image" />
            </div>
          </div>
        `,
  })
}
