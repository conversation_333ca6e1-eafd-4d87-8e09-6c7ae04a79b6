import { defineStore } from 'pinia'
import { v4 as uuidv4 } from 'uuid'
import type { NotificationItem, NotificationType } from '@/model/common'

interface State {
  notificationList: NotificationItem[]
}

export const useNotificationStore = defineStore('notification', {
  state: (): State => ({
    notificationList: [],
  }),
  actions: {
    closeNotification(value: NotificationItem) {
      clearTimeout(value.timer)
      this.notificationList = this.notificationList.filter((forItem) => {
        return forItem.id !== value.id
      })
    },
    showNotification(value: { type: NotificationType, content: string }) {
      const item: NotificationItem = {
        id: uuidv4(),
        type: value.type,
        content: value.content,
        timer: 1,
      }

      this.notificationList.push(item)
      clearTimeout(item.timer)
      item.timer = setTimeout(() => {
        this.notificationList = this.notificationList.filter((forItem) => {
          return forItem.id !== item.id
        })
      }, 3000)
    },
  },
})
