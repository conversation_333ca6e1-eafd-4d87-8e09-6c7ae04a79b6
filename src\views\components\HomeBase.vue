<template>
  <div class="size-full flex items-center justify-center">
    <DotBg>
      <slot></slot>
    </DotBg>
    <!-- <BaseHeader v-if="isPc" @click-login="logIn" @click-sign-up="signUp"></BaseHeader> -->
    <!-- <BaseFooter v-if="isPc" @toggle-protocol="onToggleProtocol"></BaseFooter> -->
  </div>
  <DarkMessage></DarkMessage>
</template>

<script setup lang="ts">
import BaseHeader from './BaseHeader.vue'
import BaseFooter from './BaseFooter.vue'
import DotBg from './DotBg.vue'
import DarkMessage from './DarkMessage.vue'
import { useAppStore } from '@/stores/app'

const emits = defineEmits<{
  (e: 'clickLogin'): void
  (e: 'clickSignUp'): void
  (e: 'toggleProtocol', showProtocol: boolean): void
}>()
const { isPc } = storeToRefs(useAppStore())
function onToggleProtocol(isShow: boolean) {
  emits('toggleProtocol', isShow)
}
function logIn() {
  emits('clickLogin')
}

function signUp() {
  emits('clickSignUp')
}
</script>

<style lang="scss">
.common-border {
  border: 1px dashed #fff;
  border-radius: 10px;
  position: relative;
  padding: 10px 24px;

  &::before {
    content: '';
    position: absolute;
    pointer-events: none;
    width: 100%;
    height: 100%;
    border: 1px dashed #979797;
    border-radius: 10px;
    left: 8px;
    top: 8px;
  }

  &.message-item {
    border-radius: 5px;
    padding: 15px 20px 10px;

    &::before {
      border-radius: 5px;
    }
  }

}

.link-visual {
  border-bottom: 1px solid transparent;
  transition: 0.3s;

  &:hover {
    border-bottom-color: white;
  }
}
</style>

<style lang="scss" scoped>
.login-page {
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: Kanit;
  color: var(--black);

  a {
    color: var(--black);
  }
}
</style>
