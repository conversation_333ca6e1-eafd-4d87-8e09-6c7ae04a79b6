.side-bar {
  &__container {
      width: 260px;
    height: 100%;
    transition: all 0.3s var(--ani-bezier);
  }
  &--shrink {
    width: 0;
    opacity: 0;
    overflow: hidden;
  }
  &__title {
    font-family: 'Roboto Slab';
    color: #a8afbf;
    font-size: 12px;
    width: 220px;
    margin: auto;
    padding-top: 30px;
    margin-bottom: -10px;
  }
  &__side {
    background: #ffffff;
    height: 100%;
    box-shadow: 10px 2px 20px 0 #00000008;
    position: relative;
    z-index: 10;
    transition: all 0.3s var(--ani-bezier);
  }
  &__tabs {
    font-size: 14px;
    font-weight: 400;
    color: #bcbcbc;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    > div {
      transition: all 0.3s var(--ani-bezier);
    }
    &--active {
      font-weight: 500;
      color: #000000;
    }
    &__block {
      bottom: -5px;
      position: absolute;
      width: 20px;
      height: 5px;
      background: #000000;
      transition: all 0.3s var(--ani-bezier);
    }
    &__right {
      left: 142.5px;
    }
    &__left {
      left: 97.5px;
    }
  }
  &__scroll {
    padding-bottom: 50px;
    height: calc(100% - 200px);
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  &__item {
    width: 220px;
    height: 40px;
    background: #fefefe;
    border-radius: 5px;
    margin: auto;
    margin-top: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    cursor: pointer;
    border: 1px solid #e6e6e6;
    transition: all 0.3s var(--ani-bezier);
    position: relative;
    input {
      width: 100%;
      &:focus {
        pointer-events: none;
      }
    }
    i {
      font-size: 18px;
      padding-right: 10px;
      // padding-top: 3px;
    }
    div:last-child {
      color: #64748b;
    }
    &__child {
      width: 190px;
      margin-right: 20px;
    }
    &__leaf {
      width: 170px;
      margin-right: 20px;
    }
    &__content {
      flex: 1;
      color: #000000 !important;
      word-break: break-all;
      .ri-pencil-line,
      .ri-delete-bin-line {
        display: none;
      }
      .ri-delete-bin-line {
        color: #ff0000 !important;
      }
      &:hover {
        .ri-pencil-line,
        .ri-delete-bin-line {
          display: block;
        }
      }
    }
    &__arrow {
      transition: transform 0.3s var(--ani-bezier);
    }
    &__rotate {
      transform: rotate(180deg);
    }
    &__count {
      font-family: 'Roboto Mono';
    }
    &--dashed {
      border: 1px dashed #e6e6e6;
    }
    &::before {
      opacity: 0;
      content: '';
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      position: absolute;
      left: -1px;
      top: -1px;
      border: 2px solid #000000;
      border-radius: 5px;
      pointer-events: none;
      transition: opacity 0.3s var(--ani-bezier);
    }
    &:hover {
      @extend .side-bar__item--active;
    }
    &--active {
      border: 1px dashed transparent;
      &::before {
        opacity: 1;
      }
    }
    &--control {
      .ri-check-line,
      .ri-close-line {
        color: #64748b;
      }
      .ri-delete-bin-line,
      .ri-pencil-line {
        display: none;
        color: #64748b;
      }
      .ri-delete-bin-line {
        color: #ff0000 !important;
      }
      &:hover {
        .ri-delete-bin-line,
        .ri-pencil-line {
          display: block;
        }
      }
    }
  }
  &__shrink {
    left: 270px;
    top: 50%;
    transform: translateY(calc(-50% - 40px));
    position: absolute;
    cursor: pointer;
    transition: all 0.3s var(--ani-bezier);
    padding: 50px 50px 50px 0;
    > div {
      width: 0.25rem;
      height: 0.75rem;
      transition: all 0.3s var(--ani-bezier);
      background: #0000001a;
      border-radius: 9999px;
    }
    > div:first-child {
      transform: rotate(0deg) translateZ(0px);
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
    > div:last-child {
      transform: rotate(0deg) translateZ(0px);
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }
    &:hover {
      > div {
        background: #000000;
      }
      > div:first-child {
        transform: translateY(0.05rem) rotate(15deg) translateZ(0px);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
      > div:last-child {
        transform: translateY(-0.05rem) rotate(-15deg) translateZ(0px);
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
    &--shrink {
      left: 10px;
      > div:first-child {
        transform: rotate(-15deg) translateZ(0px);
        border-radius: 9999px;
      }
      > div:last-child {
        transform: rotate(15deg) translateZ(0px);
        border-radius: 9999px;
      }
      > div {
        background: #000000;
      }
      &:hover {
        > div:first-child {
          transform: translateY(0.05rem) rotate(-15deg) translateZ(0px);
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 0;
        }
        > div:last-child {
          transform: translateY(-0.05rem) rotate(15deg) translateZ(0px);
          border-top-left-radius: 0;
          border-top-right-radius: 0;
        }
      }
    }
  }
  &__equity {
    position: absolute;
    width: 240px;
    height: 50px;
    padding: 10px 18px;
    border-radius: 6px;
    z-index: 1;
    bottom: 90px;
    left: 10px;
    font-family: 'Roboto Slab';
    color: #000000;
    transition: all 0.3s var(--ani-bezier);
    &__mark {
      font-size: 20px;
    }
    &--shrink {
      width: 100px;
      height: 50px;
      left: 10px;
      background: #fbfaf9;
      .side-bar__equity__content {
        width: 0;
      }
    }
    &__content {
      overflow: hidden;
      width: 160px;
    }
    &__info {
      white-space: nowrap;
      span:nth-child(1) {
        font-size: 14px;
        color: #ff0000;
      }
      span:nth-child(2) {
        font-size: 14px;
        color: #2aa50d;
      }
      span:nth-child(3) {
        font-size: 14px;
        color: #e3a100;
      }
      span:nth-child(4) {
        font-size: 14px;
        cursor: pointer;
      }
    }
    &__date {
      font-size: 14px;
      color: #9a9a9a;
    }
    &--hover {
      background: #ffffff;
      position: absolute;
      width: 240px;
      bottom: 45px;
      border-radius: 6px;
      border: 1px solid #efefef;
      box-shadow: 0 5px 20px 0 #00000012;
    }
    &__title {
      border-bottom: 1px solid #efefef;
      padding: 15px 20px;
      padding-left: 0;
      margin-left: 20px;
      font-size: 16px;
      font-weight: 600;
    }
    &__list {
      padding: 20px;
      padding-top: 10px;
      font-size: 13px;
      line-height: 35px;
      color: #373737;
      font-family: 'Roboto Slab';
      > div {
        div:last-child {
          color: #373737;
        }
      }
    }
    &:hover {
      background: #fbfaf9;
    }
  }
}
