/**
 * 展示模型类型
 * @description 例如：GPT Forte 展示类型为 0，GPT 3.5 展示类型为 1
 * @tips 还有一个modeId，modeId 是具体模型，modeId是 type 的子集
 */
export enum MODE_TYPE {
  /** GPTTEST */
  V4 = 0,
  /** GPT Piano */
  GPT_3_5 = 1,
  /** GPT Mezzo */
  GPT_4_0 = 2,
  /** DALL·E 3 */
  E3 = 3,
  /** Mistral Forte */
  GROQ = 5,
  /** Claude3 Opus(200K) */
  CLAUDE3 = 6,
  /** Claude 3.5 Haiku */
  CLAUDE_DYNAMICS_PREVIEW = 7,
  /** Llama3 70B */
  LLAMA3 = 8,
  /** Gemini */
  GEMINI = 9,
  /** DeepSeek */
  DEEPSEEK = 10,
  /** DeepSeek R1 */
  DEEPSEEKR1 = 26,
  /** GPT4O */
  GPT4O = 11,
  /** Claude 3 100K */
  CLAUDE3100 = 12,
  /** Stable */
  STABLE = 13,
  /** Gemini Flash */
  GEMINI_FLASH = 14,
  /** Claude 3.5 Sonnet */
  SONNET = 15,
  /** GPT4O Mini */
  GPT4OMINI = 16,
  /** Llama3.1 405B */
  LLAMA405B = 17,
  /** Gemini 0801 */
  GEMINI_0801 = 18,
  /** Flux Pro */
  FLUXPRO = 19,
  /** Ideogram */
  IDEOGRAM = 20,
  /** O1 Preview */
  O1PREVIEW = 21,
  /** O1 Mini */
  O1MINI = 22,
  /** O3 Mini */
  O3MINI = 27,
  /** Meta-Llama-3.1-8B-Instruct */
  LEPTON_AUDIO = 23,
  /** Artifacts */
  ARTIFACTS = 25,
  /** Gemini Exp */
  GEMINI_EXP = 28,
  /** Gemini 2.0 */
  GEMINI20 = 29,
  /** Grok */
  GROK3 = 30,
  /** GPT 4.1 */
  GPT_4_1 = 31,
  /** GPT 4.1 Mini */
  GPT_4_1_MINI = 32,
  /** GPT 4.1 Nano */
  GPT_4_1_NANO = 33,
  GPT_O3 = 34,
  GPT_04_MINI = 35,
  GPT_IMAGE = 36,
  SONNET_4_20250514 = 37,
  OPUS_4_20250514 = 38,
  FLUX_KONTEXT_PRO = 39,
  KIMI_K2_PREVIEW = 40,
  ZHIPU_GLM_4_5 = 41,
  QWEN_3 = 99,
}

export const SCAN_IMAGE_MODES = [
  MODE_TYPE.V4,
  MODE_TYPE.CLAUDE_DYNAMICS_PREVIEW,
  MODE_TYPE.GPT4O,
  MODE_TYPE.SONNET,
  MODE_TYPE.GPT4OMINI,
  MODE_TYPE.GEMINI_0801,
  MODE_TYPE.ARTIFACTS,
  MODE_TYPE.GEMINI_EXP,
  MODE_TYPE.GEMINI20,
  MODE_TYPE.GPT_4_1,
  MODE_TYPE.GPT_4_1_MINI,
  MODE_TYPE.GPT_4_1_NANO,
  MODE_TYPE.GPT_O3,
  MODE_TYPE.GPT_04_MINI,
  MODE_TYPE.GPT_IMAGE,
  MODE_TYPE.QWEN_3,
  MODE_TYPE.SONNET_4_20250514,
  MODE_TYPE.OPUS_4_20250514,
  MODE_TYPE.DEEPSEEKR1,
  MODE_TYPE.FLUX_KONTEXT_PRO,
  MODE_TYPE.KIMI_K2_PREVIEW,
  MODE_TYPE.ZHIPU_GLM_4_5,
]
export const GENERATE_IMAGE_MODES = [
  MODE_TYPE.E3,
  MODE_TYPE.STABLE,
  MODE_TYPE.FLUXPRO,
  MODE_TYPE.IDEOGRAM,
  MODE_TYPE.GEMINI20,
  MODE_TYPE.GPT_IMAGE,
  MODE_TYPE.FLUX_KONTEXT_PRO,
]
export const LLMS_MODES = [
  MODE_TYPE.GPT_3_5,
  MODE_TYPE.GPT_4_0,
  MODE_TYPE.GROQ,
  MODE_TYPE.V4,
  MODE_TYPE.CLAUDE_DYNAMICS_PREVIEW,
  MODE_TYPE.CLAUDE3,
  MODE_TYPE.LLAMA3,
  MODE_TYPE.GEMINI,
  MODE_TYPE.GEMINI_FLASH,
  MODE_TYPE.DEEPSEEK,
  MODE_TYPE.DEEPSEEKR1,
  MODE_TYPE.GPT4O,
  MODE_TYPE.CLAUDE3100,
  MODE_TYPE.SONNET,
  MODE_TYPE.GPT4OMINI,
  MODE_TYPE.LLAMA405B,
  MODE_TYPE.O1PREVIEW,
  MODE_TYPE.O1MINI,
  MODE_TYPE.O3MINI,
  MODE_TYPE.LEPTON_AUDIO,
  MODE_TYPE.ARTIFACTS,
  MODE_TYPE.GEMINI_EXP,
  MODE_TYPE.GEMINI20,
  MODE_TYPE.GROK3,
  MODE_TYPE.GPT_4_1,
  MODE_TYPE.GPT_4_1_MINI,
  MODE_TYPE.GPT_4_1_NANO,
  MODE_TYPE.GPT_O3,
  MODE_TYPE.GPT_04_MINI,
  MODE_TYPE.SONNET_4_20250514,
  MODE_TYPE.OPUS_4_20250514,
  MODE_TYPE.QWEN_3,
  MODE_TYPE.KIMI_K2_PREVIEW,
  MODE_TYPE.ZHIPU_GLM_4_5,
]
export const LABS_MODES = [
  MODE_TYPE.E3,
  MODE_TYPE.LLAMA3,
  MODE_TYPE.GEMINI,
  MODE_TYPE.DEEPSEEK,
  MODE_TYPE.DEEPSEEKR1,
  MODE_TYPE.GPT4O,
  MODE_TYPE.CLAUDE3100,
  MODE_TYPE.STABLE,
  MODE_TYPE.GEMINI_FLASH,
  MODE_TYPE.SONNET,
  MODE_TYPE.GPT4OMINI,
  MODE_TYPE.LLAMA405B,
  MODE_TYPE.GEMINI_0801,
  MODE_TYPE.FLUXPRO,
  MODE_TYPE.IDEOGRAM,
  MODE_TYPE.O1PREVIEW,
  MODE_TYPE.O1MINI,
  MODE_TYPE.O3MINI,
  MODE_TYPE.GEMINI_EXP,
  MODE_TYPE.GEMINI20,
]

export const IMAGE_SPLIT = '--IMAGES-SPLIT--'

export enum LOGIN_TYPE {
  Email = 1,
  Google = 2,
  Github = 3,
}

export enum IMAGE_SIZE {
  BIG,
  SMALL,
}
