// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'
import artifactsCodeImage from '@/assets/images/artifacts-code.png'

interface ArtifactsData {
  title: string
  description: string
  index: number
}

export const juchatsArtifacts: TokenizerAndRendererExtension = {
  name: 'juchatsArtifacts',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-artifacts>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-artifacts>([\s\S]*?)(?:<\/juchats-artifacts>|$)/
    const match = rule.exec(src)
    if (match) {
      const token = {
        type: 'juchatsArtifacts',
        raw: match[0],
        text: match[1].trim(),
      }
      return token
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    try {
      // 解析JSON数据
      const artifactsData: ArtifactsData = JSON.parse(token.text)

      return `
      <div class="card-button" onclick="(function(el) { window.dispatchEvent(new CustomEvent('show-artifacts-html', { detail: { ...${JSON.stringify(artifactsData).replace(/"/g, '&quot;')}, element: el } })); })(this)">
        <section class="artifacts-card">
          <img
            src="${artifactsCodeImage}"
            class="w-16px aspect-ratio-16/12"
            alt=""
            style="cursor: pointer;"
            data-juchats="true"
          />
        </section>
        <div class="flex-1 pl-10px py-2px flex flex-col justify-between">
          <div class="text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab text-12px">
            ${artifactsData.title}
          </div>

          <div class="text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab text-12px">
            Click to see fragment
          </div>
        </div>
      </div>
      `
    }
    catch {
      return ''
    }
  },
}
