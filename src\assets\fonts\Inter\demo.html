<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, noarchive">
    <meta name="format-detection" content="telephone=no">
    <title>Transfonter demo</title>
    <link href="stylesheet.css" rel="stylesheet">
    <style>
        /*
        http://meyerweb.com/eric/tools/css/reset/
        v2.0 | 20110126
        License: none (public domain)
        */
        html, body, div, span, applet, object, iframe,
        h1, h2, h3, h4, h5, h6, p, blockquote, pre,
        a, abbr, acronym, address, big, cite, code,
        del, dfn, em, img, ins, kbd, q, s, samp,
        small, strike, strong, sub, sup, tt, var,
        b, u, i, center,
        dl, dt, dd, ol, ul, li,
        fieldset, form, label, legend,
        table, caption, tbody, tfoot, thead, tr, th, td,
        article, aside, canvas, details, embed,
        figure, figcaption, footer, header, hgroup,
        menu, nav, output, ruby, section, summary,
        time, mark, audio, video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
            vertical-align: baseline;
        }
        /* HTML5 display-role reset for older browsers */
        article, aside, details, figcaption, figure,
        footer, header, hgroup, menu, nav, section {
            display: block;
        }
        body {
            line-height: 1;
        }
        ol, ul {
            list-style: none;
        }
        blockquote, q {
            quotes: none;
        }
        blockquote:before, blockquote:after,
        q:before, q:after {
            content: '';
            content: none;
        }
        table {
            border-collapse: collapse;
            border-spacing: 0;
        }
        /* demo styles */
        body {
            background: #f0f0f0;
            color: #000;
        }
        .page {
            background: #fff;
            width: 920px;
            margin: 0 auto;
            padding: 20px 20px 0 20px;
            overflow: hidden;
        }
        .font-container {
            overflow-x: auto;
            overflow-y: hidden;
            margin-bottom: 40px;
            line-height: 1.3;
            white-space: nowrap;
            padding-bottom: 5px;
        }
        h1 {
            position: relative;
            background: #444;
            font-size: 32px;
            color: #fff;
            padding: 10px 20px;
            margin: 0 -20px 12px -20px;
        }
        .letters {
            font-size: 25px;
            margin-bottom: 20px;
        }
        .s10:before {
            content: '10px';
        }
        .s11:before {
            content: '11px';
        }
        .s12:before {
            content: '12px';
        }
        .s14:before {
            content: '14px';
        }
        .s18:before {
            content: '18px';
        }
        .s24:before {
            content: '24px';
        }
        .s30:before {
            content: '30px';
        }
        .s36:before {
            content: '36px';
        }
        .s48:before {
            content: '48px';
        }
        .s60:before {
            content: '60px';
        }
        .s72:before {
            content: '72px';
        }
        .s10:before, .s11:before, .s12:before, .s14:before,
        .s18:before, .s24:before, .s30:before, .s36:before,
        .s48:before, .s60:before, .s72:before {
            font-family: Arial, sans-serif;
            font-size: 10px;
            font-weight: normal;
            font-style: normal;
            color: #999;
            padding-right: 6px;
        }
        pre {
            display: block;
            padding: 9px;
            margin: 0 0 12px;
            font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
            font-size: 13px;
            line-height: 1.428571429;
            color: #333;
            font-weight: normal;
            font-style: normal;
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            overflow-x: auto;
            border-radius: 4px;
        }
        /* responsive */
        @media (max-width: 959px) {
            .page {
                width: auto;
                margin: 0;
            }
        }
    </style>
</head>
<body>
<div class="page">
    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 900; font-style: normal;">Inter Black</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 900;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Black.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 900; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: bold; font-style: normal;">Inter ExtraBold</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: bold;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-ExtraBold.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: bold; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: bold; font-style: normal;">Inter Bold</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: bold;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Bold.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: bold; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 500; font-style: normal;">Inter Medium</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 500;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Medium.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 500; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 200; font-style: normal;">Inter ExtraLight</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 200;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-ExtraLight.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 200; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 300; font-style: normal;">Inter Light</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 300;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Light.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 300; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 600; font-style: normal;">Inter SemiBold</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 600;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-SemiBold.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 600; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: 100; font-style: normal;">Inter Thin</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: 100;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Thin.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: 100; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

    <div class="demo">
        <h1 style="font-family: 'Inter'; font-weight: normal; font-style: normal;">Inter Regular</h1>
        <pre title="Usage">.your-style {
    font-family: 'Inter';
    font-weight: normal;
    font-style: normal;
}</pre>
        <pre title="Preload (optional)">
&lt;link rel=&quot;preload&quot; href=&quot;Inter-Regular.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;</pre>
        <div class="font-container" style="font-family: 'Inter'; font-weight: normal; font-style: normal;">
            <p class="letters">
                abcdefghijklmnopqrstuvwxyz<br>
ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
                0123456789.:,;()*!?'@#&lt;&gt;$%&^+-=~
            </p>
            <p class="s10" style="font-size: 10px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s11" style="font-size: 11px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s12" style="font-size: 12px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s14" style="font-size: 14px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s18" style="font-size: 18px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s24" style="font-size: 24px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s30" style="font-size: 30px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s36" style="font-size: 36px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s48" style="font-size: 48px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s60" style="font-size: 60px;">The quick brown fox jumps over the lazy dog.</p>
            <p class="s72" style="font-size: 72px;">The quick brown fox jumps over the lazy dog.</p>
        </div>
    </div>

</div>
</body>
</html>
