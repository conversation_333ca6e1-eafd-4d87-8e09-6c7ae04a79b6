// @unocss-include
import { useAppStore } from '@/stores/app'

export function useModalPosition() {
  const { isPc, expandSidebar } = storeToRefs(useAppStore())

  // 用于控制dialog组件的样式，返回style对象
  const modalStyle = computed(() => {
    return {
      left: (expandSidebar.value && isPc.value) ? '260px' : '0px',
      transition: 'all 0.3s var(--ani-bezier)',
    }
  })

  // 专门用于dialog组件，用于控制dialog组件的left值，返回unocss的class
  const modalLeftClass = computed(() => {
    return expandSidebar.value ? 'left-[260px]!' : 'left-0!'
  })

  return {
    modalStyle,
    modalLeftClass,
  }
}
