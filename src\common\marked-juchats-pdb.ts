// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-pdb>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    return `<button
      class="juchats-pdb-btn"
      onclick="console.log('PDB ID: ${pdbId}')"
    >
      <span>🧬</span>
      <span>${pdbId}</span>
    </button>`
  },
}
