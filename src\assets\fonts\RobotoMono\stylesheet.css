@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Italic.woff2') format('woff2'),
        url('RobotoMono-Italic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-ExtraLightItalic.woff2') format('woff2'),
        url('RobotoMono-ExtraLightItalic.woff') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Medium.woff2') format('woff2'),
        url('RobotoMono-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-MediumItalic.woff2') format('woff2'),
        url('RobotoMono-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Light.woff2') format('woff2'),
        url('RobotoMono-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-ExtraLight.woff2') format('woff2'),
        url('RobotoMono-ExtraLight.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-BoldItalic.woff2') format('woff2'),
        url('RobotoMono-BoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-LightItalic.woff2') format('woff2'),
        url('RobotoMono-LightItalic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Bold.woff2') format('woff2'),
        url('RobotoMono-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Regular.woff2') format('woff2'),
        url('RobotoMono-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-SemiBoldItalic.woff2') format('woff2'),
        url('RobotoMono-SemiBoldItalic.woff') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-Thin.woff2') format('woff2'),
        url('RobotoMono-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-ThinItalic.woff2') format('woff2'),
        url('RobotoMono-ThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto Mono';
    src: url('RobotoMono-SemiBold.woff2') format('woff2'),
        url('RobotoMono-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

