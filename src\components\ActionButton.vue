<template>
  <div class="bg-destructive-300 h-42px flex cursor-pointer items-center justify-center border-rd-6px text-sm text-[#fff] shadow-[0_10px_30px_0_var(--d3)]">
    <button class="flex items-center">
      <i v-if="loading" class="i-ri-donut-chart-fill rotate mr-2 text-20px transition-cusbezier-300" />
      <slot />
    </button>
  </div>
</template>

<script setup lang="ts">
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
</style>
