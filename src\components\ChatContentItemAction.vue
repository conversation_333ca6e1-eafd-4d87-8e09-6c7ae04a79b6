<template>
  <!-- 只有最后一条AI消息，且AI消息没有在生成时，才展示工具栏 -->
  <!-- 也就是控制AI输出的时候不显示工具栏，AI输出往往在最后一条 -->
  <div
    v-if="!(item.receiving && loading && index === sections.length - 1)"
    border="~ transparent lt-md:none"
    class="mt-16px h-40px w-320px flex-y-c gap-10px rounded-10px px-13px transition-cusbezier-300 -translate-x-13px hover:(border-#efefef dark:border-#363636)"
  >
    <el-tooltip
      effect="customized"
      :content="$t('imageGallery.copyText')"
      placement="bottom"
      :hide-after="0"
    >
      <button
        v-if="copyVisible"
        class="chat_item-actions-btn i-ju-copy-chat-text"
        @click="emits('copyItem')"
      >
      </button>
    </el-tooltip>
    <el-tooltip
      v-if="editorVisible"
      effect="customized"
      :content="$t('chatContent.editorItem')"
      placement="bottom"
      :hide-after="0"
    >
      <button
        class="chat_item-actions-btn i-ju-chat-editor-text"
        @click="emits('editorItem')"
      >
      </button>
    </el-tooltip>

    <el-tooltip
      v-if="!item.receiving"
      effect="customized"
      :content="$t('chatContent.toggleRenderMode')"
      placement="bottom"
      :hide-after="0"
    >
      <button class="chat_item-actions-btn" @click="emits('toggleRenderMode')">
        <div class="i-ju-chat-format"></div>
      </button>
    </el-tooltip>

    <template v-if="item.receiving">
      <el-tooltip
        effect="customized"
        :content="`${$t('imageGallery.copy')} Markdown`"
        placement="bottom"
        :hide-after="0"
      >
        <button
          v-if="copyVisible"
          class="chat_item-actions-btn i-ju-copy-chat-markdown"
          @click="emits('copyAnswer')"
        >
        </button>
      </el-tooltip>

      <el-tooltip
        v-if="regenerateVisible"
        effect="customized"
        :content="$t('chatContent.regenerate')"
        placement="bottom"
        :hide-after="0"
      >
        <button
          class="chat_item-actions-btn i-ju-chat-regenerate"
          @click="emits('resend')"
        >
        </button>
      </el-tooltip>

      <div v-if="markMapVisible" class="flex-c">
        <MarkMap
          :content="markMapContent"
          :unionid="item.unionid"
          @closed="emits('closeMindMap')"
        >
          <template #button="{ openMindMap }">
            <el-tooltip
              effect="customized"
              :content="$t('markMap.title')"
              placement="bottom"
              :hide-after="0"
              :disabled="!isPc"
            >
              <button
                class="chat_item-actions-btn i-ju-chat-xmind"
                @click="() => handleOpenMindMap(openMindMap)"
              >
              </button>
            </el-tooltip>
          </template>
        </MarkMap>
      </div>

      <el-tooltip
        v-if="contentAnalyzeVisible"
        effect="customized"
        :content="$t('sideBar.contentAnalyze')"
        placement="bottom"
        :disabled="!isPc"
      >
        <button
          class="chat_item-actions-btn i-ju-chat-browsing"
          @click="handleOpenContentAnalyze"
        >
        </button>
      </el-tooltip>

      <el-tooltip
        v-if="deleteVisible"
        effect="customized"
        :content="$t('sideBar.remove')"
        placement="bottom"
      >
        <button
          class="chat_item-actions-btn i-ju-chat-delete"
          @click="emits('deleteChat')"
        >
        </button>
      </el-tooltip>

      <!-- 模型选择菜单 -->
      <ModelSelectMenu
        v-if="regenerateVisible && currentIsNotVisionModel"
        :model-data="modelData"
        @select-model="handleModelSelect"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { GENERATE_IMAGE_MODES } from '@/enum'
import { useAppStore } from '@/stores/app'
import ModelSelectMenu from './ModelSelectMenu.vue'

const props = defineProps<{
  item: any
  index: number
  loading: boolean
  sections: any[]
  copyVisible: boolean
  regenerateVisible: boolean
  markMapVisible: boolean
  deleteVisible: boolean
  contentAnalyzeVisible: boolean
  editorVisible: boolean
  markMapContent: any
  isContentAnalyzePanelVisible: boolean // 新增 prop
}>()

const emits = defineEmits<{
  (e: 'copyItem'): void
  (e: 'copyAnswer'): void
  (e: 'resend'): void
  (e: 'deleteChat'): void
  (e: 'closeMindMap'): void
  (e: 'resendOtherModel', id: number): void
  (e: 'editorItem'): void
  (e: 'contentAnalyze'): void
  (e: 'closeContentAnalyze'): void // 新增 emit
  (e: 'toggleRenderMode'): void
}>()

const appStore = useAppStore()
const { isPc, modelData, selectedMode, artifactsHtmlVisible } = storeToRefs(appStore)

const currentIsNotVisionModel = computed(() => {
  return !GENERATE_IMAGE_MODES.includes(selectedMode.value?.type || 0)
})

// 处理模型选择
const handleModelSelect = (model: any) => {
  emits('resendOtherModel', model.id)
}

// 打开思维导图的处理函数
const handleOpenMindMap = (openMindMapFn: () => void) => {
  if (props.isContentAnalyzePanelVisible) {
    emits('closeContentAnalyze')
  }
  artifactsHtmlVisible.value = false
  openMindMapFn()
}

// 打开内容分析的处理函数
const handleOpenContentAnalyze = () => {
  emits('closeMindMap')
  artifactsHtmlVisible.value = false
  emits('contentAnalyze')
}
</script>

<style lang="scss" scoped>
.chat_item-actions-btn {
  @apply size-16px flex-c text-16px transition-cusbezier-250 text-#A8AFBF dark:(text-#363636 hover:text-#F1F1F1) hover:(translate-y-[-1px] text-#000);
}
</style>
