<template>
  <div class="absolute bottom-[calc(100%+15px)] left-0 w-full flex flex-col">
    <Transition leave-active-class="animate__animated animate__fadeOut">
      <div v-if="imagesPreviewList.length > 0" class="flex">
        <TransitionGroup
          leave-active-class="animate__animated animate__flipOutX"
        >
          <div v-for="item in imagesPreviewList" :key="item.uid" class="relative mr-15px mt-10px">
            <SendAttachmentImageItem
              v-if="getFileShowType(item.name) === FILE_TYPE.IMAGE"
              :class="[imagesPreviewList.length === 1 ? '!h-6.25rem !w-6.25rem' : '!h-4rem !w-4rem']"
              :file="item"
              :size="imagesPreviewList.length > 1 ? IMAGE_SIZE.SMALL : IMAGE_SIZE.BIG"
            >
            </SendAttachmentImageItem>
            <!-- remove icon start -->
            <div
              v-if="item.status !== 'uploading' && item.status !== 'ready'"
              w="18px"
              h="18px"
              text="[var(--attachment-close-button-icon)]"
              bg="[var(--attachment-close-button-bg)]"
              class="absolute z-2 box-border flex-c cursor-pointer rounded-50% lh-1em -right-5px -top-5px"
              @click="removeAttachmentByFileUid(item.uid)"
            >
              <i class="i-mingcute-close-fill text-8px text-[#A8AFBF]" />
            </div>
          <!-- remove icon end -->
          </div>
        </TransitionGroup>
      </div>
    </Transition>
    <Transition leave-active-class="animate__animated animate__fadeOut">
      <div v-if="nonImagesPreviewList.length > 0" class="grid cols-2 w-full lt-sm:(cols-1)">
        <TransitionGroup
          leave-active-class="animate__animated animate__flipOutX"
        >
          <div v-for="item in nonImagesPreviewList" :key="item.uid" class="relative mr-15px mt-10px">
            <SendAttachmentFileItem
              :file="item"
            >
            </SendAttachmentFileItem>
            <!-- remove icon start -->
            <div
              v-if="item.status !== 'uploading' && item.status !== 'ready'"
              w="18px"
              h="18px"
              text="[var(--attachment-close-button-icon)]"
              bg="[var(--attachment-close-button-bg)]"
              class="absolute z-2 box-border flex-c cursor-pointer rounded-50% lh-1em -right-5px -top-5px"
              @click="removeAttachmentByFileUid(item.uid)"
            >
              <i class="i-mingcute-close-fill text-8px text-[#A8AFBF]" />
            </div>
            <!-- remove icon end -->
          </div>
        </TransitionGroup>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { getFileCategory, getFileShowType } from '@/common/tools'
import { IMAGE_SIZE } from '@/enum'
import { FILE_CATEGORY, FILE_TYPE } from '@/model/common'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
const { attachmentsPreviewList } = storeToRefs(appStore)
const { removeAttachmentByFileUid } = appStore
const imagesPreviewList = computed(() => {
  return attachmentsPreviewList.value.filter((item) => {
    return getFileCategory(item.name) === FILE_CATEGORY.IMAGE
  })
})
const nonImagesPreviewList = computed(() => {
  return attachmentsPreviewList.value.filter((item) => {
    return getFileCategory(item.name) !== FILE_CATEGORY.IMAGE
  })
})
</script>

<style scoped lang="scss">

</style>
