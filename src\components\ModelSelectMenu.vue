<template>
  <div ref="menuRef" class="relative">
    <!-- 触发按钮 -->
    <button
      class="chat_item-actions-btn flex items-center text-[var(--el-text-color-regular)]"
      @click="toggleMenu"
    >
      <span class="text-13px">{{ selectedMode?.showName || '-' }}</span>
      <i class="i-ri-arrow-down-s-line text-16px"></i>
    </button>

    <Transition name="el-zoom-in-top">
      <div
        v-if="menuVisible"
        class="absolute left-0 top-[calc(100%+10px)] z-50 w-150px rounded-5px bg-white px-10px py-14px shadow-[0_0_30px_0_#0000000f] dark:bg-#171717 dark:shadow-xl"
      >
        <div
          v-for="(models, provider) in filteredModels"
          :key="provider"
          class="w-full flex-y-c cursor-pointer rounded-3px text-12px hover:bg-#f5f5f5 dark:hover:(bg-#E7E9ED)"
        >
          <el-dropdown
            placement="right"
            class="w-full font-Lexend dark:hover:text-#000"
            :show-timeout="0"
            @command="selectModel"
          >
            <div class="w-full flex-y-c justify-between px-10px py-8px text-12px">
              <div>{{ formatProviderName(provider) }}</div>
              <i class="i-ri-arrow-right-s-line text-16px"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="group_dropdown font-Lexend">
                <el-dropdown-item v-for="model in models" :key="model.id" :command="model">
                  {{ model.showName }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

const props = defineProps({
  modelData: {
    type: Object as PropType<ModelData>,
    required: true,
  },
})
const emit = defineEmits(['selectModel'])

interface ModelItem {
  id: number
  showName: string
  maxToken: number
  remark: string | null
}

interface ModelData {
  [provider: string]: ModelItem[]
}

const { selectedMode } = storeToRefs(useAppStore())

// 菜单状态
const menuRef = ref(null)
const menuVisible = ref(false)
const activeProvider = ref<string | null>(null)

// 点击外部关闭菜单
onClickOutside(menuRef, () => {
  setTimeout(() => {
    menuVisible.value = false
  }, 150)
})

// 切换菜单显示状态
const toggleMenu = () => {
  menuVisible.value = !menuVisible.value
}

const setContainerPadding = (open: boolean) => {
  if (window.innerWidth > 768) {
    return
  }

  const target = menuRef.value! as HTMLDivElement
  const menuContainer = target.closest('.answer-content')
  if (menuContainer) {
    menuContainer.classList.toggle('open-model-select', open)
  }
  else {
    console.warn('Menu container not found')
  }
}

watch(menuVisible, (newVal) => {
  if (!newVal) {
    activeProvider.value = null
  }

  setContainerPadding(newVal)
})

// 过滤掉当前选中的模型，并且如果某个供应商下只有一个模型且被过滤掉，则整个供应商也不显示
const filteredModels = computed(() => {
  const result: Record<string, ModelItem[]> = {}
  const excludedProviders = ['VISION'] // 需要排除的提供者列表

  if (!props.modelData || !selectedMode.value) {
    return result
  }

  const currentModelId = selectedMode.value.id

  Object.entries(props.modelData)
    .filter(([provider]) => !excludedProviders.includes(provider)) // 过滤掉排除列表中的供应商
    .forEach(([provider, models]) => {
      // 过滤掉当前选中的模型
      const filteredModels = models.filter(model => model.id !== currentModelId)

      // 如果过滤后还有模型，则添加到结果中
      if (filteredModels.length > 0) {
        result[provider] = filteredModels
      }
    })

  return result
})

// 选择模型
const selectModel = (model: ModelItem) => {
  setContainerPadding(false)
  emit('selectModel', model)
  menuVisible.value = false
}

// 格式化 Provider 名称
const formatProviderName = (provider: string): string => {
  if (provider) {
    return provider.charAt(0).toUpperCase() + provider.slice(1).toLowerCase()
  }
  return provider
}
</script>

<style lang="scss" scoped>
.group_dropdown {
  :deep(.el-dropdown-menu__item) {
    background: transparent !important;
    font-size: 13px;
    color: var(--el-text-color-regular);
    margin-right: 15px;
    transition: all 0.2s var(--ani-bezier);

    &:hover {
      font-weight: 600;
    }
  }
}
</style>
