export interface PackageItemModel {
  [key: string]: any
  actDescription: string
  actExpireTime: string
  actName: string
  actNewDiscountPercent: number
  actOldDiscountPercent: number
  actStatus: string
  actType: number
  activityCount: number
  activityId: number
  cashbackRate: number
  claudeMezzoPreview: number
  claudeOpus: number
  deepseek: number
  description: string
  dialogLimit: number
  e3: number
  expireDays: number | string
  gemini: number
  gptFortePreview: number
  gptMezzoPreview: number
  gptPianoPreview: number
  groupDialogLimit: number
  groupLimit: number
  id: number
  images: object[]
  metaLlama: number
  mixtralFortePreview: number
  period: number
  price: number
  startGroupDialogLimit: number
  title: string
  voice: number
  // ====
  status: string
  orderNo: string
  expireTime: string
  deductionAmt: number
  commissionAmt: number
  discountPirce: number
  paymentIdentificationText: string
}

export interface UnpaidItemModel {
  commissionAmt: number
  createTime: string
  deductionAmt: number
  expireTime: string
  fromFlag: number
  id: number
  orderNo: string
  packageId: number
  paymentIdentificationText: string
  price: number
  status: number
  successTime: string
  type: number
}

export interface ModeItemModel {
  key?: string
  label: string
  visible?: number[]
  children?: ModeChildModel[]
}

export interface ModeChildModel {
  label: string
  key: string
  basicTimes: number | string
  plusTimes: number | string
  perimumTimes: number | string
}
