<script setup lang="ts">
import ThemeTransition from '@/components/ThemeTransition.vue'

import { cssBreakPoints } from '@/config/cssBreakPoints'
import { useThemeChange } from '@/hooks/useThemeChange'
import { useThemeStore } from '@/stores/theme'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { RouterView } from 'vue-router'

const breakpoints = useBreakpoints(cssBreakPoints)
const isPc = breakpoints.greaterOrEqual('md')

const themeStore = useThemeStore()
const { showThemeTransition } = storeToRefs(themeStore)

const preferredDark = usePreferredDark()
const { system: uiColorMode } = useColorMode()
const favicon = computed(() => preferredDark.value ? '/favicon-dark.svg' : '/favicon-light.svg')
useFavicon(favicon)

const { setTheme, isDark } = useThemeChange()

// TODO: 现在利用电脑操作系统切换主题，会导致主题从「跟随系统」切换到对应的颜色，而不是保持「跟随系统」
watch(uiColorMode, (newVal) => {
  setTheme(newVal)
})
</script>

<template>
  <el-config-provider :locale="zhCn" :z-index="20000">
    <transition name="el-fade-in-linear">
      <ThemeTransition
        v-show="showThemeTransition && isPc"
        :current-is-dark="isDark"
      />
    </transition>
    <!-- vshow互斥条件，主题切换动画的时候，不显示内容以节约性能 -->
    <RouterView v-show="!(showThemeTransition && isPc)" v-slot="{ Component }">
      <div class="bg-[var(--app-bg)] lt-lg:(max-h-[100%] h-full max-w-100dvw overflow-hidden)" :class="'Share' === $route.name ? '' : 'app-view flex'">
        <component :is="Component" class="flex-1" />
      </div>
    </RouterView>
  </el-config-provider>
</template>
