import * as Sentry from '@sentry/vue'
import axios from 'axios'

const request = axios.create({
  baseURL: '/api/v1',
})

request.interceptors.request.use((config) => {
  config.headers.Jtoken = localStorage.getItem('token')
  return config
})

const track = (event: string, data: Record<string, any> = {}) => {
  if (import.meta.env.DEV) {
    return
  }

  request.get('/track/event', {
    params: {
      event_name: event,
      properties: JSON.stringify(data),
    },
  })
}

export const mixpanel = {
  initUser({ email, name, avatar }: { email: string, name: string, avatar: string }) {
    if (import.meta.env.DEV) {
      return
    }

    const fullAvatar = new URL(avatar || 'favicon.ico', import.meta.env.VITE_APP_IMAGE_URL || location.origin).href
    request.get('/track/init', {
      params: {
        $name: name,
        $email: email,
        $avatar: fullAvatar,
        $browserLanguage: navigator.language,
      },
    })

    Sentry.setUser({
      email,
      name,
      avatar: fullAvatar,
    })
  },
  // 创建对话
  createDialog() {
    track('Create Dialog')
  },
  // 切换到常规对话
  regularDialog() {
    track('Regular Dialog')
  },
  // 切换到分组对话
  groupDialog() {
    track('Group Dialog')
  },
  // 打开彩蛋兑换界面
  eggExchange() {
    track('Egg Exchange')
  },
  // 开始兑换彩蛋
  eggExchangeStart() {
    track('Egg Exchange Start')
  },
  // 我的邀请
  myInvite() {
    track('My Invite')
  },
  // 套餐详情
  packageDetail() {
    track('Package Detail')
  },
  // 我的资料
  myProfile() {
    track('My Profile')
  },
  // 帮助中心
  helpCenterHelp() {
    track('Help Center Help')
  },
  // 安全退出
  logout() {
    track('Logout')
  },
  // 亮色主题
  lightTheme() {
    track('Light Theme')
  },
  // 暗黑主题
  darkTheme() {
    track('Dark Theme')
  },
  // 主题跟随系统
  followSystem() {
    track('Follow System')
  },
  // 打开选择模型面板
  openSelectModelPanel() {
    track('Open select Model Panel')
  },
  // 选择模型
  selectModel(model: string) {
    track('Select Model', {
      model,
    })
  },
  // 发送一条 prompt
  sendPromptMessage(model: string, requestId: string) {
    track('Send Prompt Message', {
      model,
      requestId,
    })
  },
  // 星标对话
  favoriteChat(type: number) {
    const saved = +type === 1
    track(`${saved ? 'Save' : 'Cancel'} Favorite Chat`, {
      note: '星标对话',
    })
  },
  // 创建分享
  createShareChat(link: string) {
    track('Create share link', {
      shareLink: link,
    })
  },
  // 复制分享链接
  copyShareChat(link: string) {
    track('Copy share link', {
      shareLink: link,
    })
  },
  // 移动到某个分组
  moveGroup(groupId: number) {
    track('Move group', {
      groupId,
    })
  },
  // 导出对话
  exportChat(type: string) {
    track('Export chat', {
      exportType: type,
    })
  },
  // 上传文件
  uploadFile(fileCategory: string) {
    track('Upload file', {
      fileType: fileCategory,
    })
  },
  // 删除会话
  deleteChats() {
    track('Delete chats')
  },
  // 重新发送消息
  resendMessage() {
    track('Resend message')
  },
  // 删除一条聊天记录
  deleteChatRecord() {
    track('Delete one chat record')
  },
  // 复制回答
  copyAnswer() {
    track('Copy answer')
  },
  // 复制提问
  copyQuestion() {
    track('Copy question')
  },
  // 工具调用
  toolCallStart(toolName: string) {
    track('Tool Call', {
      toolName,
    })
  },
  // 选择语言
  selectLanguage(lang: string) {
    track('Select language', {
      language: lang,
    })
  },
  // 引导用户使用 Prompt
  guideUserUsePrompt(name: string) {
    track('GuidePrompt', {
      name,
    })
  },
  // 引导用户使用 Tab 键补全 Prompt
  guideUserUseTabKey() {
    track('GuideUserUseTabKey')
  },
  // 登录界面滑动操作
  loginSwiperChange() {
    track('Login Swiper Change')
  },
  // Artifacts 部署相关
  artifacts(action: string) {
    track('Artifacts', {
      action,
    })
  },
  // 打开模型 Thkinking 模式
  deepThinking(open: boolean) {
    track('Deep Thinking', {
      open,
    })
  },
  // SSE 输出异常，开始重试
  // status: success 成功，fail 失败，abnormal 异常，tool_call_timeout 工具调用超时
  sseRetry(status: 'success' | 'fail' | 'abnormal' | 'tool_call_timeout') {
    track('SSE Retry', {
      status,
    })
  },
  // Artifact 激活时间
  artifactResponse(data: { start: number, end: number, duration: number, requestId: string }) {
    track('Artifact Res', data)
  },
}
