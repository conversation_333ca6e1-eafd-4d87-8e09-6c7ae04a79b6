import type { TokenizerAndRendererExtension } from 'marked';

export function citationExtension(): TokenizerAndRendererExtension {
  return {
    name: 'citation',
    level: 'inline',
    start(src) {
      return src.match(/<cite>/)?.index;
    },
    tokenizer(src) {
      const rule = /^<cite>(.*?)<\/cite>/;
      const match = rule.exec(src);
      if (match) {
        try {
          const parsed = JSON.parse(match[1]);
          if (typeof parsed.i === 'number') {
            return {
              type: 'citation',
              raw: match[0],
              text: String(parsed.i)
            };
          }
        } catch (e) {
          // JSON.parse 失败，返回空字符
          return {
            type: 'citation',
            raw: match[0],
            text: ''
          };
        }
      }
      return undefined;
    },
    renderer(token) {
      return `<span class="citation">${token.text}</span>`;
    }
  };
}
