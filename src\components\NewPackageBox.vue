<template>
  <div class="relative h-full flex-y-c flex-col text-primary-400">
    <template v-if="!giftData.title">
      <PageTitle class="flex items-center lt-md:hidden">
        <div class="flex items-center">
          <div>{{ $t("packageBox.packageSubscription") }}</div>
          <!-- <div
            class="relative ml-5 flex rounded-1.25 bg-#fff p-1.5 shadow-[0_0_6.25rem_0_#00000020] dark:bg-#272727"
          >
            <div
              v-if="tabs[activeTab]"
              :class="`rounded-1.25 w-4.38rem h-1.75rem text-3.25 text-center bg-destructive-300 text-#ffffff absolute transition shadow-[0_0.63rem_1.88rem_0_#f85a002e] ${tabs[activeTab].translate}`"
            ></div>
            <!~~ <div
              v-for="({ label }, index) in tabs"
              :key="index"
              :class="`rounded-1.25 w-4.38rem h-1.75rem text-0.88rem font-400 leading-1.75rem text-center cursor-pointer relative transition ${
                activeTab === index ? tabs[activeTab].style : ''
              }`"
              @click="setActiveTab({ index })"
            >
              {{ label }}
            </div> ~~>
          </div> -->
        </div>
      </PageTitle>
      <div
        class="flex justify-center overflow-x-auto p-[100px] pt-60px lt-md:(mt-30px w-100 snap-x snap-mandatory justify-start pt-0 space-x-0) space-x-80px"
      >
        <div
          v-for="(item, index) in packageData.slice(
            activeTab * 2,
            activeTab === null ? 4 : (activeTab + 1) * 2,
          )"
          :key="index"
          class="plan_item relative w-300px font-[var(--font-family)] lt-md:(w-100 flex-[0_0_25rem] snap-start)"
        >
          <!-- 当前是Plus会员时，Premium会始终有hover的效果 -->
          <div
            p="l-29px t-26px b-17px"
            class="package-box border-box relative z-1 h-564px overflow-hidden rounded-20px transition-cusbezier-450 lt-md:(h-[calc(100dvh-182px)] border-transparent hover:shadow-none)"
            :class="{ 'emphasize-card': (userInfoV2.level === 2 && item.level === 3) }"
          >
            <div class="flex flex-col justify-end whitespace-nowrap">
              <div class="min-h-38px text-24px font-700" :class="clacPlanColor(item.title)">
                {{ language === 'zhCn' ? item.description : item.title }}
              </div>

              <p text="13px #cdcdcd" class="min-h-22px flex justify-between pr-29px">
                <span>{{ calcBillingCycle(item.expireDays) }}</span>
                <span v-if="isCurrentPlan(item) && userInfoV2.plusExpireTime" class="flex-y-c select-none">
                  {{ formatExpireTime(userInfoV2.plusExpireTime) }}
                  {{ t('packageBox.expiryTime') }}
                </span>
              </p>
            </div>
            <el-divider border-style="dashed" class="!mb-22px !mt-18px !border-#f1f1f1 dark:!border-#363636" />

            <template v-if="!item.paymentIdentificationText">
              <p class="subtitle !mb-18px">Includes</p>

              <section class="space-y-17px">
                <div v-for="(value, key) in filteredConfigs(item)" :key="key">
                  <div class="pr-29px">
                    <div class="flex items-center space-x-8px">
                      <div class="item_dot mb-1px" />
                      <div class="item_text">
                        <el-popover
                          :placement="isPc ? 'right' : 'bottom'"
                          :width="415"
                          :show-arrow="false"
                          :persistent="false"
                          :hide-after="0"
                          popper-class="!rounded-20px !p-0 !z-20000"
                        >
                          <template #default>
                            <div class="px-25px pb-25px pt-27px text-#000 font-400 dark:text-#F1F1F1">
                              <p class="text-12px">
                                <span class="text-#f00 font-800">{{ t('packageBox.tipTitle') }}</span>
                                <span class="font-400">
                                  {{ calcTipContent(item) }}
                                </span>
                              </p>
                              <div class="mt-22px text-13px">
                                <ul class="list-circle pl-15px space-y-22px">
                                  <li v-for="(group, groupIndex) in groupModelItemsByType(Array.isArray(value) ? value : [])" :key="groupIndex">
                                    <!-- 单个模型项 -->
                                    <div v-if="group.items.length === 1" class="flex space-x-6px">
                                      <span>{{ group.items[0].name }}</span>
                                      <NewPackageInfoTag v-if="typeof key === 'string' && key.toLowerCase() !== 'vision'">
                                        {{ formatNumberToK(group.items[0].maxToken) }}
                                      </NewPackageInfoTag>
                                      <NewPackageInfoTag>
                                        {{ group.limit >= 9999 ? '∞' : `${group.limit || 0}次` }}
                                      </NewPackageInfoTag>
                                    </div>

                                    <!-- 多个相同类型的模型项 -->
                                    <div v-else class="flex flex-col space-y-22px">
                                      <div v-for="(item, itemIndex) in group.items" :key="itemIndex" class="flex space-x-6px">
                                        <span>{{ item.name }}</span>
                                        <NewPackageInfoTag v-if="typeof key === 'string' && key.toLowerCase() !== 'vision'">
                                          {{ formatNumberToK(item.maxToken) }}
                                        </NewPackageInfoTag>
                                        <!-- 只对第一个项显示limit -->
                                        <NewPackageInfoTag v-if="itemIndex === 0">
                                          {{ group.limit >= 9999 ? '∞' : `${group.limit || 0}次` }}
                                        </NewPackageInfoTag>
                                      </div>
                                    </div>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </template>

                          <template #reference>
                            <p class="underline-wrapper cursor-pointer transition-cusbezier-150 dark:(text-#727272) hover:(text-#FF5913 font-700)">
                              {{ key }}
                            </p>
                          </template>
                        </el-popover>

                        <!-- 自己的套餐，显示使用量 -->
                        <SubscriptionPlansProgress
                          v-if="isCurrentPlan(item)"
                          :progress="calcProviderUsage(key as string).progress"
                          :sum="calcProviderUsage(key as string).sum"
                          :used="calcProviderUsage(key as string).used"
                        />

                        <!-- 不是自己的套餐，显示套餐的总量 -->
                        <div v-else class="w-48px flex-c whitespace-nowrap rounded-4px bg-#ECF0F5 text-10px text-#A8AFBF font-500 dark:(bg-#363636 text-#CDCDCD)">
                          {{ has9999Model(value) ? '∞' : `${calcTotalLimit(value)}次` }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <section class="mt-20px">
                <p class="subtitle !mb-0">{{ item.price }}</p>
                <p class="text-13px text-#cdcdcd font-500">
                  {{
                    isFreePlan(item)
                      ? `Per user ${trialDays} days`
                      : `Per user ${item.expireDays} days`
                  }}
                </p>
              </section>
            </template>
            <template v-else>
              <div class="">
                <!-- <div class="flex-c items-start pr-29px font-RobotoSlab">
                  <span class="text-1.75rem font-700">{{ item.price }}</span>
                </div> -->
                <div class="subtitle">
                  order
                </div>
                <div class="pr-29px text-0.88rem">
                  <div class="flex items-start justify-between gap-8px">
                    <div class="whitespace-nowrap">
                      {{ $t("packageBox.orderNumber") }}:
                    </div>
                    <div class="break-all text-right text-14px text-#A8AFBF">{{ item.orderNo }}</div>
                  </div>
                  <div
                    v-if="item.deductionAmt"
                    class="flex-y-c justify-between pt-22px"
                  >
                    <div>{{ $t("packageBox.packageCredit") }}:</div>

                    <div class="text-#A8AFBF font-500">
                      -${{ item.deductionAmt }}
                    </div>
                  </div>
                  <div
                    v-if="item.commissionAmt"
                    class="flex-y-c justify-between pt-22px"
                  >
                    <div>{{ $t("packageBox.rewardCredit") }}:</div>

                    <div class="text-#A8AFBF font-500">
                      -${{ item.commissionAmt }}
                    </div>
                  </div>
                  <div class="flex-y-c justify-between pt-22px">
                    <div>{{ $t("packageBox.actualPayments") }}:</div>

                    <div class="text-#A8AFBF font-500">
                      ${{ item.discountPirce }}
                    </div>
                  </div>
                  <div
                    v-if="
                      item.activityCount !== 0
                        && item.actNewDiscountPercent
                        && userInfoV2.userType === 1
                    "
                    class="flex-y-c justify-between pt-22px"
                  >
                    <div>{{ $t("packageBox.newUserDiscount") }}：</div>
                    <div
                      class="mr-0.63rem flex-1 border-t border-#ECF0F5 border-dashed"
                    ></div>
                    <div class="text-#A8AFBF font-500">
                      {{ (item.actNewDiscountPercent * 100).toFixed(0) }}%
                    </div>
                  </div>
                  <div
                    v-if="
                      item.activityCount !== 0
                        && item.actOldDiscountPercent
                        && userInfoV2.userType === 2
                    "
                    class="flex-y-c justify-between pt-0.94rem"
                  >
                    <div>{{ $t("packageBox.discountRegularUsers") }}：</div>
                    <div
                      class="mr-0.63rem flex-1 border-t border-#ECF0F5 border-dashed"
                    ></div>
                    <div class="text-#A8AFBF font-500">
                      {{ (item.actOldDiscountPercent * 100).toFixed(0) }}%
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <div
            class="pay_btn absolute inset-x-0 bottom-0 h-74px w-full flex-c translate-y-[calc(100%-20px)] cursor-pointer rounded-b-20px pt-20px text-center text-14px font-700"
            :class="{
              'pointer-events-none': item.status === 'DISABLED'
                || item.loading
                || isCurrentPlan(item)
                || userInfoV2?.level > item?.level,
            }"
          >
            <div
              v-if="['UNPAID'].includes(item.status)"
              class="size-full flex items-center justify-between px-29px text-#ffffff"
            >
              <div class="h-full flex gap-6px">
                <button
                  class="pay_item_btn h-full flex-c select-none pr-6px transition-cusbezier-150 hover:text-#ffffffd1"
                  :class="{ 'pointer-events-none': item?.loading || getPackageLoading }"
                  @click="submitHandler('repayment', item)"
                >
                  {{
                    item.paymentIdentificationText
                      ? $t("packageBox.confirmPayment")
                      : $t("packageBox.waitPayment")
                  }}
                </button>
                <button
                  class="cancel_pay_btn pay_item_btn h-full flex-c select-none px-6px transition-cusbezier-150 hover:text-#ffffffd1"
                  :class="{ 'pointer-events-none': item?.loading || getPackageLoading }"
                  @click="cancelPay(item)"
                >
                  <i v-if="item?.loading || getPackageLoading" class="i-line-md-loading-twotone-loop mr-2" />
                  <span>{{ $t('packageBox.cancelPayment') }}</span>
                </button>
              </div>
              {{ unpaidTime }}
            </div>
            <div
              v-if="['NORMAL'].includes(item.status)"
              class="size-full flex-c text-#ffffff"
              @click="submitHandler('stripe', item)"
            >
              <i v-if="item?.loading" class="i-line-md-loading-twotone-loop mr-2" />
              <div v-if="isCurrentPlan(item)" class="flex-c space-x-6px">
                <i class="i-ju-current-plan text-16px" />
                <span>{{ $t("packageBox.currentMember") }}</span>
              </div>

              <div v-else class="">
                <span v-if="userInfoV2?.level >= item?.level" class="flex-c space-x-6px">
                  <i class="i-ju-right text-16px" />
                  <span>{{ $t("packageBox.upgraded") }}</span>
                </span>
                <span v-else>
                  {{ $t("packageBox.buyNow") }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <BillDetail v-else :gift-data="giftData"></BillDetail>
  </div>
</template>

<script setup lang="ts">
import type { Duration } from 'moment'
import type {
  PackageItemModel,
  UnpaidItemModel,
} from '@/model/packages'

import { sum } from 'lodash'
import moment from 'moment'
import services from '@/common/axios'
import { formatNumberToK } from '@/common/tools'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useAppStore } from '@/stores/app'
import { useLangStore } from '@/stores/i18n'
import { useUserStore } from '@/stores/user'

const { isPc } = storeToRefs(useAppStore())

const giftData: any = ref({})
const unpaidTime = ref('')
const unpaidData = ref<any>({})
const activeTab = ref<any>(null)
const expirePolling = ref()

const t = useGlobalI18n()
const tabs = ref([
  {
    label: t('packageBox.dailyPackage'),
    style: 'text-#ffffff',
    translate: 'translate-x-0',
  },
  {
    label: t('packageBox.monthlyPackage'),
    style: 'text-#ffffff',
    translate: 'translate-x-4.38rem',
  },
])

const userStore = useUserStore()
const { userInfoV2 } = storeToRefs(userStore)

/**
 * 判断传入的套餐是否是免费套餐
 * @param item 套餐信息
 * @returns boolean
 */
function isFreePlan(item: any) {
  if (item.expireDays) {
    return item.expireDays === '/'
  }
  else {
    return false
  }
}

/**
 * 判断传入的套餐是否是用户当前使用的套餐
 * @param item 套餐信息
 * @returns boolean
 */
function isCurrentPlan(item: PackageItemModel) {
  // 判断用户是否有会员过期时间（无过期时间表示未开通过会员）
  const hasActiveMembership = Boolean(userInfoV2.value.plusExpireTime)

  // 如果用户未开通过会员，且当前是体验会员套餐，则为当前套餐
  if (!hasActiveMembership && isFreePlan(item)) {
    return true
  }

  // 如果用户开通过会员，则比较会员等级
  return item.level === userInfoV2.value.level
}

const langStore = useLangStore()
const { language } = storeToRefs(langStore)

const packageData = ref<any>([])
const getPackageLoading = ref(false)
async function getPackagesV2() {
  getPackageLoading.value = true
  let array = []
  const data: any = await services.get(
    `/order/v2/packages`,
    {
      baseURL: '/gw/chatpay',
    },
  )
  const ordersData: UnpaidItemModel = await services.post(
    `/order/notCompletedRecord`,
    {
      headers: { noninductive: true },
    },
    {
      baseURL: '/gw/chatpay',
    },
  )
  array = data
    ?.map((item: any) => {
      //     status: item.id ? 'NORMAL' : 'DISABLED',
      return {
        ...item,
        title: item.title || 'Trial',
        description: item.description || '体验会员',
        status: 'NORMAL',
        price: item.price ? `$${item.price}` : 'Free',
        expireDays: item.expireDays ? item.expireDays : '/',
      }
    })
  if (Boolean(ordersData) && ordersData.expireTime) {
    console.log('存在历史订单: ', ordersData)
    calcExpireTime(ordersData.expireTime)
    unpaidData.value = ordersData
    array.forEach((i: any) => {
      if (ordersData.packageId === i.id) {
        i.status = 'UNPAID'
        i.expireTime = ordersData.expireTime
        i.orderNo = ordersData?.orderNo
      }
      else {
        i.status = 'DISABLED'
      }
    })
  }
  packageData.value = array
  getPackageLoading.value = false
}

// 计算套餐标题颜色
const clacPlanColor = (param: string) => {
  const text = param?.toLocaleLowerCase()
  switch (text) {
    case 'plus':
      return 'text-#ff8000'
    case 'premium':
      return 'text-#f00'
    default:
      return ''
  }
}

/**
 * 格式化时间
 * @param duration 时间
 */
function formatTime(duration: Duration) {
  const isNegative = duration.asSeconds() < 0
  const padZeroHours = String(Math.abs(duration.hours())).padStart(2, '0')
  const padZeroMinutes = String(Math.abs(duration.minutes())).padStart(2, '0')
  const padZeroSeconds = String(Math.abs(duration.seconds())).padStart(2, '0')
  return `${isNegative ? '-' : ''}${padZeroHours}:${padZeroMinutes}:${padZeroSeconds}`
}

const submit = async (method: string, item: PackageItemModel) => {
  try {
    if (['DISABLED'].includes(item.status)) {
      return
    }
    if (['stripe'].includes(method)) {
      item.loading = true
      const { commissionAmt, deductionAmt, expireTime, orderNo, payAmt, url }: any = await services.post(
        `/order/stripe/pay`,
        { packageId: item.id },
        { baseURL: '/gw/chatpay' },
      )
      item.loading = false
      if (expirePolling.value) {
        clearInterval(expirePolling.value)
        expirePolling.value = null
      }

      const duration = moment.duration(moment(expireTime).diff(moment()))
      unpaidTime.value = formatTime(duration)

      packageData.value = packageData.value.map((element: any) => {
        if (item.id === element.id) {
          return {
            ...element,
            status: 'UNPAID',
            orderNo,
            discountPirce: payAmt,
            deductionAmt,
            expireTime,
            commissionAmt,
            paymentIdentificationText: url,
          }
        }
        return {
          ...element,
          status: 'DISABLED',
        }
      })
      calcExpireTime(expireTime)
    }
    else if (['repayment'].includes(method)) {
      const {
        price,
        orderNo,
        deductionAmt,
        commissionAmt,
        paymentIdentificationText,
      } = unpaidData.value

      Object.assign(item, {
        orderNo,
        discountPirce: price,
        deductionAmt,
        commissionAmt,
        paymentIdentificationText,
      })
    }
  }
  catch {}
  finally {
    item.loading = false
  }
}

const submitTimer = ref()
const submitHandler = async (method: string, item: PackageItemModel) => {
  clearInterval(submitTimer.value)
  submitTimer.value = null
  if (!item.paymentIdentificationText) {
    // 如果没有支付链接，就先请求支付链接，让UI变化，显示账单优惠情况
    await submit(method, item)
  }
  else {
    // 如果有支付链接，就打开支付链接，并设置定时器，定时获取支付状态
    open(item.paymentIdentificationText)
    submitTimer.value = setInterval(() => {
      getStatus(item)
    }, 1500)
  }
}

// 取消订单
async function cancelPay(item: PackageItemModel) {
  item.loading = true
  const data: any = await services.post(
    `${import.meta.env.VITE_APP_PAY_URL}/order/stripe/cancel`,
    {
      orderNo: item.orderNo,
    },
  )
  item.loading = false
  if (!data.success) {
    return
  }
  clearAllTimer()
  setTimeout(() => {
    nextTick(() => {
      pageInit()
    })
  }, 0)
}

const calcExpireTime = (date: string) => {
  // 清除已存在的定时器
  if (expirePolling.value) {
    clearInterval(expirePolling.value)
    expirePolling.value = null
  }

  const setUnpaidTime = () => {
    const duration = moment.duration(moment(date).diff(moment()))
    if (duration.asSeconds() <= 0) {
      clearInterval(expirePolling.value)
      expirePolling.value = null
      unpaidTime.value = '00:00:00'
      return
    }
    unpaidTime.value = formatTime(duration)
  }

  // 立即执行一次
  setUnpaidTime()

  // 只有当倒计时大于0时才设置定时器
  const duration = moment.duration(moment(date).diff(moment()))
  if (duration.asSeconds() > 0) {
    expirePolling.value = setInterval(setUnpaidTime, 1000)
  }
}

const getStatus = async (item: PackageItemModel) => {
  const { status } = await services.post(
    `${import.meta.env.VITE_APP_PAY_URL}/order/detail`,
    { orderNo: item.orderNo },
    { headers: { noninductive: true } },
  )
  if ([1].includes(status)) {
    clearAllTimer()
    await getUserInfoV2()
    const { plusExpireTime } = userInfoV2.value
    giftData.value = { ...item, expireTime: plusExpireTime }
    getPackagesV2()
  }
}
const { getUserInfoV2 } = useUserInfo()

interface ModelConfig {
  name: string
  limit: number
  maxToken: number
}

interface FilteredConfigs {
  [key: string]: ModelConfig[]
}

// 过滤掉LAB模型
const filteredConfigs = (item: any): FilteredConfigs => {
  const result: FilteredConfigs = {}
  if (item.modelConfigs) {
    for (const [key, value] of Object.entries(item.modelConfigs)) {
      if (key.toUpperCase() !== 'LAB') {
        result[String(key)] = value as ModelConfig[]
      }
    }
  }
  return result
}

// 计算不是自己开通的套餐的供应商总量
const calcTotalLimit = (models: any[]) => {
  return sum(models?.map(model => model.limit) || [])
}

// 套餐中是否包含>= 9999（无限制）的模型
const has9999Model = (models: any[]) => {
  return models?.some(model => model.limit >= 9999)
}

// 将相同类型的模型项分组
function groupModelItemsByType(items: any[]) {
  if (!Array.isArray(items)) { return [] }

  const groupedByType: Record<string | number, {
    type: string | number
    limit: number
    items: any[]
  }> = {}

  // 按类型分组
  items.forEach((item) => {
    if (!item || typeof item.type === 'undefined') { return }

    if (!groupedByType[item.type]) {
      groupedByType[item.type] = {
        type: item.type,
        limit: item.limit,
        items: [],
      }
    }
    groupedByType[item.type].items.push(item)
  })

  return Object.values(groupedByType)
}

interface ModelUsage {
  name: string
  limit: number
  used: number
}

// 计算供应商的总使用量和限制
const calcProviderUsage = (key: string) => {
  const providerModels = (userInfoV2.value as any).modelUsed?.[key] || []
  const totalLimit = Number(sum(providerModels.map((model: ModelUsage) => model.limit)))
  const totalUsed = Number(sum(providerModels.map((model: ModelUsage) => model.used)))
  const progress = totalLimit > 0 ? totalUsed / totalLimit : 0
  return {
    sum: totalLimit,
    used: totalUsed,
    progress,
  }
}

function pageInit() {
  getUserInfoV2()
  getPackagesV2()
}

onMounted(() => {
  pageInit()
})

function clearAllTimer() {
  if (submitTimer.value) {
    clearInterval(submitTimer.value)
    submitTimer.value = null
  }
  if (expirePolling.value) {
    clearInterval(expirePolling.value)
    expirePolling.value = null
  }
}

onBeforeUnmount(() => {
  clearAllTimer()
})

// 计算提示内容
function calcTipContent(item: any) {
  const itemTitle = item.title
  const isEnglish = language.value === 'en'

  // itemType 的值是字符串 免费 付费
  const itemType = isFreePlan(item)
    ? t('packageBox.freeType').value
    : t('packageBox.paidType').value

  const itemDays = isFreePlan(item) ? trialDays.value : item.expireDays
  const i18nContent = t('packageBox.planTip').value

  // 处理英文状态下的单复数
  let daysText = itemDays
  if (isEnglish) {
    daysText = `${String(itemDays).trim()} days`
  }

  const res = i18nContent
    .replace('%title', itemTitle)
    .replace('%type', itemType)
    .replace('%days', daysText)

  return res
}

// 体验会员的天数
const trialDays = ref(31)

// 计算计费周期显示文本
function calcBillingCycle(expireDays: string | number) {
  const expireDaysNumber = Number(expireDays)
  const obj = {
    expireDays,
  }
  if (isFreePlan(obj)) {
    return 'Billed Monthly'
  }
  if ([28, 29, 30, 31].includes(expireDaysNumber)) {
    return 'Billed Monthly'
  }
  if ([89, 90, 91, 92].includes(expireDaysNumber)) {
    return 'Billed Quarterly'
  }
  if ([365, 366].includes(expireDaysNumber)) {
    return 'Billed Yearly'
  }
  return `Billed ${expireDays} days`
}

function formatExpireTime(time: string | undefined) {
  return time ? moment(time).format('YY/MM/DD HH:mm') : ''
}
</script>

<style scoped lang="scss">
@property --houdini-pay-btn-color-a {
  syntax: "<color>";
  inherits: false;
  initial-value: #FFBB9E;
}

@property --houdini-pay-btn-color-b {
  syntax: "<color>";
  inherits: false;
  initial-value: #FF4C00;
}

@property --houdini-packageitemcard-a {
  syntax: "<color>";
  inherits: false;
  initial-value: #fff;
}

@property --houdini-packageitemcard-b {
  syntax: "<color>";
  inherits: false;
  initial-value: #fff;
}

@property --houdini-packageitemcard-c {
  syntax: "<color>";
  inherits: false;
  initial-value: #fff;
}

@property --houdini-packageitemborder-a {
  syntax: "<color>";
  inherits: false;
  initial-value: #ECF0F5;
}

@property --houdini-packageitemborder-b {
  syntax: "<color>";
  inherits: false;
  initial-value: #ECF0F5;
}

@property --houdini-packageitemborder-c {
  syntax: "<color>";
  inherits: false;
  initial-value: #ECF0F5;
}

@mixin pay-btn-hover {
  --houdini-pay-btn-color-a: #FFAF8D;
  --houdini-pay-btn-color-b: #ff2600;

  background-size: 28px 24px, 100% 100%;
}

.pay_btn {
  cursor: pointer;
  background-image:
    radial-gradient(#FFAF8D 1px, transparent 1px),
    linear-gradient(
      136deg,
      var(--houdini-pay-btn-color-a) 0%,
      var(--houdini-pay-btn-color-b) 100%
    );
  background-size: 24px 24px, 100% 100%;
  transition:
    0.8s --houdini-pay-btn-color-a var(--ani-bezier),
    0.8s --houdini-pay-btn-color-b var(--ani-bezier),
    0.8s background-size var(--ani-bezier);

  // 当包含 pay_item_btn 时，cursor 变为 default
  &:has(.pay_item_btn) {
    cursor: default;
  }

  // 当不包含 pay_item_btn 时，hover 在 pay_btn 上生效
  &:not(:has(.pay_item_btn)):hover {
    @include pay-btn-hover;
  }

  // 当包含 pay_item_btn 时，hover 在 pay_item_btn 上让 pay_btn 生效hover效果
  &:has(.pay_item_btn:hover) {
    @include pay-btn-hover;
  }
}

.package-box {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 12%) transparent;
}

.subtitle {
  @apply mb-20px text-16px font-700;
}

.item_dot {
  width: 14px;
  height: 14px;
  background-image: linear-gradient(136deg, #FFBB9E 0%, #FF4C00 100%);
  box-shadow: 0 10px 50px 0 #0000001a;
  border-radius: 50%;
}

.item_text {
  font-size: 13px;
  color: #171717;
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.underline-wrapper {
  background-image: linear-gradient(90deg, #FF5913 50%, transparent 50%);
  background-size: 0 1px;
  background-repeat: repeat-x;
  background-position: 0 100%;
  line-height: 24px;

  &:hover,
  &:active {
    background-size: 15px 1px;
  }
}

@mixin package-box-active {
  border: 2px solid transparent;

  --houdini-packageitemborder-a: var(--plan-item-border-hover-a) !important;
  --houdini-packageitemborder-b: var(--plan-item-border-hover-b) !important;
  --houdini-packageitemborder-c: var(--plan-item-border-hover-c) !important;
  --houdini-packageitemcard-a: var(--plan-item-card-color-a) !important;
  --houdini-packageitemcard-b: var(--plan-item-card-color-b) !important;
  --houdini-packageitemcard-c: var(--plan-item-card-color-c) !important;
}

.border-box {
  --houdini-packageitemborder-a: var(--plan-item-border);
  --houdini-packageitemborder-b: var(--plan-item-border-medium);
  --houdini-packageitemborder-c: var(--plan-item-border-bottom);
  --houdini-packageitemcard-a: var(--plan-item-bg);
  --houdini-packageitemcard-b: var(--plan-item-bg);
  --houdini-packageitemcard-c: var(--plan-item-bg);

  border: 2px solid transparent;
  border-radius: 16px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(
      225deg,
      var(--houdini-packageitemcard-a) 0%,
      var(--houdini-packageitemcard-b) 15%,
      var(--houdini-packageitemcard-c) 28%,
      var(--houdini-packageitemcard-c) 100%,
    ),
    linear-gradient(
      180deg,
      var(--houdini-packageitemborder-a) 0%,
      var(--houdini-packageitemborder-b) 67%,
      var(--houdini-packageitemborder-c) 100%
    );
  transition:
    1s --houdini-packageitemborder-a var(--ani-bezier),
    1s --houdini-packageitemborder-b var(--ani-bezier),
    1s --houdini-packageitemborder-c var(--ani-bezier),
    0.7s --houdini-packageitemcard-a var(--ani-bezier),
    0.7s --houdini-packageitemcard-b var(--ani-bezier),
    0.7s --houdini-packageitemcard-c var(--ani-bezier);

}

.emphasize-card {
  @include package-box-active;
}

.cancel_pay_btn {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;

    // 外层有个6px的gap，所以left需要-3px
    left: -3px;
    transform: translateY(-41%);
    width: 1px;
    height: 14px;
    background-color: #fff;
  }
}

.plan_item {
  &:hover {
    .border-box {
      @include package-box-active;
    }
  }
}
</style>
