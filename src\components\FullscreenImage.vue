<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <Teleport to="body">
      <div
        v-if="showFullscreenImage"
        class="fullscreen-image fixed inset-0 z-99 animate-duration-300 bg-[#151A23] text-#fff transition-cusbezier-300"
      >
        <div class="size-full flex flex-col select-none">
          <div
            class="relative h-[var(--header-height)] w-full flex items-end justify-between pl-40px pr-30px text-#cbd5e1 lt-lg:(flex-row-reverse)"
          >
            <button
              class="flex-c text-24px hover:opacity-80"
              :title="t('imageGallery.closeButton').value"
              @click="closeFullscreenImage"
            >
              <i class="i-ri-close-line"></i>
            </button>

            <div class="flex-y-c text-18px space-x-10px !lt-lg:(hidden)">
              <button
                class="flex-c hover:opacity-80"
                :title="t('imageGallery.downloadButton').value"
                @click="downloadCurrentImage"
              >
                <i class="i-ri-download-line"></i>
              </button>
              <button
                v-if="!isVideo(currentDisplayImage?.src)"
                class="flex-c hover:opacity-80"
                :title="t('imageGallery.copyImageButton').value"
                @click="copyImageToClipboard"
              >
                <i class="i-ri-file-copy-line"></i>
              </button>
            </div>
          </div>

          <section class="mt-[var(--image-padding-top)] h-[calc(100dvh-2*var(--header-height)-var(--image-padding-top))] flex lt-lg:(w-full)">
            <div class="relative size-full flex-c flex-1 overflow-hidden lt-lg:h-[calc((100dvh-2*var(--header-height)-var(--image-padding-top)))]">
              <!-- 左箭头 -->
              <button
                v-show="images && images.length > 1"
                class="absolute left-40px text-3xl lt-lg:left-15px hover:opacity-80"
                @click="prevImage"
              >
                <i class="i-ri-arrow-left-wide-line"></i>
              </button>

              <!-- 根据资源类型显示图片或视频 -->
              <template v-if="isVideo(currentDisplayImage?.src)">
                <video
                  :src="currentDisplayImage?.src"
                  class="media-style px-80px"
                  controls
                />
              </template>
              <template v-else>
                <div
                  class="mx-80px size-full flex-c overflow-hidden rounded-16px lt-lg:mx-40px"
                >
                  <img
                    :src="currentDisplayImage?.src"
                    class="media-style"
                    :class="{ 'mix-blend-screen rounded-10px': getFileExtension(currentDisplayImage?.src) === 'svg' }"

                    :style="{
                      'transform': `translate3d(${position.x}px, ${position.y}px, 0) scale(${scale})`,
                      'cursor': isDragging ? 'grabbing' : 'grab',
                      'transform-origin': '0 0',
                    }"
                    @wheel="handleWheel"
                    @pointerdown="handlePointerDown"
                    @pointermove="handlePointerMove"
                    @pointerup="handlePointerUp"
                    @pointercancel="handlePointerUp"
                    @touchstart="handleTouchStart"
                    @touchmove="handleTouchMove"
                    @touchend="handleTouchEnd"
                    @touchcancel="handleTouchEnd"
                  />
                </div>
              </template>

              <!-- 右箭头 -->
              <button
                v-show="images && images.length > 1"
                class="absolute right-40px text-3xl lt-lg:right-15px hover:opacity-80"
                @click="nextImage"
              >
                <i class="i-ri-arrow-right-wide-line"></i>
              </button>
            </div>
          </section>

          <!-- UI样式上为了平衡高度，使得图片在整个页面居中（因为上面有个工具栏，所以下面也弄个和工具栏一样高的空白区域） -->
          <section class="h-[calc(var(--header-height)+var(--image-padding-top))] w-full bg-#0000">
          </section>
        </div>
      </div>
    </Teleport>
  </Transition>
</template>

<script setup lang="ts">
import { getFileExtension, isVideo } from '@/common/common'
import {
  calculateScaledDimensions,
  copyImageToClipboard as copyToClipboard,
  downloadImage,
  svgToBase64,
  svgToPngBlob,
} from '@/common/imageHandler'
import { notify } from '@/common/tools'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useAppStore } from '@/stores/app'

const props = defineProps<{
  showFullscreenImage: boolean
  images: ImageInfo[]
  currentIndex: number
  prompt: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'update:currentIndex', index: number): void
}>()

const t = useGlobalI18n()

interface ImageInfo {
  src: string
  text: string
}

const currentDisplayImage = computed(() => props.images[props.currentIndex])

const prevImage = () => {
  const newIndex = props.currentIndex > 0 ? props.currentIndex - 1 : props.images.length - 1
  emit('update:currentIndex', newIndex)
}

const nextImage = () => {
  const newIndex = props.currentIndex < props.images.length - 1 ? props.currentIndex + 1 : 0
  emit('update:currentIndex', newIndex)
}

const closeFullscreenImage = () => {
  emit('close')
}

// 复制图片到剪贴板, 如果是svg则会按短边最短1000进行缩放，复制成png
async function copyImageToClipboard() {
  const imgSrc = currentDisplayImage.value?.src
  if (!imgSrc || isVideo(imgSrc)) {
    return
  }

  const isSvg = imgSrc.toLowerCase().endsWith('.svg') || imgSrc.includes('svg+xml')

  try {
    if (isSvg) {
      const response = await fetch(imgSrc)
      const svgText = await response.text()

      // 解析SVG获取尺寸
      const parser = new DOMParser()
      const svgDoc = parser.parseFromString(svgText, 'image/svg+xml')
      const svgElement = svgDoc.documentElement as unknown as SVGSVGElement

      // 获取原始尺寸
      let width = svgElement.width?.baseVal?.value
      let height = svgElement.height?.baseVal?.value

      if (!width || !height) {
        const viewBox = svgElement.viewBox?.baseVal
        if (viewBox) {
          width = viewBox.width
          height = viewBox.height
        }
        else {
          width = 1000
          height = 1000
        }
      }

      // 计算缩放后的尺寸
      const { width: scaledWidth, height: scaledHeight } = calculateScaledDimensions(width, height)

      // 转换为 Base64
      const svgBase64 = svgToBase64(svgText)

      // 转换为 PNG Blob
      const blob = await svgToPngBlob(svgBase64, scaledWidth, scaledHeight)

      // 复制到剪贴板
      await copyToClipboard(blob)
      notify.success({
        title: t('tipMessage.copySuccess').value,
      })
    }
    else {
      // 处理普通图片
      const img = new Image()
      img.crossOrigin = 'anonymous'

      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = imgSrc
      })

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      canvas.width = img.width
      canvas.height = img.height

      if (ctx) {
        ctx.fillStyle = '#FFFFFF'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(img, 0, 0)
      }

      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob(b => resolve(b!), 'image/png', 1.0)
      })

      const data = [new ClipboardItem({ 'image/png': blob })]
      await navigator.clipboard.write(data)
      notify.success({
        title: t('tipMessage.copySuccess').value,
      })
    }
  }
  catch (error) {
    console.error('Failed to copy image:', error)
  }
}

// 下载当前图片
async function downloadCurrentImage() {
  const imgSrc = currentDisplayImage.value?.src
  if (!imgSrc) {
    return
  }
  await downloadImage(imgSrc)
}

const { breakpoints } = useAppStore()
const ltLg = breakpoints.smaller('lg') // 小于LG尺寸

// 图片缩放相关状态
const scale = ref(1)
const minScale = 0.5
const maxScale = 4
const position = reactive({ x: 0, y: 0 })
const isDragging = ref(false)
const lastPointerPosition = reactive({ x: 0, y: 0 })
// 触摸相关状态
const lastTouchDistance = ref(0)
// 手势状态管理
const gestureType = ref<'none' | 'drag' | 'zoom'>('none')

// 触摸开始事件处理函数
function handleTouchStart(e: TouchEvent) {
  if (e.touches.length === 2) {
    // 双指触摸，准备缩放
    gestureType.value = 'zoom'
    isDragging.value = false
    const touch1 = e.touches[0]
    const touch2 = e.touches[1]
    lastTouchDistance.value = Math.hypot(
      touch2.clientX - touch1.clientX,
      touch2.clientY - touch1.clientY,
    )
  }
  else if (e.touches.length === 1 && gestureType.value === 'none') {
    // 单指触摸，准备拖动
    gestureType.value = 'drag'
    isDragging.value = true
    lastPointerPosition.x = e.touches[0].clientX
    lastPointerPosition.y = e.touches[0].clientY
  }
}

// 触摸结束事件处理函数
function handleTouchEnd(e: TouchEvent) {
  if (e.touches.length === 0) {
    // 所有手指都离开了屏幕
    gestureType.value = 'none'
    isDragging.value = false
    lastTouchDistance.value = 0
  }
  else if (e.touches.length === 1 && gestureType.value === 'zoom') {
    // 从双指变成单指，重置状态
    gestureType.value = 'none'
    isDragging.value = false
    lastTouchDistance.value = 0
  }
}

// 触摸移动事件处理函数
function handleTouchMove(e: TouchEvent) {
  e.preventDefault() // 阻止页面滚动

  if (e.touches.length === 2 && gestureType.value === 'zoom') {
    // 双指缩放
    const touch1 = e.touches[0]
    const touch2 = e.touches[1]
    const currentDistance = Math.hypot(
      touch2.clientX - touch1.clientX,
      touch2.clientY - touch1.clientY,
    )

    // 计算缩放比例
    const ratio = currentDistance / lastTouchDistance.value
    const newScale = scale.value * ratio

    if (newScale >= minScale && newScale <= maxScale) {
      // 计算缩放中心点
      const centerX = (touch1.clientX + touch2.clientX) / 2
      const centerY = (touch1.clientY + touch2.clientY) / 2
      const rect = (e.target as HTMLElement).getBoundingClientRect()
      const touchCenterX = centerX - rect.left
      const touchCenterY = centerY - rect.top

      // 更新位置和缩放
      position.x = position.x + (touchCenterX * (1 - ratio))
      position.y = position.y + (touchCenterY * (1 - ratio))
      scale.value = newScale
    }

    lastTouchDistance.value = currentDistance
  }
  else if (e.touches.length === 1 && gestureType.value === 'drag' && isDragging.value) {
    // 单指拖动
    const touch = e.touches[0]
    const dx = touch.clientX - lastPointerPosition.x
    const dy = touch.clientY - lastPointerPosition.y

    position.x += dx
    position.y += dy

    lastPointerPosition.x = touch.clientX
    lastPointerPosition.y = touch.clientY
  }
}

// 重置状态函数
function resetImageState() {
  scale.value = 1
  position.x = 0
  position.y = 0
  isDragging.value = false
  gestureType.value = 'none'
  lastTouchDistance.value = 0
}

// 监听图片切换和组件显示状态
// 以下这些状态变化时，重置图片的缩放状态和拖动位置
watch(
  [
    () => props.currentIndex,
    () => props.showFullscreenImage,
    () => ltLg.value,
  ],
  () => {
    resetImageState()
  },
)

// 鼠标滚轮事件处理函数
function handleWheel(e: WheelEvent) {
  e.preventDefault()
  const ratio = e.deltaY > 0 ? 1 / 1.1 : 1.1

  // 计算新的缩放值
  const newScale = scale.value * ratio
  if (newScale < minScale || newScale > maxScale) { return }

  // 获取鼠标相对于图片的位置
  const rect = (e.target as HTMLElement).getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top

  // 计算新的位置
  position.x = position.x + (mouseX * (1 - ratio))
  position.y = position.y + (mouseY * (1 - ratio))
  scale.value = newScale
}

// 鼠标按下事件（处理拖动）
function handlePointerDown(e: PointerEvent) {
  isDragging.value = true
  ;(e.target as HTMLElement).setPointerCapture(e.pointerId)
  lastPointerPosition.x = e.clientX
  lastPointerPosition.y = e.clientY
}

// 鼠标移动事件（处理拖动）
function handlePointerMove(e: PointerEvent) {
  if (!isDragging.value) { return }

  const dx = e.clientX - lastPointerPosition.x
  const dy = e.clientY - lastPointerPosition.y

  position.x += dx
  position.y += dy

  lastPointerPosition.x = e.clientX
  lastPointerPosition.y = e.clientY
}

// 鼠标松开事件（处理拖动）
function handlePointerUp() {
  isDragging.value = false
}

onKeyStroke('Escape', (e) => {
  e.preventDefault()
  closeFullscreenImage()
})
</script>

<style lang="scss" scoped>
.fullscreen-image {
  --header-height: 64px;
  --image-padding-top: 10px;
}

.media-style {
  @apply max-h-full max-w-full object-contain select-none;

  -webkit-user-drag: none;
}
</style>
