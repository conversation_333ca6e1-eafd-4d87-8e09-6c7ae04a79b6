<template>
  <div class="chat-view">
    <div id="params" :data-mode="mode" :data-sections="JSON.stringify(sections)" class="hidden">
    </div>
    <!-- exportHeader & exportFooter 在PDF导出时使用 -->
    <div class="export-header hidden h-100px items-center justify-center bg-#FBFBFB px-[20px] dark:bg-#000000">
      <img :src="isDark ? logoWhite : logo" alt="logo" class="mb-6px h-32px">
      <div class="mx-20px h-32px w-2px bg-#EAEAEA"></div>
      <div v-if="userInfo.title" class="line-clamp-1 mt-2px text-22px font-medium">
        <span>{{ userInfo.title.replace(/^"|"$/g, '').slice(0, 50) }}</span>
        <span v-if="userInfo.title.length >= 50">...</span>
      </div>
    </div>
    <div class="export-footer hidden h-100px items-center justify-center bg-#000000 text-20px text-#ffffff">
      <AvatarFrame class="h-50px w-50px" :src="userInfo.avatar"></AvatarFrame>
      <div class="ml-20px font-semibold font-RobotoSlab">{{ userInfo.userName }}</div>
      <img class="ml-20px h-25px" src="@/assets/images/ai-avatar-transparent-white.svg" alt="">
      <div class="ml-10px font-bold">Juchats</div>
      <div class="ml-10px">One Window, Infinite Worlds!</div>
    </div>

    <ShareBar :user-info="userInfo" />
    <ChatContent v-model:sections="sections" :message-id="messageId" :is-share-page="true"></ChatContent>
  </div>
  <!-- <ShareFooter :share-code="route.query.shareCode" :userInfo="userInfo"></ShareFooter> -->
</template>

<script setup lang="ts">
import avatar_gpt from '@/assets/images/ai-avatar.png'
import avatar_default from '@/assets/images/avatar_default.png'
import logoWhite from '@/assets/images/logo-white.svg'
import logo from '@/assets/images/logo.svg'
import services from '@/common/axios'
import { getFullImageUrl, notify } from '@/common/tools'
import { GENERATE_IMAGE_MODES, IMAGE_SPLIT } from '@/enum'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useThemeChange } from '@/hooks/useThemeChange'
import { useAppStore } from '@/stores/app'
import { toRefs } from '@vueuse/core'
import moment from 'moment'
import { useRoute } from 'vue-router'
import { formatMarkdown } from '@/common/marked'

const { modelSelectInfo } = storeToRefs(useAppStore())
const {
  mode,
  models,
} = toRefs(modelSelectInfo)

const t = useGlobalI18n()
const { isDark } = useThemeChange()

const route = useRoute()
const userInfo: any = ref({})
const sections: any = ref([])
const messageId: any = ref('')

onMounted(async () => {
  if (!route.query.shareCode) {
    return
  }
  messageId.value = route.query.shareCode
  const array: any = []
  const { records }: any = await services.post('/gpt/shareChatRecords', {
    shareCode: route.query.shareCode,
    limit: 1000,
    page: 1,
  })
  try {
    const data: any = await services.post('/gpt/shareInfo', {
      shareCode: route.query.shareCode,
      limit: 1000,
      page: 1,
    })

    userInfo.value = {
      ...data,
      avatar: data.userHead ? import.meta.env.VITE_APP_IMAGE_URL + data.userHead : avatar_default,
    }

    // 匹配当前分享对话的逻辑判断 例如 isGenerateImage
    models.value = await services.get('/gpt/modes', {})
    mode.value = records[0].modeId
    const type = records[0].type

    records.reverse().forEach((element: any) => {
      // GEMINI2.0IMAGE分享结果额外处理
      if (type === 29) {
        const questions = JSON.parse(element.question)
        const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
        const questionsFiles = questions.filter((element: { inlineData: { uuid: string, data: string } }) => element?.inlineData)
        const thumbnailUrls = questionsFiles.map((element: { inlineData: { data: string } }) => element?.inlineData?.data)
        array.push({
          ...element,
          question: questionsText,
          avatar: userInfo.value.avatar || avatar_default,
          content: questionsText,
          img: '',
          attachments: [],
          requestId: '',
          receiving: false,
          thumbnailUrls,
          file: [],
          date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
          nickName: userInfo.value.userName,
        })
      } else {
        array.push({
          ...element,
          avatar: userInfo.value.avatar,
          content: element.question,
          img: element.thumbnailUrls ? getFullImageUrl(element.thumbnailUrls[0]) : '',
          attachments: JSON.parse(element.attachments),
          requestId: '',
          receiving: false,
          file: element.requestFile ? [element.requestFile] : [],
          date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
          nickName: userInfo.value.userName,
        })
      }
      if (type === 29) {
        // GEMINI2.0IMAGE分享结果额外处理
        const questions = JSON.parse(element.question)
        const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
        const answer = JSON.parse(element.answer)
        const answerText = answer.map((element: { text: string }) => element.text)
        const inlineData = answer.find((element: { inlineData: { data: string } }) => element.inlineData)?.inlineData
        array.push({
          ...element,
          question: questionsText,
          avatar: avatar_gpt,
          content: answerText?.join(''),
          requestId: '',
          imgText: '',
          img: inlineData?.data ? getFullImageUrl(inlineData?.data) : '',
          receiving: true,
          file: [],
          date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
          nickName: userInfo.value.name,
        })
      } else {
        array.push({
          ...element,
          avatar: avatar_gpt,
          content: element.answer,
          imgText: element.answer.split(IMAGE_SPLIT)[1] || '',
          img: element.thumbnailUrls ? getFullImageUrl(element.thumbnailUrls[0]) : '',
          requestId: '',
          receiving: true,
          file: element.responseFiles
            ? element.responseFiles.map((element: any) => ({ ...element, content: element.chats }))
            : [],
          date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
          nickName: 'Juchats',
        })
      }
    })
    sections.value = array
    nextTick(() => {
      const renderMermaidElements = document.querySelectorAll('.render-mermaid')
      if (renderMermaidElements.length > 0) {
        renderMermaidElements.forEach((element) => {
          (element as HTMLElement).click()
        })
      }
    })
  }
  catch (error) {
    if (error === '未找到数据') {
      notify.error({
        title: t('shareFooter.deleted'),
      })
    }
  }
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/chat-view';
</style>
