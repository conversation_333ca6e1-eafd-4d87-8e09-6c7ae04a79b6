.policy-content {
  a {
    color: #409EFF;
    border-bottom: 1px dashed currentcolor;
    text-decoration: none;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.8;
    }
  }

  // 标题样式
  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 24px 0 16px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 20px 0 14px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 16px 0 12px;
  }

  h4, h5, h6 {
    font-size: 16px;
    font-weight: 600;
    margin: 14px 0 10px;
  }

  // 段落和列表样式
  p, ul, ol {
    line-height: 1.6;
    margin: 12px 0;
  }

  ul, ol {
    padding-left: 24px;
    list-style: disc;

    li {
      margin: 6px 0;
    }
  }

  // 引用块样式
  blockquote {
    margin: 16px 0;
    padding: 0 16px;
    color: #666;
    border-left: 4px solid #ddd;
  }

  // 代码样式
  code {
    background-color: rgba(0, 0, 0, 6%);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
  }

  pre {
    background-color: #f6f8fa;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 16px 0;

    code {
      background: none;
      padding: 0;
    }
  }

  // 表格样式
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;

    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
    }

    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  // 水平线
  hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 24px 0;
  }

  // 图片
  img {
    max-width: 100%;
    height: auto;
    margin: 16px 0;
  }
}
