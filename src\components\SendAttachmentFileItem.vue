<script setup lang="ts">
import type { EUploadUserFile } from '@/types'
import type { PropType } from 'vue'

defineProps({
  file: {
    type: Object as PropType<EUploadUserFile>,
    required: true,
  },
})
</script>

<template>
  <AttachmentCard :upload-status="file.status" :analyze-status="file?.analyzeStatus" :file-name="file.name!" :is-parsing="file.isParsing">
    <template #progress>
      <SendAttachmentProgress
        class="animate-duration-0.2s!"
        :percentage="file.percentage"
      ></SendAttachmentProgress>
    </template>
    <template #success>
      <div
        class="absolute left-0 top-0 h-[100%] w-[100%] flex-c animate-duration-0.3s!"
      >
        <div class="h-70% w-70% flex-c border-rd-50% bg-#ffffff80">
          <i
            class="i-ju-right text-0.7rem c-white"
          ></i>
        </div>
      </div>
    </template>
  </AttachmentCard>
</template>

<style scoped lang="scss">

</style>
