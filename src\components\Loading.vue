<template>
  <div>
    <div className="line"></div>
    <div className="line"></div>
    <div className="line"></div>
    <div className="line"></div>
  </div>
</template>

<style lang="scss" scoped>
    .line {
      position: absolute;
      width: 12px;
      height: 2px;
      animation: spin 1.5s infinite ease;
      background: #63e039;

      &:nth-of-type(2) {
        animation-delay: 0.1s;
        background: #7caaff;
      }

      &:nth-of-type(3) {
        animation-delay: 0.2s;
        background: #fb0;
      }

      &:nth-of-type(4) {
        animation-delay: 0.3s;
        background: #ff4a4a;
      }
    }

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  </style>
