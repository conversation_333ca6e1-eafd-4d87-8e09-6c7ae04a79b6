<template>
  <div class="policy-layout">
    <DotBg class="flex flex-col">
      <div class="pt-52px lt-lg:(flex-c flex-1 flex-col pt-0px)">
        <Loginlogo class="mb-38px lt-lg:(mb-0px)" />
        <LoginTipWithLogo v-if="!ltLg" class="mb-41px flex-c" tip="Juchats One Window, Infinite Worlds!" />
      </div>
      <div class="mx-auto w-1000px lt-lg:(w-full)">
        <div class="flex pl-14px lt-lg:(justify-between px-34px) space-x-32px">
          <router-link
            class="router-link-style"
            active-class="router-link-active"
            to="/policy/consumer-terms"
          >
            Terms of use
          </router-link>
          <router-link
            class="router-link-style"
            active-class="router-link-active"
            to="/policy/privacy-policy"
          >
            Privacy Policy
          </router-link>
          <router-link
            class="router-link-style"
            active-class="router-link-active"
            to="/policy/usage-policy"
          >
            Usage Policy
          </router-link>
        </div>
      </div>
      <div
        class="mx-auto mb-63px mt-22px w-1000px flex-1 overflow-hidden rounded-20px bg-[var(--login-card-bg)] px-85px pb-52px pt-70px shadow-[0_0_40px_0_#00000012] lt-lg:(mb-0 min-h-65% w-full rounded-b-0 p-30px)"
      >
        <router-view />
      </div>
    </DotBg>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import DotBg from '@/views/components/DotBg.vue'

const { breakpoints } = useAppStore()
const ltLg = breakpoints.smaller('lg') // 小于LG尺寸
</script>

<style lang="scss" scoped>
.router-link-active {
  @apply font-bold dark:!text-#fff;
}

.router-link-style {
  @apply text-13px text-#000 transition-all dark:(text-#727272 hover:text-#f1f1f1);
}
</style>
