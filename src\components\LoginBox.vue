<template>
  <div
    class="m-auto w-460px border-rd-7px bg-[#fff] p-70px shadow-[0_50px_30px_0_#00000008]"
  >
    <div class="text-[14px] text-[#37425C]">
      {{ $t("loginBox.mobileLabel") }}
    </div>
    <InputBox
      v-model="params.phone"
      class="mt-[8px]"
      :maxlength="11"
      :placeholder="$t('loginBox.mobilePlaceholder')"
      @keydown.enter="submit"
    ></InputBox>
    <div class="pt-[25px] text-[14px] text-[#37425C]">
      {{ $t("loginBox.codeLabel") }}
    </div>
    <InputBox
      v-model="params.code"
      class="mt-[8px]"
      :maxlength="6"
      :placeholder="$t('loginBox.codePlaceholder')"
      @keydown.enter="submit"
    >
      <template #suffix>
        <div v-debounce="() => getCode()">
          {{
            seconds
              ? $t("loginBox.sendAgain", { seconds })
              : $t("loginBox.getCode")
          }}
        </div>
      </template>
    </InputBox>
    <div v-if="inviteVisible" class="pt-[25px] text-[14px] text-[#37425C]">
      {{ $t("loginBox.inviteCode") }}
    </div>
    <InputBox
      v-if="inviteVisible"
      v-model="params.inviteCode"
      class="mt-[8px]"
      :disabled="disabled"
      :placeholder="$t('emailBox.promptEnterInvitationCode')"
      :maxlength="8"
      @keydown.enter="submit"
    ></InputBox>
    <div
      class="login-box__agreement flex-y-c pt-5"
      :class="{ 'login-box__agreement--active': actived }"
    >
      <div class="checkbox">
        <input v-model="checked" type="checkbox" />
        <Transition name="fade" mode="out-in">
          <img
            v-if="checked"
            src="//hermosssvip.herm.tools/images/checked.png"
            class="checkbox--activited"
            @click="checked = false"
          />
        </Transition>
      </div>
      <div class="pl-2 text-sm">
        <span>{{ $t("loginBox.agree") }}</span>
        <span>&nbsp;</span>
        <span
          class="cursor-pointer font-[600]"
          @click="toStatic('Terms of use.html')"
          >{{ $t("loginBox.terms") }}</span
        >
        <span> & </span>
        <span
          class="cursor-pointer font-[600]"
          @click="toStatic('Privacy policy.html')"
          >{{ $t("loginBox.policy") }}</span
        >
      </div>
    </div>
    <div class="pt-10">
      <ActionButton :loading="loading" @click="submit">{{
        $t("loginBox.loginNow")
      }}</ActionButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { ElNotification } from "element-plus";
import { encode } from "base-64";
import InputBox from "@/components/InputBox.vue";
import ActionButton from "@/components/ActionButton.vue";
import services from "@/common/axios";
import router from "@/router";
import { useGlobalI18n } from "@/hooks/useGlobalI18n";
const route = useRoute();
const t = useGlobalI18n();
// eslint-disable-next-line regexp/no-unused-capturing-group
const regular =
  /^(13\d|14[014-9]|15[0-35-9]|16[2567]|17[0-8]|18\d|19[0-35-9])\d{8}$/;
const checked = ref(false);
const actived = ref(false);
const loading = ref(false);
const seconds = ref(0);
const disabled = ref(false);
const inviteVisible = ref(false);
const params: any = ref({
  phone: "",
  code: "",
});
const submit = async () => {
  if (loading.value) {
    return;
  }
  if (!params.value.phone) {
    ElNotification.error({ title: t("loginBox.mobilePlaceholder") });
    return;
  }
  if (!regular.test(params.value.phone)) {
    ElNotification.error({ title: t("loginBox.mobileFormatTip") });
    return;
  }
  if (!params.value.code) {
    ElNotification.error({ title: t("loginBox.codePlaceholder") });
    return;
  }
  if (params.value.code.length !== 6) {
    ElNotification.error({ title: t("loginBox.codeFormatTip") });
    return;
  }
  if (!checked.value) {
    actived.value = true;
    setTimeout(() => {
      actived.value = false;
    }, 500);
    ElNotification.error({
      title: t("loginBox.agreeTip"),
    });
    return;
  }
  loading.value = true;
  try {
    const { token, exchange }: any = await services.post(
      "/user/loginReg",
      { ...params.value },
      {
        headers: {
          noninductive: true,
        },
      }
    );
    localStorage.setItem("token", token);
    router.push({
      name: exchange ? "Gift" : "Chat",
    });
  } catch ({ code }: any) {
    if ([402].includes(code)) {
      ElNotification.error({
        title: t("loginBox.newUserLoginTip"),
      });
    } else {
      loading.value = false;
    }
  } finally {
    loading.value = false;
  }
};
const toStatic = (string: any) => {
  window.open(`${location.origin}/frame?static=${string}`, "_blank");
};
const getCode = async () => {
  if (!params.value.phone) {
    ElNotification.error({ title: t("loginBox.mobilePlaceholder") });
    return;
  }
  if (!regular.test(params.value.phone)) {
    ElNotification.error({ title: t("loginBox.mobileFormatTip") });
    return;
  }
  if (seconds.value) {
    return;
  }

  const data: any = await services.post(
    "/user/bind/status",
    {
      phone: encode(params.value.phone),
    },
    {
      headers: {
        noninductive: true,
      },
    }
  );

  if (data === "true") {
    ElNotification.success({
      title: t("loginBox.ttpLoginTip"),
    });
    return;
  }
  await services.post(
    "/user/vCode",
    {
      phone: params.value.phone,
      type: 1,
    },
    {
      headers: {
        noninductive: true,
      },
    }
  );
  ElNotification.success({ title: t("loginBox.codeTip") });
  seconds.value = 120;
  const interval = setInterval(() => {
    seconds.value--;
    if (seconds.value <= 0) {
      clearInterval(interval);
    }
  }, 1000);
};
onMounted(() => {
  if (route.query.code) {
    disabled.value = true;
    params.value.inviteCode = JSON.parse(
      JSON.stringify(route.query.code)
    ).substring(0, 8);
  } else {
    disabled.value = false;
  }
});
</script>

<style lang="scss" scoped>
.login-box__agreement--active {
  animation: shake 0.3s;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-10px);
  }

  50% {
    transform: translateX(10px);
  }

  75% {
    transform: translateX(-10px);
  }
}
</style>
