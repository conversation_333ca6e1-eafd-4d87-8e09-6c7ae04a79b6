<template>
  <div
    class="footer-bar fixed bottom-5px left-0 right-0 m-auto text-center text-12px font-400 font-RobotoSlab"
    :class="[colorful ? 'text-[#ffffff]' : 'text-[#a5a5a5]']"
  >
    <template v-if="colorful">
      <!-- <span>© Hermchats · </span> -->
      <span
        class="cursor-pointer text-[#CCCCCC] transition hover:text-[#FFFFFF]"
        @click="toStatic('Privacy policy.html')"
        >{{ $t("footerBar.privacyPolicy") }}</span
      >
      <span> ｜ </span>
      <span
        class="cursor-pointer text-[#CCCCCC] transition hover:text-[#FFFFFF]"
        @click="toStatic('Terms of use.html')"
        >{{ $t("footerBar.termsUse") }}</span
      >
      <div>
        <span>&emsp;</span>
        <!-- <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2022057030号-2</a> -->
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
defineProps({
  colorful: {
    type: Boolean,
    default: false,
  },
});
// import TheWelcome from '@/components/TheWelcome.vue'
const toStatic = (string: any) => {
  window.open(`${location.origin}/frame?static=${string}`, "_blank");
};
</script>

<style lang="scss" scoped>
a {
  color: #fff;
}
</style>
