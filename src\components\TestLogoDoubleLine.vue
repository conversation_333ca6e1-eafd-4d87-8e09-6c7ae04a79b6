<template>
  <div class="relative h-full flex-c">
    <div class="animate__animated animate__fadeOut absolute animate-delay-1700 text-#000 dark:text-#fff">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 255 158" width="56">
        <path class="animated-path" d="M219.6 154.8c-12.6 0-19.3-.2-23.1-3.3-2.7-2.3-4.5-5.5-5-9-.1-.6-.1-1.3 0-2-33.2-.3-60-27.3-60-60.5 0-8.2 1.6-16.2 4.8-23.7-.6-.6-1.1-1.2-1.6-1.8-3.1-4.3-3.3-10.2-3.3-21.1v-4.6c0-10.9.2-16.8 3.3-21.1a17.12 17.12 0 0 1 3.8-3.8C142.8.8 148.9.6 160.2.6h3.5c11.3 0 17.4.2 21.7 3.3h0a17.12 17.12 0 0 1 3.8 3.8c2.2 3.1 2.9 7 3.1 11.9 33.2.2 60.2 27.3 60.2 60.5a59.97 59.97 0 0 1-12.4 36.7l1.6 2.5c7.6 12.1 11.5 18.2 10.8 23.3-.5 3.5-2.2 6.7-5 9-3.8 3.2-10.5 3.3-23.1 3.3h-4.8z" fill="none" stroke="currentColor" stroke-width="2" />
        <path class="animated-path" d="M192.5 140.5c-.1.6 0 1.3 0 1.9.4 3.3 2.1 6.3 4.6 8.4 3.5 2.9 10.1 3.1 22.4 3.1h4.7c12.3 0 18.9-.2 22.4-3.1 2.5-2.1 4.2-5.1 4.6-8.4.6-4.8-3.1-10.7-10.6-22.6l-1.4-2.2C228 132 211 140.3 192.5 140.5zM137.1 57c-3.1 7.3-4.6 15-4.6 23 0 32.7 26.5 59.3 59.2 59.5.8-4.6 4.4-10.3 10.7-20.3 6.8-10.8 10.2-16.2 14.8-17.8a14.67 14.67 0 0 1 9.7 0c4 1.5 7 5.5 12.6 14.5 7.8-10.4 12-22.7 12-35.8 0-32.7-26.5-59.3-59.1-59.5.1 2.3.1 4.8.1 8.2v2.9 1.7c0 10.9-.2 16.8-3.3 21.1a17.12 17.12 0 0 1-3.8 3.8c-4.3 3.1-10.4 3.3-21.7 3.3h-3.5c-11.3 0-17.4-.2-21.7-3.3-.4-.4-.9-.9-1.4-1.3zm84.9 44.5c-1.5 0-3.1.3-4.5.8-4.3 1.5-7.6 6.8-14.3 17.4-6 9.5-9.7 15.4-10.5 19.8 18.2-.2 35-8.5 46.2-22.8-5.5-8.7-8.6-13-12.3-14.4-1.5-.5-3.1-.8-4.6-.8zM137.5 56c.5.5 1.1 1 1.6 1.4 4 2.9 10 3.1 21.1 3.1h3.5c11.1 0 17-.1 21.1-3.1a15.38 15.38 0 0 0 3.6-3.6c2.9-4 3.1-9.8 3.1-20.5v-2.9-1.7l-.1-8.2c-23.4.2-44.5 14.1-53.9 35.5zm22.8-54.5c-11.1 0-17 .1-21.1 3.1a15.38 15.38 0 0 0-3.6 3.6c-2.9 4-3.1 9.8-3.1 20.5v4.6c0 10.7.2 16.5 3.1 20.5.4.5.8 1 1.2 1.4 9.7-21.6 31-35.5 54.6-35.8-.3-5.7-1.1-8.9-2.9-11.3a15.38 15.38 0 0 0-3.6-3.6h0c-4-2.9-10-3.1-21.1-3.1h-3.5z" fill="none" stroke="currentColor" stroke-width="2" />
        <path class="animated-path" d="M121 140.5H1a.47.47 0 0 1-.5-.5v-36a.47.47 0 0 1 .5-.5h120a.47.47 0 0 1 .5.5v36a.47.47 0 0 1-.5.5z" fill="none" stroke="currentColor" stroke-width="2" />
        <rect class="animated-path" x="1.5" y="104.5" width="119" height="35" fill="none" stroke="currentColor" stroke-width="2" />
      </svg>
    </div>

    <div class="animate__animated animate__fadeIn absolute animate-delay-1500 text-#000 dark:text-#fff">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 255 158" width="56"><path d="M137.1 57c-3.1 7.3-4.6 15-4.6 23 0 32.7 26.5 59.3 59.2 59.5.8-4.6 4.4-10.3 10.7-20.3 6.8-10.8 10.2-16.2 14.8-17.8a14.67 14.67 0 0 1 9.7 0c4 1.5 7 5.5 12.6 14.5 7.8-10.4 12-22.7 12-35.8 0-32.7-26.5-59.3-59.1-59.5.1 2.3.1 4.8.1 8.2v2.9 1.7c0 10.9-.2 16.8-3.3 21.1a17.12 17.12 0 0 1-3.8 3.8c-4.3 3.1-10.4 3.3-21.7 3.3h-3.5c-11.3 0-17.4-.2-21.7-3.3-.4-.4-.9-.9-1.4-1.3z" fill="#f43513" /><path d="M121 140.5H1a.47.47 0 0 1-.5-.5v-36a.47.47 0 0 1 .5-.5h120a.47.47 0 0 1 .5.5v36a.47.47 0 0 1-.5.5z" /><path fill="currentColor" d="M1.5 104.5h119v35H1.5z" /></svg>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="scss" scoped>
.animated-path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw 2s cubic-bezier(0.0, 0.015, 0.925, 0.114) forwards;
}

@keyframes draw {
  to {
    stroke-dashoffset: 0;
  }
}
</style>
