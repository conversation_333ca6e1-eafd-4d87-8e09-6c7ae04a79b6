<template>
  <div class="header-bar flex items-center justify-between">
    <div class="logo-wrapper relative">
      <LOGO class="cursor-pointer pb-[5px] pl-[15px]" @click="clickLogo"></LOGO>
      <Transition
        enter-active-class="animate__animated animate__bounceIn"
        leave-active-class="animate__animated animate__bounceOut"
      >
        <span v-if="hasNewFeature" class="new-dot absolute"></span>
      </Transition>
    </div>
    <div
      class="header-bar__info flex items-center justify-end"
      @click="onClickAvatar"
    >
      <AvatarFrame
        v-if="userInfo.thumbnailUrl"
        :src="userInfo.thumbnailUrl"
        name="header-bar__avatar"
        shadow
      ></AvatarFrame>
      <div class="pl-3 pr-1 font-extrabold">
        {{ formatMobile(userInfo.name || userInfo.phone) }}
      </div>
      <i
        :class="`header-bar__icon i-ri-arrow-down-s-line font-bold ${
          menuVisible ? 'header-bar__icon--active' : ''
        }`"
      ></i>
    </div>
  </div>
  <Transition
    enter-active-class="animate__animated animate__flipInX"
    leave-active-class="animate__animated animate__fadeOut"
  >
    <div v-if="hasNewFeature" class="new-features-box">
      <dl>
        <dt class="flex items-center">
          <span> {{ $t("headerBar.updateLog") }}</span>
          <div class="grow"></div>
          <span
            class="icon-close cursor-pointer"
            @click="closeNewFeature"
          ></span>
        </dt>
        <dd
          v-for="(item, index) in newFeatures"
          :key="`features-${index}`"
          v-html="item"
        ></dd>
      </dl>
    </div>
  </Transition>
  <div class="header-bar__placeholder"></div>
  <!-- menu -->
  <Transition name="glide" mode="out-in">
    <div v-if="menuVisible" class="header-bar__menu">
      <div class="h-[65px] flex items-center justify-end">
        <div
          class="mr-[25px] flex cursor-pointer items-center"
          @click="onClickInfo"
        >
          <i class="i-ri-pass-pending-line pr-2 text-xl"></i>
          <span class="text-sm"> {{ $t("headerBar.profileData") }}</span>
        </div>
        <div
          class="mr-[25px] flex cursor-pointer items-center"
          @click="onClickInvite"
        >
          <i class="i-ri-p2p-line pr-2 text-xl"></i>
          <span class="text-sm">{{ $t("headerBar.inviteAction") }}</span>
        </div>
        <div
          class="mr-[25px] flex cursor-pointer items-center"
          @click="onClickMember({ type: 'sub' })"
        >
          <i class="i-ri-vip-line pr-2 text-xl"></i>
          <span class="text-sm">{{ $t("headerBar.manageSubscription") }}</span>
        </div>
        <div
          class="mr-[25px] flex cursor-pointer items-center"
          @click="onClickMember({ type: 'gift' })"
        >
          <i class="i-ri-switch-line pr-2 text-xl"></i>
          <span class="text-sm">{{ $t("headerBar.discoverEasterEgg") }}</span>
        </div>
        <div class="mr-[50px] flex cursor-pointer items-center" @click="logout">
          <i class="i-ri-layout-right-2-line pr-2 text-xl"></i>
          <span class="text-sm">{{ $t("headerBar.actionLogout") }}</span>
        </div>
      </div>
      <div
        class="ml-[50px] h-[190px] border-t border-[#F0F0F0] pb-[40px] pr-[50px] pt-[10px]"
      >
        <el-calendar ref="calendar" v-model="month">
          <template #header>
            <div class="w-full flex items-center justify-between pb-[15px]">
              <div class="text-[24px] text-[#FF5D00] font-['Damion']">
                {{ $t("headerBar.viewStatistics") }}
              </div>
              <div class="flex items-center text-[14px] font-['Roboto_Slab']">
                <i
                  class="i-ri-arrow-drop-left-line cursor-pointer text-[24px] text-[#CBD5E1] transition hover:text-[#000000]"
                  @click="selectDate('prev-month')"
                ></i>
                <span>{{ moment(month).format("YYYY.MM") }}</span>
                <i
                  class="i-ri-arrow-drop-right-line cursor-pointer text-[24px] text-[#CBD5E1] transition hover:text-[#000000]"
                  @click="selectDate('next-month')"
                ></i>
              </div>
            </div>
          </template>
          <template #date-cell="{ data }">
            <i
              v-if="data.type.includes('current-month')"
              class="i-ri-chat-1-fill text-[20px]"
            ></i>
          </template>
        </el-calendar>
        <div class="flex flex-wrap justify-start gap-[15px]">
          <i
            v-for="({ count }, index) in calendarData"
            :key="index"
            :class="`i-ri-chat-1-fill w-[20px] h-[20px] text-[26px] leading-[20px] rotate-y-180 ${formatCount(
              count
            )}`"
          ></i>
        </div>
      </div>
    </div>
  </Transition>
  <!-- cover -->
  <Transition name="fade" mode="out-in">
    <div
      v-if="menuVisible || infoVisible || memberVisible || inviteVisible"
      class="header-bar__mask"
      @click="onClose"
    ></div>
  </Transition>
  <!-- info -->
  <Transition name="side" mode="out-in">
    <div v-if="infoVisible" class="header-bar__side">
      <div class="header-bar__side__title flex items-center justify-between">
        <div class="text-lg font-semibold">
          {{ $t("headerBar.userProfile") }}
        </div>
        <i
          class="i-ri-close-line cursor-pointer font-semibold"
          @click="onClose"
        ></i>
      </div>
      <div class="flex flex-1 justify-center text-center">
        <div>
          <el-upload
            class="m-auto mb-[10px] mt-[50px]"
            :action="action"
            :show-file-list="false"
            :http-request="upload"
          >
            <AvatarFrame
              upload
              :border="userForm.thumbnailUrl.includes('avatar_default')"
              :src="
                userForm.thumbnailUrl.includes('avatar_default')
                  ? avatar_upload
                  : userForm.thumbnailUrl
              "
              name="header-bar__side__avatar"
            ></AvatarFrame>
          </el-upload>
          <div class="flex items-center justify-center gap-x-[20px] pb-[30px]">
            <el-tooltip
              :disabled="!userInfo?.googleUserId"
              :offset="8"
              effect="dark"
              placement="bottom-end"
            >
              <GoogleLogin :callback="bingGoogle">
                <i
                  :class="`${
                    !userInfo?.googleUserId
                      ? 'text-[#CBD5E1]'
                      : 'text-[#000000]'
                  } i-ri-google-fill text-[25px] hover:text-[#000000] transition cursor-pointer`"
                  @click="
                    (event) => {
                      userInfo?.googleUserId && event.stopPropagation();
                    }
                  "
                ></i>
              </GoogleLogin>
              <template #content>
                <div>
                  <span>{{ userInfo?.googleUserName }}</span>
                  <span
                    class="mx-[10px] border border-[0.5px] border-dashed"
                  ></span>
                  <span class="cursor-pointer" @click="unbind(2)">
                    {{ $t("headerBar.actionUnbind") }}</span
                  >
                </div>
              </template>
            </el-tooltip>
            <el-tooltip
              :disabled="!userInfo?.githubUserId"
              :offset="8"
              effect="dark"
              placement="bottom-start"
            >
              <i
                :class="`${
                  !userInfo?.githubUserId ? 'text-[#CBD5E1]' : 'text-[#000000]'
                } i-ri-github-fill text-[26px] hover:text-[#000000] transition cursor-pointer`"
                @click="
                  () => {
                    !userInfo?.githubUserId && bingGithub();
                  }
                "
              ></i>
              <template #content>
                <div>
                  <span>{{ userInfo?.githubUserName }}</span>
                  <span
                    class="mx-[10px] border border-[0.5px] border-dashed"
                  ></span>
                  <span class="cursor-pointer" @click="unbind(3)">
                    {{ $t("headerBar.actionUnbind") }}</span
                  >
                </div>
              </template>
            </el-tooltip>
            <el-tooltip
              :disabled="!userInfo?.email"
              :offset="8"
              effect="dark"
              placement="bottom-start"
            >
              <i
                :class="`${
                  !userInfo?.email ? 'text-[#CBD5E1]' : 'text-[#000000]'
                } i-ri-mail-star-line text-[22px] hover:text-[#000000] transition cursor-pointer pt-[3px]`"
                @click="emailVisible = !emailVisible"
              ></i>
              <template #content>
                <div>
                  <span>{{ userInfo?.email }}</span>
                  <span
                    class="mx-[10px] border border-[0.5px] border-dashed"
                  ></span>
                  <span class="cursor-pointer" @click="unbind(4)">
                    {{ $t("headerBar.actionUnbind") }}</span
                  >
                </div>
              </template>
            </el-tooltip>
          </div>
          <InputBox
            v-model="userForm.name"
            :label="$t('personalCenterInfo.nickName')"
          >
          </InputBox>
          <!-- email -->
          <InputBox
            v-if="[0].includes(emailStep) && emailVisible"
            v-model="userForm.email"
            :placeholder="$t('personalCenterInfo.unbindedEmail')"
            :label="$t('headerBar.labelEmail')"
            disabled
          >
            <template v-if="!userForm.email" #suffix>
              <div v-debounce="() => bindEmail()">
                {{ $t("headerBar.actionBindEmail") }}
              </div>
            </template>
          </InputBox>
          <InputBox
            v-if="[1].includes(emailStep)"
            v-model="userForm.email"
            :placeholder="$t('headerBar.enterYourEmail')"
            :label="$t('headerBar.labelEmail')"
          >
            <template #suffix>
              <div v-debounce="() => bindEmail()">
                {{ $t("headerBar.sendCode") }}
              </div>
            </template>
          </InputBox>
          <InputBox
            v-if="[2].includes(emailStep)"
            v-model="userForm.emailCode"
            :placeholder="$t('headerBar.enterCode')"
            :label="$t('headerBar.labelEmail')"
            @input="onEmailCodeInput"
          >
            <template #suffix>
              <div @click="resendEmailCode">
                {{
                  sendSeconds
                    ? $t("headerBar.resendCode", {
                        sendSeconds,
                      })
                    : $t("headerBar.resendNow")
                }}
              </div>
            </template>
          </InputBox>

          <!-- phone -->
          <InputBox
            v-if="[0].includes(phoneStep) && userForm.phone"
            v-model="userForm.phone"
            :label="$t('headerBar.labelPhone')"
            disabled
          >
            <template #suffix>
              <div v-debounce="() => unBindPhone()" class="text-[#FF0000]">
                {{ $t("headerBar.actionUnbind") }}
              </div>
            </template>
          </InputBox>
          <InputBox
            v-if="[1].includes(phoneStep) && userForm.phone"
            v-model="userForm.phone"
            :label="$t('headerBar.labelPhone')"
            disabled
            :error="sendError"
          >
            <template #suffix>
              <div v-debounce="() => unBindPhone()" class="text-[#FF0000]">
                {{ $t("headerBar.confirmUnbinding") }}
              </div>
            </template>
          </InputBox>
          <!-- <InputBox
            :maxlength="6"
            :autofocus="true"
            v-if="[1].includes(phoneStep)"
            v-model="userForm.code"
            label="短信验证码"
            @input="onCodeInput"
            :error="sendError"
          >
            <template #suffix>
              <div v-debounce="() => resendPhoneCode()">
                {{ sendSeconds ? `重新发送 ${sendSeconds}s` : '重新发送' }}
              </div>
            </template>
          </InputBox>
          <InputBox
            :maxlength="11"
            v-if="[2].includes(phoneStep)"
            v-model="userForm.phone"
            label="新手机号"
            @input="onPhoneInput"
            :error="sendError"
          >
          </InputBox>
          <InputBox
            :autofocus="true"
            :maxlength="6"
            v-if="[3].includes(phoneStep)"
            v-model="userForm.code"
            label="短信验证码"
            @input="onChangeInput"
            :error="sendError"
          >
            <template #suffix>
              <div @click="resendPhoneCode">
                {{ sendSeconds ? `重新发送 ${sendSeconds}s` : '重新发送' }}
              </div>
            </template>
          </InputBox> -->
        </div>
      </div>
      <div class="header-bar__side__button flex items-center justify-between">
        <div class="flex items-center justify-center" @click="onClickInfo">
          {{ $t("headerBar.doNotSave") }}
        </div>
        <div class="flex items-center justify-center" @click="saveInfo">
          {{ $t("headerBar.saveInfo") }}
        </div>
      </div>
    </div>
  </Transition>
  <!-- member -->
  <Transition name="fade" mode="out-in">
    <PackageBox
      v-if="memberVisible"
      :gift-button-visible="giftButtonVisible"
      @close="onClose"
    ></PackageBox>
  </Transition>
  <Transition name="side" mode="out-in">
    <!-- v-if="memberVisible" -->
    <div v-if="false" class="header-bar__side">
      <!-- packages -->
      <template v-if="!packageData.orderNo">
        <div class="header-bar__side__title flex items-center justify-between">
          <div class="text-lg font-semibold">
            {{ $t("headerBar.membershipLevel") }}
          </div>
          <i
            class="i-ri-close-line cursor-pointer font-semibold"
            @click="onClose"
          ></i>
        </div>
        <div class="header-bar__side__container">
          <div class="flex items-start justify-between pt-10">
            <div class="header-bar__side__free">
              <div>{{ $t("headerBar.free") }}</div>
              <div>
                <div>{{ $t("headerBar.dailyConversationLimit") }}</div>
                <div>{{ $t("headerBar.modelUsageRestriction") }}</div>
              </div>
            </div>
            <div class="header-bar__side__plus">
              <div>{{ $t("headerBar.plus") }}</div>
              <div>
                <div>{{ $t("headerBar.unlimitedConversations") }}</div>
                <div>{{ $t("headerBar.priorityOtherModels") }}</div>
                <div>{{ $t("headerBar.betaFeatureAccess") }}</div>
                <div>{{ $t("headerBar.subscriptionOffer") }}</div>
              </div>
            </div>
          </div>
          <template v-if="!packageData.id">
            <div class="pb-4 pt-8 text-lg font-semibold">
              {{ $t("headerBar.subscriptionPlans") }}
            </div>
            <div class="flex flex-wrap items-start justify-between">
              <div
                v-for="(
                  {
                    payAmt,
                    orderNo,
                    expireDays,
                    expireTime,
                    title,
                    limitUse,
                    price,
                    id,
                    type,
                    description,
                    current,
                    actived,
                    disabled,
                    commissionAmt,
                    deductionAmt,
                    paymentIdentificationText,
                  },
                  index
                ) in packages"
                :key="index"
                :class="`header-bar__side__package  ${
                  actived ? 'header-bar__side__package--active' : ''
                } ${
                  disabled || current
                    ? 'header-bar__side__package--disable'
                    : ''
                }`"
                @click="
                  pay({
                    payAmt,
                    orderNo,
                    expireDays,
                    expireTime,
                    title,
                    limitUse,
                    price,
                    id,
                    type,
                    description,
                    current,
                    actived,
                    disabled,
                    commissionAmt,
                    deductionAmt,
                    paymentIdentificationText,
                  })
                "
              >
                <div class="flex justify-between">
                  <div class="header-bar__side__package__month shrink">
                    {{ price }}
                  </div>
                  <div class="flex-1 whitespace-nowrap text-right">
                    <div class="text-lg font-semibold">{{ title }}</div>
                    <div class="pt-1 text-sm">
                      <span class="pr-1 text-[#FF0000]">≈</span>
                      <span class="text-[#2AA50D]">{{
                        $t("headerBar.unlimitedAccess")
                      }}</span>
                    </div>
                    <div class="pt-1 text-sm text-[#64748B]">
                      {{
                        $t("headerBar.daysValidity", {
                          days: expireDays,
                        })
                      }}
                    </div>
                  </div>
                </div>
                <div class="whitespace-nowrap text-center text-sm">
                  {{
                    actived
                      ? `${$t("headerBar.payImmediately")} ${unpaidExpireTime}`
                      : current
                      ? $t("headerBar.currentPlan")
                      : $t("headerBar.subscriptionPlans")
                  }}
                </div>
              </div>
            </div>
            <!-- <div class="header-bar__side__package" @click="toGift">
              <div class="flex justify-between"></div>
              <div class="text-sm text-center whitespace-nowrap">兑换彩蛋</div>
            </div> -->
          </template>
          <Transition name="fade" mode="out-in">
            <div v-if="packageData.id">
              <div class="pb-4 pt-8 text-lg font-semibold">
                {{ $t("headerBar.choosePaymentMethod") }}
              </div>
              <div>
                <div class="mb-10 flex justify-between">
                  <!-- <div
                    class="header-bar__side__code cursor-pointer"
                    v-debounce="() => payMethod('wechat')"
                  >
                    <div class="text-center flex items-center justify-center">
                      <i class="i-ri-wechat-pay-fill text-[#26C73D] text-2xl pr-1"></i>
                      <div class="pl-1">微信支付</div>
                    </div>
                  </div> -->
                  <!-- <div
                    class="header-bar__side__code cursor-pointer"
                    v-debounce="() => payMethod('alipay')"
                  >
                    <div class="text-center flex items-center justify-center">
                      <i class="i-ri-alipay-fill text-[#009CFF] text-2xl pr-1"></i>
                      <div class="pl-1">支付宝支付</div>
                    </div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
        </div>
      </template>
      <!-- pay -->
      <Transition name="fade" mode="out-in">
        <div
          v-if="
            packageData.orderNo &&
            !packageData.status &&
            ![0].includes(packageData.status)
          "
        >
          <div
            class="header-bar__side__title flex items-center justify-between"
          >
            <div class="text-lg font-semibold">
              {{ $t("headerBar.planPayment") }}
            </div>
            <i
              class="i-ri-close-line cursor-pointer font-semibold"
              @click="onClose"
            ></i>
          </div>
          <div class="header-bar__side__container">
            <div
              class="header-bar__side__card mt-8 flex items-center justify-center"
            >
              <div class="header-bar__side__card__title pr-8">
                {{ packageData.payAmt }}
              </div>
              <div class="pl-8 text-right text-[#ffffff]">
                <div class="pt-1 text-2xl">{{ packageData.title }}</div>
                <div class="pt-1 text-xl">
                  ≈ {{ $t("headerBar.unlimitedAccess") }}
                </div>
                <div class="pt-1 text-xl">
                  {{
                    $t("headerBar.daysValidity", {
                      days: packageData.expireDays,
                    })
                  }}
                </div>
              </div>
            </div>
            <div class="pb-8 pt-8 text-center text-sm text-[#CBD5E1]">
              - {{ $t("headerBar.orderDetails") }} -
            </div>
            <div class="header-bar__side__box">
              <div class="flex items-center text-sm">
                <div>{{ $t("packageBox.orderNumber") }}：</div>
                <div class="text-[#B3B3B3]">#{{ packageData.orderNo }}</div>
              </div>
              <div class="flex items-center pt-3 text-sm">
                <div>{{ $t("headerBar.paymentTimer") }}：</div>
                <div class="text-[#FF0000]">#{{ expireTime }}</div>
              </div>
              <div
                v-if="discount.deductionAmt"
                class="flex items-center pt-3 text-sm"
              >
                <div>{{ $t("headerBar.packageOffset") }}：</div>
                <div class="text-[#2E8400]">
                  - {{ discount.deductionAmt
                  }}{{ $t("headerBar.unitCurrency") }}
                </div>
                <div>{{ $t("headerBar.dailyPackageDeduction") }}</div>
              </div>
              <div
                v-if="discount.commissionAmt"
                class="flex items-center pt-3 text-sm"
              >
                <div>{{ $t("headerBar.rewardOffset") }}：</div>
                <div class="text-[#FF8D00]">
                  - {{ discount.commissionAmt
                  }}{{ $t("headerBar.unitCurrency") }}
                </div>
                <div class="pl-1">
                  ≈ {{ discount.commissionAmt
                  }}{{ $t("headerBar.labelReward") }}
                </div>
              </div>
              <div class="pt-3 text-sm">
                <span class="pr-1 text-[#D7D7D7]">// </span>
                <span class="text-[#707070]">{{
                  packageData.description
                }}</span>
              </div>
            </div>
            <div class="pb-8 pt-8 text-center text-13 text-sm text-[#CBD5E1]">
              - {{ $t("headerBar.choosePaymentMethod") }} -
            </div>
            <div class="mb-10 flex justify-between">
              <div v-if="packageData.qrCodeUrl" class="header-bar__side__code">
                <div class="mb-3">
                  <img :src="packageData.qrCodeUrl" alt="" />
                </div>
                <div class="flex items-center justify-center text-center">
                  <i
                    class="i-ri-wechat-pay-fill pr-1 text-2xl text-[#26C73D]"
                  ></i>
                  <div class="pl-1">{{ $t("headerBar.weChatPayment") }}</div>
                </div>
              </div>
              <div v-if="packageData.formBody" class="header-bar__side__code">
                <div class="pb-3">
                  <iframe
                    ref="alipayFrame"
                    scrolling="no"
                    :srcdoc="packageData.formBody"
                    frameborder="0"
                  ></iframe>
                </div>
                <div class="flex items-center justify-center text-center">
                  <i class="i-ri-alipay-fill pr-1 text-2xl text-[#009CFF]"></i>
                  <div class="pl-1">{{ $t("headerBar.alipayPayment") }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
      <!-- status -->
      <Transition name="fade" mode="out-in">
        <div v-if="packageData.status">
          <div
            class="header-bar__side__title flex items-center justify-between"
          >
            <div class="text-lg font-semibold">
              {{ $t("headerBar.subscriptionStatus") }}
            </div>
            <i
              class="i-ri-close-line cursor-pointer font-semibold"
              @click="onClose"
            ></i>
          </div>
          <div ref="lottieElement" class="header-bar__side__lottie">
            <div class="header-bar__side__lottie__title">Plus</div>
            <div class="header-bar__side__lottie__subtitle">
              {{ $t("headerBar.plusUserWelcome") }}
            </div>
          </div>
          <div class="header-bar__side__container">
            <div class="pb-8 pt-8 text-center text-sm text-[#CBD5E1]">
              - {{ $t("headerBar.orderStatus") }} -
            </div>
            <div class="header-bar__side__box">
              <div class="flex items-center text-sm">
                <div>{{ $t("packageBox.orderNumber") }}：</div>
                <div class="text-[#B3B3B3]">#{{ packageData.orderNo }}</div>
              </div>
              <div class="flex items-center pb-2 pt-3 text-sm">
                <div>{{ $t("headerBar.paymentStatus") }}：</div>
                <div :class="mapping[packageData.status].color">
                  {{ mapping[packageData.status].status }}
                </div>
              </div>
              <div class="flex items-center pb-2 pt-3 text-sm">
                <div>{{ $t("headerBar.paymentTotal") }}：</div>
                <div>
                  {{ packageData.price }}
                </div>
              </div>
              <div class="flex items-center pb-2 pt-3 text-sm">
                <div>{{ $t("headerBar.subscriptionPlans") }}：</div>
                <div>
                  {{ packageData.title }}
                </div>
              </div>
              <div class="pt-2 text-sm">
                <span class="pr-1 text-[#D7D7D7]">// </span>
                <span class="text-[#707070]">{{
                  packageData.description
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
  <!-- invite -->
  <Transition name="side" mode="out-in">
    <div v-if="inviteVisible" class="header-bar__side">
      <div class="header-bar__side__title flex items-center justify-between">
        <div class="text-lg font-semibold">{{ $t("userInfo.myInvite") }}</div>
        <i
          class="i-ri-close-line cursor-pointer font-semibold"
          @click="onClose"
        ></i>
      </div>
      <div class="header-bar__side__container">
        <div class="flex items-start justify-between pt-10">
          <div class="header-bar__side__invite__card">
            <div>
              <div class="header-bar__side__invite__card__title">
                {{ userInfo?.inviteCount || 0 }}
              </div>
              <div
                class="header-bar__side__invite__card__subtitle font-semibold"
              >
                {{ $t("headerBar.numberInvited") }}
              </div>
            </div>
          </div>
          <div class="header-bar__side__invite__card">
            <div>
              <div class="header-bar__side__invite__card__title">
                {{ userInfo?.commission || 0 }}
              </div>
              <div
                class="header-bar__side__invite__card__subtitle text-destructive-300 font-semibold"
              >
                {{ $t("headerBar.myReward") }}
              </div>
            </div>
          </div>
        </div>
        <div class="pb-6 pt-10 text-lg font-semibold">
          {{ $t("headerBar.myInviteCode") }}
        </div>
        <div class="header-bar__side__invite__code flex justify-between">
          <img v-if="inviteQRCode" :src="inviteQRCode" alt="" />
          <div class="flex flex-1 flex-col justify-between">
            <div>
              <div class="header-bar__side__invite__code__title pb-1 text-xl">
                {{ userForm.inviteCode }}
              </div>
              <div class="header-bar__side__invite__code__subtitle text-sm">
                {{ inviteLink }}
              </div>
            </div>
            <div
              class="header-bar__side__invite__code__button"
              @click="copy(inviteLink)"
            >
              {{ $t("headerBar.copyInviteLink") }}
            </div>
          </div>
        </div>
        <div v-if="commissions.length" class="pt-8 text-sm">
          <el-row class="pb-1" :gutter="20" :infinite-scroll-immediate="false">
            <el-col :span="5"
              ><div>{{ $t("headerBar.nicknameUser") }}</div></el-col
            >
            <el-col :span="1"><el-divider direction="vertical" /></el-col>
            <el-col :span="5"
              ><div class="text-center">
                {{ $t("headerBar.buySubscriptionPlan") }}
              </div></el-col
            >
            <el-col :span="1"><el-divider direction="vertical" /></el-col>
            <el-col :span="5"
              ><div class="text-center">
                {{ $t("headerBar.receiveReward") }}
              </div></el-col
            >
            <el-col :span="1"><el-divider direction="vertical" /></el-col>
            <el-col :span="5"
              ><div class="text-right">
                {{ $t("headerBar.timeOfPurchase") }}
              </div></el-col
            >
          </el-row>
          <div
            v-infinite-scroll="getCommissions"
            :infinite-scroll-disabled="inviteLoading"
          >
            <el-row
              v-for="(
                { fromUserName, fromUserPhone, packageName, amount, time },
                index
              ) in commissions"
              :key="index"
              class="header-bar__side__invite__list pt-4 text-[#596578]"
              :gutter="20"
            >
              <el-col :span="5">
                <div>{{ fromUserName || fromUserPhone }}</div>
              </el-col>
              <el-col :span="1"></el-col>
              <el-col :span="5">
                <div class="text-center">{{ packageName }}</div>
              </el-col>
              <el-col :span="1"></el-col>
              <el-col :span="5">
                <div class="text-center">{{ amount }}</div>
              </el-col>
              <el-col :span="1"></el-col>
              <el-col :span="5">
                <div class="text-right">
                  {{ moment(time).format("YYYY.MM.DD") }}
                </div>
              </el-col>
            </el-row>
            <div class="pt-10"></div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import services from "@/common/axios";
import avatar_upload from "@/assets/images/avatar_upload.svg";
import InputBox from "@/components/InputBox.vue";
import PackageBox from "@/components/PackageBox.vue";
import { ElNotification } from "element-plus";
import { useUserStore } from "@/stores/user";
import moment from "moment";
import {
  copy,
  expire,
  filterTime,
  formatMobile,
  generateQRCode,
  lottieAnimation,
} from "@/common";
import type { CalendarDateType, CalendarInstance } from "element-plus";
import { storeToRefs } from "pinia";
import { useGlobalI18n } from "@/hooks/useGlobalI18n";

const t = useGlobalI18n();
const emits = defineEmits(["logo"]);
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const hasNewFeature = ref(false);
const regular =
  /^(13\d|14[014-9]|15[0-35-9]|16[2567]|17[0-8]|18\d|19[0-35-9])\d{8}$/;
const emailRegular = /^[\w.%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const action = `/file/upload`;
const sendSeconds = ref(0);
const sendError = ref("");
const commissions: any = ref([]);
const inviteLink = ref("");
const inviteQRCode = ref("");
const invitePage = ref(0);
const inviteLoading = ref(false);
const lottieElement = ref();
const expireTime = ref("");
const month = ref(new Date());
const calendar = ref<CalendarInstance>();
const calendarData: any = ref([]);
const unpaidExpireTime = ref("");
const phoneStep = ref(0);
const emailStep = ref(0);
const lottieSVG = ref();
const alipayFrame = ref();
const router = useRouter();
const route: any = useRoute();
const infoVisible = ref(false);
const menuVisible = ref(false);
const memberVisible = ref(false);
const inviteVisible = ref(false);
const emailVisible = ref(false);
const giftButtonVisible = ref(false);
const packages = ref([]);
const packageData: any = ref({});
const countdownInterval = ref();
const verifyInterval = ref();
const unpaidInterval = ref();
const orderInterval = ref();
const imageId = ref("");
const userForm: any = ref({});
const discount: any = ref({});

const newFeatures = ref([
  t("headerBar.brandReconstruction"),
  t("headerBar.modelSupportGPT"),
  t("headerBar.modelSupportClaude"),
  t("headerBar.modelSupportMixtral"),
  t("headerBar.modelSupportDALL"),
  t("headerBar.modelSupportLlama"),
  t("headerBar.optimizeLeTax"),
  t("headerBar.optimizeMarkdown"),
  t("headerBar.optimizeConnection"),
  t("headerBar.fixBugs"),
  t("headerBar.subscriptionOpen"),
  t("headerBar.subscriptionUnlimited"),
  t("headerBar.subscriptionHistory"),
]);
const mapping: any = ref({
  0: {
    status: t("headerBar.paymentProcessing"),
    color: "text-[#00BC32]",
  },
  1: {
    status: t("headerBar.paymentSuccessful"),
    color: "text-[#00BC32]",
  },
  2: {
    status: t("headerBar.paymentCancelled"),
    color: "text-[#FF0000]",
  },
  3: {
    status: t("headerBar.transactionClosed"),
    color: "text-[#FF0000]",
  },
  4: {
    status: t("headerBar.refundedAmount"),
    color: "text-[#FF0000]",
  },
  5: {
    status: t("headerBar.contactSupport"),
    color: "text-[#FF0000]",
  },
});
watch(infoVisible, (newValue) => {
  if (newValue) {
    getInfo();
  }
});
watch(month, (newValue) => {
  if (newValue) {
    getMonth(newValue);
  }
});

function closeNewFeature() {
  localStorage.setItem("nfrd", "1");
  hasNewFeature.value = false;
}
const resendPhoneCode = async () => {
  if (sendSeconds.value) {
    return;
  }
  await services.post("/user/vCode", {
    phone: userForm.value.phone,
    type: 2,
  });
  if (verifyInterval.value) {
    clearInterval(verifyInterval.value);
  }
  sendSeconds.value = 120;
  verifyInterval.value = setInterval(() => {
    if (sendSeconds.value <= 0) {
      clearInterval(verifyInterval.value);
      return;
    }
    sendSeconds.value--;
  }, 1000);
};
const modifyPhoneNumber = async () => {
  await services.post("/user/vCode", {
    phone: userForm.value.phone,
    type: 2,
  });
  if (verifyInterval.value) {
    clearInterval(verifyInterval.value);
  }
  sendSeconds.value = 120;
  verifyInterval.value = setInterval(() => {
    if (sendSeconds.value <= 0) {
      clearInterval(verifyInterval.value);
      return;
    }
    sendSeconds.value--;
  }, 1000);
  phoneStep.value++;
};
const onCodeInput = async (param: any) => {
  if (param.length === 6) {
    try {
      await services.post("/user/changePhone/Step1", {
        code: param,
      });
      sendError.value = "";
      userForm.value.phone = "";
      phoneStep.value++;
    } catch (error: any) {
      sendError.value = error;
    }
  }
};

const onPhoneInput = async (param: any) => {
  if (param.length === 11) {
    if (regular.test(param)) {
      try {
        userForm.value.code = "";
        modifyPhoneNumber();
      } catch (error: any) {
        sendError.value = error;
      }
    } else {
      sendError.value = t("headerBar.phoneFormatError");
    }
  }
};

const unbind = async (untieType: number) => {
  // 1:手机 2:Google 3:Github:4:Email
  await services.post("/user/untie", {
    untieType,
  });
  ElNotification.success({ title: t("headerBar.successfulUnbind") });
  getInfo();
};

const bindEmail = async () => {
  if ([0].includes(emailStep.value)) {
    emailStep.value++;
  } else {
    if (!emailRegular.test(userForm.value.email)) {
      ElNotification.error({ title: t("headerBar.enterCorrectEmailAccount") });
    } else {
      await services.post("/user/email/sendCaptcha", {
        email: userForm.value.email,
        type: 2,
      });
      if (verifyInterval.value) {
        clearInterval(verifyInterval.value);
      }
      sendSeconds.value = 120;
      verifyInterval.value = setInterval(() => {
        if (sendSeconds.value <= 0) {
          clearInterval(verifyInterval.value);
          return;
        }
        sendSeconds.value--;
      }, 1000);
      emailStep.value++;
    }
  }
};

const resendEmailCode = async () => {
  if (sendSeconds.value) {
    return;
  }
  await services.post("/user/email/sendCaptcha", {
    email: userForm.value.email,
    type: 2,
  });
  if (verifyInterval.value) {
    clearInterval(verifyInterval.value);
  }
  sendSeconds.value = 120;
  verifyInterval.value = setInterval(() => {
    if (sendSeconds.value <= 0) {
      clearInterval(verifyInterval.value);
      return;
    }
    sendSeconds.value--;
  }, 1000);
};

const onEmailCodeInput = async (param: any) => {
  if (param.length === 6) {
    try {
      await services.post("/user/bind/email", {
        code: param,
        email: userForm.value.email,
      });
      ElNotification.success({ title: t("headerBar.successfulBinding") });
      sendError.value = "";
      emailStep.value = 0;
      getInfo();
    } catch (error: any) {
      sendError.value = error;
    }
  }
};

const unBindPhone = async () => {
  if ([0].includes(phoneStep.value)) {
    phoneStep.value++;
    sendError.value = t("headerBar.confirmUnbindPhone");
  } else {
    unbind(1);
  }
};

const onChangeInput = async (param: any) => {
  if (param.length === 6) {
    try {
      await services.post("/user/changePhone/Step2", {
        code: param,
        phone: userForm.value.phone,
      });
      ElNotification.success({ title: t("headerBar.successfulModification") });
      sendError.value = "";
      phoneStep.value = 0;
      getInfo();
    } catch (error: any) {
      sendError.value = error;
    }
  }
};

const onClickInfo = () => {
  menuVisible.value = false;
  memberVisible.value = false;
  inviteVisible.value = false;
  infoVisible.value = !infoVisible.value;
};
const onClickMember = async ({ type }: { type: string }) => {
  // if (!memberVisible.value) {
  //   await getPackages()
  // }
  if (type === "gift") {
    giftButtonVisible.value = true;
  } else {
    giftButtonVisible.value = false;
  }
  infoVisible.value = false;
  inviteVisible.value = false;
  menuVisible.value = false;
  if (type && memberVisible.value) {
    memberVisible.value =
      (type === "gift" && giftButtonVisible.value) ||
      (type === "sub" && !giftButtonVisible.value);
  } else {
    memberVisible.value = !memberVisible.value;
  }
};
const onClickInvite = async () => {
  if (!inviteVisible.value) {
    await getCommissions();
  }
  menuVisible.value = false;
  memberVisible.value = false;
  infoVisible.value = false;
  inviteVisible.value = !inviteVisible.value;
};
const onClickAvatar = () => {
  // if (infoVisible.value || memberVisible.value || inviteVisible.value) {
  //   return
  // }
  menuVisible.value = !menuVisible.value;
};
const saveInfo = async () => {
  await services.post("/user/updInfo", {
    name: userForm.value.name,
    uuid: imageId.value,
  });
  onClickInfo();
  getInfo();
  ElNotification.success({ title: t("headerBar.infoSavedSuccessfully") });
};
const onClose = () => {
  menuVisible.value = false;
  infoVisible.value = false;
  memberVisible.value = false;
  inviteVisible.value = false;
  phoneStep.value = 0;
  emailStep.value = 0;
  clearInterval(orderInterval.value);
};
const countdown = () => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
    expireTime.value = "";
  }
  const date = packageData.value.expireTime;
  const expire = moment(date);
  countdownInterval.value = setInterval(() => {
    const minutes = expire.diff(new Date(), "minutes") % 60;
    const seconds = expire.diff(new Date(), "seconds") % 60;
    if (minutes < 0 || seconds < 0) {
      clearInterval(countdownInterval.value);
    }
    expireTime.value = `${filterTime(minutes)}:${filterTime(seconds)}`;
  }, 100);
};
const logout = () => {
  localStorage.removeItem("token");
  router.replace({ name: "Home" });
};
const upload = async (data: any) => {
  const image = new Image();
  image.src = URL.createObjectURL(data.file);
  const canvas = document.createElement("canvas");
  const ctx: any = canvas.getContext("2d");

  image.onload = async function () {
    const minSide = Math.min(image.width, image.height);
    canvas.width = minSide;
    canvas.height = minSide;

    const offsetX = (image.width - minSide) / 2;
    const offsetY = (image.height - minSide) / 2;

    ctx.drawImage(
      image,
      offsetX,
      offsetY,
      minSide,
      minSide,
      0,
      0,
      minSide,
      minSide
    );

    await canvas.toBlob(async (blob: any) => {
      const formdata = new FormData();
      formdata.append(
        "file",
        new File([blob], "edited_image.png", { type: "image/png" })
      );
      const { thumbnailUrl, uuid }: any = await services.post(
        data.action,
        formdata
      );
      userForm.value.thumbnailUrl =
        import.meta.env.VITE_APP_IMAGE_URL + thumbnailUrl;
      imageId.value = uuid;
    }, "image/png");
  };
};
const pay = async (params: any) => {
  if (params.disabled || params.current) {
    return;
  }
  packageData.value = { ...params };
  if (params.actived) {
    payMethod("repayment");
  }
};
const payMethod = async (param: any) => {
  let payAmt: any,
    orderNo: any,
    qrCodeUrl: any,
    formBody: any,
    expireTime: any,
    commissionAmt: any,
    deductionAmt: any;
  if (["wechat"].includes(param)) {
    const response: any = await services.post(
      `${import.meta.env.VITE_APP_PAY_URL}/order/wx/native/pay`,
      {
        packageId: packageData.value.id,
      }
    );
    payAmt = response.payAmt;
    orderNo = response.orderNo;
    qrCodeUrl = response.qrCodeUrl;
    expireTime = response.expireTime;
    commissionAmt = response.commissionAmt;
    deductionAmt = response.deductionAmt;
  } else if (["alipay"].includes(param)) {
    const response: any = await services.post(
      `${import.meta.env.VITE_APP_PAY_URL}/order/ali/web/pay`,
      {
        packageId: packageData.value.id,
      }
    );
    payAmt = response.payAmt;
    orderNo = response.orderNo;
    formBody = response.formBody;
    expireTime = response.expireTime;
    commissionAmt = response.commissionAmt;
    deductionAmt = response.deductionAmt;
  } else if (["repayment"].includes(param)) {
    if (packageData.value.type) {
      formBody = packageData.value.paymentIdentificationText;
    } else {
      qrCodeUrl = packageData.value.paymentIdentificationText;
    }
    payAmt = packageData.value.payAmt;
    orderNo = packageData.value.orderNo;
    expireTime = packageData.value.expireTime;
    commissionAmt = packageData.value.commissionAmt;
    deductionAmt = packageData.value.deductionAmt;
  }
  packageData.value = {
    ...packageData.value,
    payAmt,
    orderNo,
    formBody,
    qrCodeUrl: qrCodeUrl && (await generateQRCode(qrCodeUrl)),
    expireTime,
  };
  discount.value = {
    commissionAmt,
    deductionAmt,
  };
  if (formBody) {
    const html = document.createElement("div");
    html.innerHTML = formBody;
  }
  countdown();
  getStatus(orderNo);
};
const getStatus = async (value: any) => {
  if (orderInterval.value) {
    clearInterval(orderInterval.value);
  }
  orderInterval.value = setInterval(async () => {
    const { status, price }: any = await services.post(
      `${import.meta.env.VITE_APP_PAY_URL}/order/detail`,
      {
        orderNo: value,
      },
      {
        headers: {
          noninductive: true,
        },
      }
    );
    if ([1].includes(status)) {
      getInfo();
      packageData.value.status = status;
      packageData.value.price = price;
      clearInterval(orderInterval.value);
      setTimeout(() => {
        lottieSVG.value = lottieAnimation(lottieElement.value);
      }, 0);
    }
  }, 2000);
};
const unpaiddown = (param: any) => {
  if (unpaidInterval.value) {
    clearInterval(unpaidInterval.value);
    unpaidExpireTime.value = "";
  }
  const expire = moment(param);
  unpaidInterval.value = setInterval(() => {
    const minutes = expire.diff(new Date(), "minutes") % 60;
    const seconds = expire.diff(new Date(), "seconds") % 60;
    if (minutes < 0 || seconds < 0) {
      getPackages();
      clearInterval(orderInterval.value);
      clearInterval(unpaidInterval.value);
    }
    unpaidExpireTime.value = `${filterTime(minutes)}:${filterTime(seconds)}`;
  }, 100);
};
const getPackages = async () => {
  packageData.value = {};
  const orderData: any = await services.post(
    `${import.meta.env.VITE_APP_PAY_URL}/order/notCompletedRecord`
  );
  const packagesData: any = await services.get(
    `${import.meta.env.VITE_APP_PAY_URL}/order/packages`,
    {}
  );
  packages.value = packagesData.map((element: any) => {
    const unpaid = !!(
      orderData &&
      orderData.expireTime &&
      moment(orderData.expireTime).isAfter(moment(new Date()))
    );
    if (unpaid && element.id === orderData.packageId) {
      unpaiddown(orderData.expireTime);
      return {
        ...element,
        ...orderData,
        payAmt: orderData.price,
        current: false,
        disabled: false,
        actived: true,
      };
    } else {
      return {
        ...element,
        disabled: unpaid,
        actived: false,
        current: userInfo.value.discountPackage === element.title,
      };
    }
  });
};
const getCommissions = async () => {
  invitePage.value++;
  inviteLoading.value = true;
  const { records }: any = await services.post("/user/commissions", {
    limit: 10,
    page: invitePage.value,
  });
  if (!records.length) {
    return;
  }
  commissions.value.push(...records);
  inviteLoading.value = false;
};
const getInfo = async () => {
  await userStore.getUserInfo();
  inviteLink.value = `${location.origin}?code=${userInfo.value.inviteCode}`;
  inviteQRCode.value = await generateQRCode(inviteLink.value);
  userForm.value = userInfo.value;
};
const getMonth = async (month: any) => {
  const formatMonth = moment(month).format("YYYY-MM");
  const daysInMonth = moment(formatMonth).daysInMonth();
  const array = [];
  for (let i = 1; i <= daysInMonth; i++) {
    const currentDate = moment(formatMonth).date(i);
    array.push({
      time: currentDate.format("YYYY-MM-DD"),
      count: 0,
    });
  }

  const data: any = await services.post(
    "/user/monthUsage",
    {
      month: formatMonth,
    },
    {
      headers: {
        noninductive: true,
      },
    }
  );
  calendarData.value = array.map((arrayElement: any) => ({
    time: arrayElement.time,
    count:
      data.find((dataElement: any) => dataElement.time === arrayElement.time)
        ?.count || 0,
  }));
};
const selectDate = (val: CalendarDateType) => {
  if (!calendar.value) {
    return;
  }
  const y = Number(moment(month.value).format("YYYY"));
  const m = Number(moment(month.value).format("MM"));
  const new_y = Number(moment(new Date()).format("YYYY"));
  const new_m = Number(moment(new Date()).format("MM"));
  if (y >= new_y && m >= new_m && val === "next-month") {
    return;
  }
  calendar.value.selectDate(val);
};
const formatCount = (val: any) => {
  if (val >= 50) {
    return `text-[#FF5D00]`;
  } else if (val >= 30) {
    return `text-[#FFAE80]`;
  } else if (val >= 10) {
    return `text-[#FFEFE6]`;
  } else {
    return `text-[#F6F6F6]`;
  }
};
const bingGithub = () => {
  location.href = `https://github.com/login/oauth/authorize?client_id=${
    import.meta.env.VITE_APP_GITHUB_CLIENT
  }&redirect_uri=${location.origin + `/oauth?from=HEADER`}`;
};
const bingGoogle = async ({ code }: any) => {
  await services.post("/user/bind/thirdParty/login", {
    googleCode: code,
    googleRedirectUri: location.origin,
  });
  getInfo();
  ElNotification.success({ title: t("oauthView.bindSucceed") });
};
const clickLogo = () => {
  if (["Chat"].includes(route.name)) {
    emits("logo");
  } else {
    router.push({
      name: "Chat",
    });
  }
};
defineExpose({
  getInfo,
  onClose,
  onClickMember,
});
onMounted(async () => {
  if (!localStorage.getItem("nfrd")) {
    hasNewFeature.value = true;
  }

  await getInfo();
  expire();
  getMonth(new Date());
});
</script>

<style lang="scss">
.new-features-box {
  position: absolute;
  left: calc(260px + 30px);
  top: calc(80px + 30px);
  z-index: 100;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(black, 0.1);
  border-radius: 10px;
  min-width: 380px;

  .icon-close {
    font-size: 20px;
  }

  dl {
    dt,
    dd {
      &::before {
        content: "✨";
        margin-right: 10px;
      }
    }

    dt {
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #fafafa;
      margin-bottom: 10px;
    }

    dd {
      span {
        font-weight: bold;
      }

      .red {
        color: #f00;
      }

      .blue {
        color: #155eef;
      }

      .green {
        color: #008f38;
      }

      font-size: 14px;
      color: #575f72;
      padding: 7px 0;
      margin-left: 30px;
    }
  }
}
</style>

<style lang="scss" scoped>
.logo-wrapper {
  .new-dot {
    width: 10px;
    height: 10px;
    background: #f00;
    border-radius: 50%;
    left: calc(100% + 10px);
    top: 10px;
  }
}
</style>
