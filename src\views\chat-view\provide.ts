import type { SSEResponse } from '../../types'
import { provide } from 'vue'

const KEY = Symbol('chat-view')

interface ChatViewProvide {
  createLeptonDialog: (id: number) => Promise<void>
  onAssistantMessage: (response: SSEResponse, userMessage: string) => void
  onReceiveDone: () => void
  updateArtifactHtml: (artifactId: number, newHtml: string) => void
}

export const provideChatView = (provideAction: ChatViewProvide) => {
  provide(KEY, provideAction)
}

export const injectChatView = () => {
  return inject(KEY) as ChatViewProvide
}
