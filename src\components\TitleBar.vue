<script setup lang="ts">
import ControlButton from '@/components/ControlButton.vue'
</script>

<template>
  <div class="flex items-center justify-between px-100px">
    <div></div>
    <img class="w-400px" src="//hermosssvip.herm.tools/images/logo_title.png" alt="" />
    <ControlButton class="relative z-1 mb-15px"></ControlButton>
  </div>
</template>

<style lang="scss" scoped>
.title-bar {
  & > div {
    min-width: 270px;
  }
}
</style>
