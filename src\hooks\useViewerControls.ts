import type { Ref } from 'vue'
import { toPng } from 'html-to-image'

interface UseViewerControlsOptions {
  containerRef: Ref<HTMLElement | null>
  contentRef: Ref<HTMLElement | null>
  onReset?: () => void
}

export function useViewerControls({ containerRef, contentRef, onReset }: UseViewerControlsOptions, downloadName: string) {
  const scale = ref(1)
  const position = ref({ x: 0, y: 0 })
  const isDragging = ref(false)
  const dragStart = ref({ x: 0, y: 0 })
  const dragStartPosition = ref({ x: 0, y: 0 })
  const isFullscreen = ref(false)
  const touchStartDistance = ref(0)
  const lastTouchScale = ref(1)

  const zoomIn = () => {
    scale.value = Math.min(scale.value + 0.1, 3)
  }

  const zoomOut = () => {
    scale.value = Math.max(scale.value - 0.1, 0.5)
  }

  const resetZoom = () => {
    scale.value = 1
    position.value = { x: 0, y: 0 }
    if (onReset) {
      onReset()
    }
  }

  const startDrag = (e: MouseEvent | TouchEvent) => {
    isDragging.value = true
    const clientX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
    const clientY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
    dragStart.value = { x: clientX, y: clientY }
    dragStartPosition.value = { x: position.value.x, y: position.value.y }
  }

  const onDrag = (e: MouseEvent | TouchEvent) => {
    if (!isDragging.value) { return }
    const clientX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
    const clientY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
    const dx = clientX - dragStart.value.x
    const dy = clientY - dragStart.value.y
    position.value = {
      x: dragStartPosition.value.x + dx / scale.value,
      y: dragStartPosition.value.y + dy / scale.value,
    }
  }

  const stopDrag = () => {
    isDragging.value = false
  }

  const handleTouchStart = (e: TouchEvent) => {
    if (e.touches.length === 2) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      touchStartDistance.value = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY,
      )
      lastTouchScale.value = scale.value
    }
    else if (e.touches.length === 1) {
      startDrag(e)
    }
  }

  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault()
    if (e.touches.length === 2) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      const currentDistance = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY,
      )
      const scaleFactor = currentDistance / touchStartDistance.value
      const newScale = Math.min(Math.max(lastTouchScale.value * scaleFactor, 0.5), 3)
      if (!containerRef.value) { return }
      const rect = containerRef.value.getBoundingClientRect()
      const centerX = (touch1.clientX + touch2.clientX) / 2
      const centerY = (touch1.clientY + touch2.clientY) / 2
      const relativeX = centerX - rect.left
      const relativeY = centerY - rect.top
      const scaleChange = newScale - scale.value
      position.value = {
        x: position.value.x - (relativeX - rect.width / 2) * scaleChange / newScale,
        y: position.value.y - (relativeY - rect.height / 2) * scaleChange / newScale,
      }
      scale.value = newScale
    }
    else if (e.touches.length === 1) {
      onDrag(e)
    }
  }

  const handleTouchEnd = () => {
    stopDrag()
    touchStartDistance.value = 0
    lastTouchScale.value = scale.value
  }

  const handleWheel = (e: WheelEvent) => {
    if (!containerRef.value) { return }
    if (e.ctrlKey || e.metaKey || e.deltaY % 1 !== 0) {
      e.preventDefault()
      const delta = -e.deltaY * 0.01
      const newScale = Math.min(Math.max(scale.value + delta, 0.5), 3)
      const rect = containerRef.value.getBoundingClientRect()
      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top
      const scaleChange = newScale - scale.value
      position.value = {
        x: position.value.x - (mouseX - rect.width / 2) * scaleChange / newScale,
        y: position.value.y - (mouseY - rect.height / 2) * scaleChange / newScale,
      }
      scale.value = newScale
    }
  }

  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
    resetZoom()
  }

  const downloadPNG = async (options?: { backgroundColor?: string }) => {
    try {
      if (!contentRef.value) { return }
      const svgElement = contentRef.value.querySelector('svg')
      if (!svgElement) { return }

      const png = await toPng(svgElement as unknown as HTMLElement, {
        quality: 1,
        pixelRatio: 2,
        backgroundColor: options?.backgroundColor,
      })

      const link = document.createElement('a')
      link.href = png
      link.download = `Juchats-${downloadName}-${Date.now()}.png`
      link.click()
    }
    catch (error) {
      console.error('Export failed:', error)
    }
  }

  const downloadSVG = () => {
    if (!contentRef.value) { return }
    const svgElement = contentRef.value.querySelector('svg')
    if (!svgElement) { return }

    const svgData = new XMLSerializer().serializeToString(svgElement)
    const blob = new Blob([svgData], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `Juchats-${downloadName}-${Date.now()}.svg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  return {
    scale,
    position,
    isDragging,
    isFullscreen,
    zoomIn,
    zoomOut,
    resetZoom,
    startDrag,
    onDrag,
    stopDrag,
    handleWheel,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    toggleFullscreen,
    downloadPNG,
    downloadSVG,
  }
}
