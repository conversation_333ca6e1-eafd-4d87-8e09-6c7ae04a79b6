window._iconfont_svg_string_4499433='<svg><symbol id="icon-Mixtral" viewBox="0 0 1152 1024"><path d="M32 0h256v256h192v192h192V256h192V0h256v1024h-256v-320h-154.176v169.984h-256V704H288v320h-256V0z"  ></path></symbol><symbol id="icon-OpenAI" viewBox="0 0 1024 1024"><path d="M474.331247 101.411936c32.872589 0 63.32015 10.400672 88.217094 28.184203-65.47573 34.058157-125.67029 77.008081-189.529335 113.868492l50.65612 87.732089c79.756445-46.075514 154.608946-103.898936 241.155467-137.040972a152.02225 152.02225 0 0 1 205.480624 156.495078c-62.242361-39.662664-129.496444-70.325784-193.355489-107.240084l-50.65612 87.785978c79.756445 46.075514 167.272976 81.912024 239.269334 140.328232a152.02225 152.02225 0 0 1-32.872589 256.190634c3.233369-73.720822-3.772264-147.333865-3.772264-221.054687h-101.312241c0 92.151027 12.66403 185.810961-1.886132 277.369203a151.968361 151.968361 0 0 1-238.299323 99.695556c65.47573-34.112047 125.67029-77.008081 189.529335-113.868491l-50.65612-87.73209c-79.756445 46.075514-154.608946 103.898936-241.155467 137.040973a152.02225 152.02225 0 0 1-205.480624-156.495078c62.29625 39.608775 129.496444 70.325784 193.355489 107.240084l50.65612-87.785979c-79.756445-46.021624-167.272976-81.912024-239.269334-140.328231a152.02225 152.02225 0 0 1 32.872589-256.190634c-3.233369 73.720822 3.772264 147.333865 3.772264 221.054687h101.312241c0-92.151027-12.66403-185.810961 1.886132-277.369204A152.02225 152.02225 0 0 1 474.331247 101.411936z m192.116031-13.095146a253.334491 253.334491 0 0 0-433.00205 86.6543 253.334491 253.334491 0 0 0-141.459911 418.344108 253.334491 253.334491 0 0 0 291.54214 331.689809 253.334491 253.334491 0 0 0 433.002049-86.654299 253.334491 253.334491 0 0 0 141.459911-418.344109 253.334491 253.334491 0 0 0-291.542139-331.689809z"  ></path></symbol><symbol id="icon-Gemini" viewBox="0 0 1239 1024"><path d="M840.165053 532.264421a307.307789 307.307789 0 0 1 55.511579-24.845474c19.078737-6.359579 38.696421-12.180211 58.745263-16.976842 19.617684-4.742737 39.720421-8.946526 59.823158-13.20421a435.038316 435.038316 0 0 0 58.745263-15.36 214.231579 214.231579 0 0 0 137.593263-229.645474c-13.743158-98.950737-94.693053-175.157895-195.260632-184.158316-45.487158-4.257684-90.004211 6.844632-130.694736 25.384421a414.181053 414.181053 0 0 0-110.106948 75.129263c-65.589895 60.901053-113.717895 139.210105-143.36 222.261895a640.970105 640.970105 0 0 0-9.539368 393.216c12.665263 41.822316 31.205053 84.668632 55.511579 120.670316a356.459789 356.459789 0 0 1 27.001263-201.620211c28.564211-61.978947 75.183158-115.873684 136.030316-150.851368z"  ></path><path d="M979.321263 762.987789a153.492211 153.492211 0 0 0-42.846316-35.462736c-16.922947-10.024421-32.821895-15.845053-51.361684-22.743579-16.922947-6.359579-35.408842-13.743158-48.128-27.486316-13.258105-13.797053-17.461895-32.875789-17.461895-51.361684-76.746105 78.848-109.029053 191.056842-92.106105 298.469052 11.102316 15.898947 24.360421 32.336842 39.181474 45.541053 58.745263 50.229895 146.593684 47.104 202.105263-6.898526a146.593684 146.593684 0 0 0 10.617263-200.057264zM209.327158 163.408842c68.823579 0 134.952421 37.564632 183.134316 79.925895 51.307789 45.002105 90.489263 102.130526 116.951579 164.055579a496.747789 496.747789 0 0 1 38.103579 197.362526c-0.538947 65.643789-13.743158 136.569263-42.361264 196.338526a328.650105 328.650105 0 0 0-84.614736-208.464842 164.001684 164.001684 0 0 0-30.181053-27.001263 152.683789 152.683789 0 0 0-36.001684-18.000842c-24.899368-9.000421-53.463579-12.719158-79.386948-15.898947-26.947368-3.179789-53.948632-4.742737-80.410947-8.461474-27.540211-4.203789-52.439579-10.024421-76.746105-23.821474a179.361684 179.361684 0 0 1-84.668632-202.64421 182.703158 182.703158 0 0 1 176.23579-133.389474z"  ></path><path d="M379.742316 626.472421a247.808 247.808 0 0 0-20.641684-20.102737c9.539368 15.898947 10.078316 33.845895 6.359579 50.822737-3.718737 16.922947-12.719158 32.768-23.282527 46.565053a205.123368 205.123368 0 0 1-38.642526 35.462737c-12.665263 8.946526-30.72 20.587789-42.307369 31.744a99.543579 99.543579 0 0 0-16.437894 122.77221 102.076632 102.076632 0 0 0 116.412631 45.487158c24.899368-6.844632 45.541053-23.282526 62.517895-41.822316 16.869053-18.485895 30.666105-40.744421 41.229474-62.949052a334.848 334.848 0 0 0-85.153684-207.97979z"  ></path></symbol><symbol id="icon-Voice" viewBox="0 0 1194 1024"><path d="M233.244444 256h102.4v512H233.244444v-512zM28.444444 409.6h102.4v204.8H28.444444V409.6zM438.044444 0h102.4v921.6H438.044444V0z m204.8 102.4h102.4V1024h-102.4V102.4z m204.8 153.6h102.4v512h-102.4v-512z m204.8 153.6h102.4v204.8h-102.4V409.6z"  ></path></symbol><symbol id="icon-Claude" viewBox="0 0 1297 1024"><path d="M920.439467 0h-188.2112l342.562133 970.069333H1262.933333L920.439467 0zM376.6272 0L34.133333 970.069333h192.034134l69.085866-203.707733H654.677333l69.905067 203.776h191.965867L573.303467 0h-196.608z m-18.432 586.1376l116.736-341.1968L592.554667 586.1376H358.1952z"  ></path></symbol><symbol id="icon-Labs" viewBox="0 0 1077 1024"><path d="M538.947368 1024a512 512 0 1 1 0-1024 512 512 0 0 1 0 1024z m0-102.4a409.6 409.6 0 1 0 0-819.2 409.6 409.6 0 0 0 0 819.2z m102.4-512h102.4v204.8h-102.4V409.6z m-307.2 0h102.4v204.8H334.147368V409.6z"  ></path></symbol></svg>',function(n){var t=(t=document.getElementsByTagName("script"))[t.length-1],e=t.getAttribute("data-injectcss"),t=t.getAttribute("data-disable-injectsvg");if(!t){var o,a,i,c,d,h=function(t,e){e.parentNode.insertBefore(t,e)};if(e&&!n.__iconfont__svg__cssinject__){n.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}o=function(){var t,e=document.createElement("div");e.innerHTML=n._iconfont_svg_string_4499433,(e=e.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",e=e,(t=document.body).firstChild?h(e,t.firstChild):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(o,0):(a=function(){document.removeEventListener("DOMContentLoaded",a,!1),o()},document.addEventListener("DOMContentLoaded",a,!1)):document.attachEvent&&(i=o,c=n.document,d=!1,s(),c.onreadystatechange=function(){"complete"==c.readyState&&(c.onreadystatechange=null,l())})}function l(){d||(d=!0,i())}function s(){try{c.documentElement.doScroll("left")}catch(t){return void setTimeout(s,50)}l()}}(window);