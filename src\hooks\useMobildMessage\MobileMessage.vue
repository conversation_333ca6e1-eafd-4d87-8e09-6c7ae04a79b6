<template>
  <Teleport :to="teleport">
    <transition :name="transition">
      <div
        v-if="visible"
        class="fixed left-50% min-h-60px w-240px flex-c rounded-11px bg-[#000c] p-16px text-white op-100 transition-cusbezier-300 -translate-x-50%"
        :style="{ zIndex, top: `${top}px` }"
      >
        {{ message }}
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  message: string
  duration: number
  transition: string
  teleport: string
  zIndex: number
  top: number
}

const props = defineProps<Props>()

const visible = ref(false)

onMounted(() => {
  visible.value = true
  setTimeout(() => {
    visible.value = false
  }, props.duration)
})
</script>

<style scoped>
.message.leave-active {
  opacity: 0;
  top: 0;
}

.message-enter-active,
.message-leave-active {
  transition: opacity 0.4s var(--ani-bezier);
}

.message-enter-from,
.message-leave-to {
  opacity: 0;
}
</style>
