<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="false"
    background-color="#F9FAFC"
    :full-screenable="false"
  >
    <template #content>
      <section class="px-50px py-47px">
        <div class="mb-16px text-24px text-#18181a font-450 font-Lexend">蛋白质结构</div>

        <div v-if="pdbId" class="space-y-16px">
          <!-- PDB ID 信息卡片 -->
          <div class="border border-#E5E7EB rounded-8px bg-white p-20px">
            <div class="mb-16px flex items-center gap-12px">
              <span class="text-32px">🧬</span>
              <div>
                <div class="text-18px text-#18181a font-600">{{ pdbId.toUpperCase() }}</div>
                <div class="text-14px text-#6B7280">蛋白质数据库标识符</div>
              </div>
            </div>
            <div class="flex items-center gap-8px">
              <div class="inline-block rounded-6px bg-#ECFDF5 px-12px py-8px text-20px text-#059669 font-600 font-mono">
                {{ pdbId.toUpperCase() }}
              </div>
              <div v-if="loadingStatus" class="text-14px text-#6B7280">
                {{ loadingStatus }}
              </div>
            </div>
          </div>

          <!-- 3D 蛋白质结构渲染区域 -->
          <div class="overflow-hidden border border-#E5E7EB rounded-8px bg-white">
            <div class="border-b border-#E5E7EB p-20px">
              <div class="text-16px text-#18181a font-600">3D 结构</div>
            </div>

            <!-- Loading 状态 -->
            <div v-if="isLoading" class="h-400px flex items-center justify-center bg-#F9FAFB">
              <div class="text-center">
                <div class="i-line-md-loading-twotone-loop mb-12px text-32px text-#059669"></div>
                <div class="mb-8px text-16px text-#6B7280">正在加载蛋白质结构...</div>
                <div class="text-14px text-#9CA3AF">{{ loadingStatus }}</div>
              </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="errorMessage" class="h-400px flex items-center justify-center bg-#FEF2F2">
              <div class="text-center">
                <div class="i-ri-error-warning-line mb-12px text-32px text-#EF4444"></div>
                <div class="mb-8px text-16px text-#DC2626">加载失败</div>
                <div class="mb-16px text-14px text-#9CA3AF">{{ errorMessage }}</div>
                <button
                  class="rounded-6px bg-#059669 px-16px py-8px text-14px text-white transition-colors hover:bg-#047857"
                  @click="loadProteinStructure"
                >
                  重新加载
                </button>
              </div>
            </div>

            <!-- Mol* 渲染容器 -->
            <div
              v-else
              ref="molstarContainer"
              class="h-400px w-full"
              style="position: relative;"
            ></div>
          </div>
        </div>

        <div v-else class="py-40px text-center">
          <div class="mb-16px text-48px">🧬</div>
          <div class="text-16px text-#6B7280">暂无蛋白质信息</div>
        </div>
      </section>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { computed, nextTick, onUnmounted, ref, watch } from 'vue'
import ResizablePanel from '@/components/ResizablePanel.vue'

interface Props {
  visible?: boolean
  pdbId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  pdbId: '',
})

const emits = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: value => emits('update:visible', value),
})

const fullVisible = ref(false)
const tab = ref('structure')

const panelTabs = computed(() => [
  {
    key: 'structure',
    label: '蛋白质结构',
  },
])

// 状态管理
const isLoading = ref(false)
const errorMessage = ref('')
const loadingStatus = ref('')
const molstarContainer = ref<HTMLElement>()
let molstarViewer: any = null

// 声明全局 molstar 类型
declare global {
  interface Window {
    molstar: any
  }
}

// 监听 pdbId 变化，自动加载蛋白质结构
watch(() => props.pdbId, (newPdbId) => {
  if (newPdbId && props.visible) {
    loadProteinStructure()
  }
}, { immediate: true })

// 监听面板可见性变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.pdbId) {
    nextTick(() => {
      loadProteinStructure()
    })
  }
  else if (!newVisible) {
    // 清理 Mol* 实例
    cleanupMolstar()
  }
})

// 加载蛋白质结构
async function loadProteinStructure() {
  console.log('molstarContainer.value: ', molstarContainer.value)
  if (!props.pdbId || !molstarContainer.value) { return }

  isLoading.value = true
  errorMessage.value = ''
  loadingStatus.value = '正在初始化 3D 渲染器...'

  try {
    // 清理之前的实例
    cleanupMolstar()

    // 检查 Mol* 是否已加载
    if (!window.molstar) {
      throw new Error('Mol* 库未加载，请刷新页面重试')
    }

    loadingStatus.value = '正在创建 3D 查看器...'

    // 创建 Mol* 查看器
    molstarViewer = await window.molstar.Viewer.create(molstarContainer.value, {
      layoutIsExpanded: false,
      layoutShowControls: true,
      layoutShowRemoteState: false,
      layoutShowSequence: true,
      layoutShowLog: false,
      layoutShowLeftPanel: true,
      viewportShowExpand: true,
      viewportShowSelectionMode: false,
      viewportShowAnimation: false,
    })

    loadingStatus.value = '正在加载蛋白质结构...'

    // 直接从 RCSB PDB 加载结构
    const pdbUrl = `https://files.rcsb.org/download/${props.pdbId.toUpperCase()}.cif`
    await molstarViewer.loadStructureFromUrl(pdbUrl, 'mmcif')

    loadingStatus.value = '渲染完成'
    isLoading.value = false
  }
  catch (error) {
    console.error('加载蛋白质结构失败:', error)
    errorMessage.value = error instanceof Error ? error.message : '加载失败，请检查 PDB ID 是否正确'
    isLoading.value = false
    loadingStatus.value = ''
  }
}

// 清理 Mol* 实例
function cleanupMolstar() {
  if (molstarViewer) {
    try {
      molstarViewer.dispose()
    }
    catch (error) {
      console.warn('清理 Mol* 实例时出错:', error)
    }
    molstarViewer = null
  }
}

// 组件卸载时清理
onUnmounted(() => {
  cleanupMolstar()
})
</script>

<style scoped>
/* 如果需要特定样式可以在这里添加 */
</style>
