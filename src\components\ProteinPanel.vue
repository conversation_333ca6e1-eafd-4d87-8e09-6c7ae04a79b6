<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="false"
    background-color="#F9FAFC"
    :full-screenable="false"
  >
    <template #content>
      <section class="px-50px py-47px">
        <div class="mb-16px text-24px text-#18181a font-450 font-Lexend">蛋白质信息</div>
        
        <div v-if="pdbId" class="space-y-16px">
          <div class="p-20px bg-white rounded-8px border border-#E5E7EB">
            <div class="flex items-center gap-12px mb-16px">
              <span class="text-32px">🧬</span>
              <div>
                <div class="text-18px font-600 text-#18181a">PDB ID</div>
                <div class="text-14px text-#6B7280">蛋白质数据库标识符</div>
              </div>
            </div>
            <div class="text-20px font-mono font-600 text-#059669 bg-#ECFDF5 px-12px py-8px rounded-6px inline-block">
              {{ pdbId }}
            </div>
          </div>
          
          <!-- 这里可以添加更多蛋白质相关信息 -->
          <div class="p-20px bg-white rounded-8px border border-#E5E7EB">
            <div class="text-16px font-600 text-#18181a mb-12px">详细信息</div>
            <div class="text-14px text-#6B7280">
              点击的蛋白质 PDB ID: {{ pdbId }}
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-40px">
          <div class="text-48px mb-16px">🧬</div>
          <div class="text-16px text-#6B7280">暂无蛋白质信息</div>
        </div>
      </section>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import ResizablePanel from '@/components/ResizablePanel.vue'

interface Props {
  visible?: boolean
  pdbId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  pdbId: '',
})

const emits = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emits('update:visible', value),
})

const fullVisible = ref(false)
const tab = ref('info')

const panelTabs = computed(() => [
  {
    key: 'info',
    label: '蛋白质信息',
  },
])
</script>

<style scoped>
/* 如果需要特定样式可以在这里添加 */
</style>
