<template>
  <Transition name="fade" mode="out-in">
    <div
      v-if="bindVisible"
      class="fixed z-[1] h-full w-full flex items-center justify-center bg-[#ffffff80] py-[80px] backdrop-blur-md"
    >
      <i
        class="i-ri-close-line absolute right-[45px] top-[35px] z-[1] mt-[80px] h-[40px] w-[40px] flex cursor-pointer items-center justify-center border border-[#CBD5E1] rounded-full text-[24px] text-[#A8AFBF]"
        @click="bindVisible = false"
      ></i>
      <div>
        <div
          class="pb-[40px] text-center text-[64px] font-['Alegreya'] font-[700]"
        >
          {{ $t("bindLogin.authLogin") }}
        </div>
        <div class="flex">
          <div
            class="mx-[10px] h-[70px] w-[320px] flex items-center justify-between rounded-[20px] bg-[#ffffff] px-[20px] shadow-[0_0_20px_0_rgba(0,0,0,0.12)]"
          >
            <i class="i-ri-google-fill text-[32px]"></i>
            <div class="flex-1 px-[10px] text-[18px] font-['Roboto_Slab']">
              {{ $t("bindLogin.googleAuthorized") }}
            </div>
            <div v-if="isClient" @click="clientBindGoogle">
              <div
                class="cursor-pointer rounded-[10px] bg-destructive-300 px-[20px] py-[10px] text-[14px] text-[#FFFFFF] shadow-[0_0_50px_0_#F651344D]"
              >
                {{ $t("bindLogin.bind") }}
              </div>
            </div>
            <GoogleLogin v-else :callback="bingGoogle">
              <div
                class="cursor-pointer rounded-[10px] bg-destructive-300 px-[20px] py-[10px] text-[14px] text-[#FFFFFF] shadow-[0_0_50px_0_#F651344D]"
              >
                {{ $t("bindLogin.bind") }}
              </div>
            </GoogleLogin>
          </div>
          <div
            class="mx-[10px] h-[70px] w-[320px] flex items-center justify-between rounded-[20px] bg-[#ffffff] px-[20px] shadow-[0_0_20px_0_rgba(0,0,0,0.12)]"
          >
            <i class="i-ri-github-fill text-[32px]"></i>
            <div class="flex-1 px-[10px] text-[18px] font-['Roboto_Slab']">
              {{ $t("bindLogin.githubAuthorized") }}
            </div>
            <div
              class="cursor-pointer rounded-[10px] bg-[#000000] px-[20px] py-[10px] text-[14px] text-[#FFFFFF] shadow-[0_0_50px_0_#00000024]"
              @click="bingGithub"
            >
              {{ $t("bindLogin.bind") }}
            </div>
          </div>
        </div>
        <div
          class="mt-[40px] h-[100px] w-[700px] flex items-center justify-center border border-[#A8AFBF] rounded-[5px] border-dashed text-[14px] font-[500]"
        >
          {{ $t("bindLogin.googleTip") }}
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import services from '@/common/axios'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useAppStore } from '@/stores/app'
import { encode } from 'base-64'
import { ElNotification } from 'element-plus'

const t = useGlobalI18n()
const { isClient } = storeToRefs(useAppStore())
const bindVisible = ref(false)
const getBindLogin = async () => {
  const data: any = await services.post(
    '/user/bind/status',
    {},
    {
      headers: {
        noninductive: true,
      },
    },
  )
  if (data === 'false') {
    bindVisible.value = true
  }
}
onMounted(() => {
  getBindLogin()
})
const clientBindGoogle = async () => {
  const ORIGIN = location.origin
  const GOOGLE_CLIENT_ID = import.meta.env.VITE_APP_GOOGLE_CLIENT
  const REDIRECT_URI = `${ORIGIN}/gw/chatweb/user/google/oauth/callback`
  const SCOPE = 'openid email profile'
  const localeLang = localStorage.getItem('localeLang') || ''
  const STATE = encode(
    JSON.stringify({
      'from': 'BIND',
      'invitationCode': '',
      'Jtoken': localStorage.token,
      'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
    }),
  )
  const RESPONSE_TYPE = 'code'
  location.href = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=${RESPONSE_TYPE}&scope=${SCOPE}&state=${STATE}`
}

const bingGithub = () => {
  location.href = `https://github.com/login/oauth/authorize?client_id=${
    import.meta.env.VITE_APP_GITHUB_CLIENT
  }&redirect_uri=${`${location.origin}/oauth?from=BIND`}`
}
const bingGoogle = async ({ code }: any) => {
  await services.post('/user/bind/thirdParty/login', {
    googleCode: code,
    googleRedirectUri: location.origin,
  })
  bindVisible.value = false
  ElNotification.success({ title: t('oauthView.bindSucceed') })
}
</script>
