<template>
  <div class="size-full py-45px pr-42px lt-lg:(flex flex-col p-0)">
    <div v-if="ltLg" class="flex-c flex-1">
      <LoginLogoTextLine text="Juchats" />
    </div>
    <div class="login-swiper grid-c size-full rounded-20px bg-[var(--login-card-bg)] shadow-[0_0_30px_0_#00000005] lt-lg:(block h-550px min-h-550px rounded-b-0)">
      <el-carousel
        ref="carouselRef"
        :key="randomKey"
        trigger="click"
        height="705px"
        :interval="10000"
        :indicator-position="indicatorPosition"
        class="max-w-480px w-full"
        arrow="never"
        :autoplay="!ltLg"
        @change="handleChange"
      >
        <el-carousel-item v-if="ltLg" name="steps">
          <LoginSteps
            ref="loginStepsRef"
            key="swiperLoginWay"
            @current-step="handleCurrentStep"
          />
        </el-carousel-item>
        <el-carousel-item name="uploadFiles">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(1)"
            title="UPLOAD FILES"
            tip="Juchats Supports uploading 72 file formats"
          >
            <template #user>
              <div class="mt-6px text-13px leading-loose">
                Please read this book "The Underlying Logic" carefully
              </div>
              <div>
                <AttachmentCard
                  class="mt-8px h-60px w-240px leading-tight"
                  :only-display="true"
                  file-name="Underlying logic.pdf"
                >
                </AttachmentCard>
              </div>
            </template>

            <template #bot>
              <p v-if="!ltLg" class="mb-8px">
                Based on the search results, I have summarized the main content and core viewpoints of the book
                "<span class="font-bold">The Underlying Logic</span>"
                for you:
              </p>
              <p class="text-20px font-bold">Book Overview</p>
              <p>"The Underlying Logic" is a self-improvement book written by Lü Bai, aimed at helping ordinary people achieve a breakthrough in life. The author shares his personal journey from earning 3,000 yuan per month to making millions annually, providing readers with a series of practical thinking methods and strategies.</p>
            </template>
          </LoginSwiperItem>
        </el-carousel-item>
        <el-carousel-item name="summeryUrls">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(2)"
            title="SUMMERY URLS"
            tip="Summarize various types of linked content."
          >
            <template #user>
              <div>
                <p>https://arxiv.org/abs/2210.03629</p>
                <p>Summarize the above link.</p>
              </div>
            </template>

            <template #bot>
              <p v-if="!ltLg">
                Based on the abstract of the arXiv paper,I will provide you with a structured overview of key points:
              </p>
              <p v-if="!ltLg" class="mt-8px text-20px font-bold">ReAct:Synergizing Reasoning and Acting in Language Models</p>
              <p class="mt-6px text-18px font-bold">Paper Overview</p>
              <p>
                This paper introduces ReAct,a novel approach that combines reasoning and acting in language models to enhance their performance on various tasks.
              </p>
            </template>
          </LoginSwiperItem>
        </el-carousel-item>
        <el-carousel-item name="dataChart">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(3)"
            title="DATA CHART"
            tip="Create various types of data charts."
          >
            <template #user>
              <div>
                <p>Please Search Apple's 10-k 2024 financial report</p>
              </div>
            </template>

            <template #bot>
              <p v-if="!ltLg">
                Drawing charts based on the searched data.
              </p>
              <p class="mt-8px text-20px font-bold">Financial Trend Charts</p>
              <div>
                <el-image :style="{ width: !ltLg ? '420px' : '100%' }" fit="fill" :src="isDark ? dataChartDarkImg : dataChartImg" alt="">
                </el-image>
              </div>
            </template>
          </LoginSwiperItem>
        </el-carousel-item>

        <el-carousel-item name="mermaid">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(4)"
            title="MERMAID"
            tip="Organize important relationship maps."
          >
            <template #user>
              <div>
                <p>
                  Help me organize the timeline and character
                  relationships of "One Hundred Years of Solitude."
                </p>
              </div>
            </template>

            <template #bot>
              <p v-if="!ltLg">Based on the content I found,I am organizing a relationship map.</p>
              <div>
                <el-image :style="{ width: !ltLg ? '373px' : '80%' }" fit="fill" :src="mermaidImg" alt="">
                </el-image>
              </div>
            </template>
          </LoginSwiperItem>
        </el-carousel-item>

        <el-carousel-item name="twitter">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(5)"
            title="TWITTER"
            tip="Summarize and search for X bloggers you like."
          >
            <template #user>
              <div>
                <p>
                  Show the top ten tweets from @Cydiar404 and provide sarcastic commentary!
                </p>
              </div>
            </template>

            <template #bot>
              <p v-if="!ltLg">
                @Cydiar404's tweets showcase sarcastic reviews.
              </p>
              <div>
                <el-image style="width: 330px;" fit="fill" :src="summaryTwitterImg" alt="">
                </el-image>
              </div>
              <template v-if="!ltLg">
                <p>
                  Tweet Content:Has it been 11 years already?
                  #MyXAnniversary
                </p>
                <p>
                  Snarky Comment:An X user for 11 years,has Cydiar
                  made this his second home?But after 11 years,his snarky
                  skills must be top-notch now.We have reason to suspect,
                  is he making a living off of snarky comments?
                </p>
              </template>
            </template>
          </LoginSwiperItem>
        </el-carousel-item>

        <el-carousel-item class="!flex-c" name="client">
          <LoginSwiperItem
            v-if="currentIndex === getAdjustedIndex(6)"
            title="CLIENT"
            tip="A more comprehensive client experience."
            :dialogue="false"
          >
            <div class="animate__animated animate__fadeInUp mt-60px animate-delay-800 lt-lg:(mt-40px)">
              <div class="relative">
                <el-image style="width: 362px;" fit="fill" :src="isDark ? clientDarkImg : clientImg" alt="">
                  <template #placeholder></template>
                </el-image>
                <div class="led-font absolute-x-center bottom-41px flex justify-between text-16px space-x-20px">
                  <span class="cursor-pointer text-#727272 hover:(text-#000 dark:text-#fff)">WINDOWS</span>
                  <span class="cursor-pointer text-#727272 hover:(text-#000 dark:text-#fff)">LINUX</span>
                  <span class="cursor-pointer text-#727272 hover:(text-#000 dark:text-#fff)">MAC</span>
                  <span class="text-#727272">PWA</span>
                  <span class="text-#727272">H5</span>
                </div>
              </div>
            </div>
          </LoginSwiperItem>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
import clientDarkImg from '@/assets/images/login-client-dark.webp'
import clientImg from '@/assets/images/login-client.webp'
import dataChartDarkImg from '@/assets/images/login-data-chart-dark.webp'
import dataChartImg from '@/assets/images/login-data-chart.webp'
import mermaidImg from '@/assets/images/login-mermaid.webp'
import summaryTwitterImg from '@/assets/images/login-twitter.webp'
import { mixpanel } from '@/config/mixpanel'
import { useDevice } from '@/hooks/useDevice'
import { useThemeChange } from '@/hooks/useThemeChange'
import { useAppStore } from '@/stores/app'
import { nanoid } from 'nanoid'

const loginStepsRef = ref()
const { breakpoints } = useAppStore()
const ltLg = breakpoints.smaller('lg') // 小于LG尺寸

const { isDark } = useThemeChange()
const currentIndex = ref(0)
const carouselRef = ref()
const randomKey = ref(nanoid())
const { isRealPc } = useDevice()

const indicatorPosition = computed(() => {
  if (!isRealPc.value && (
    loginStepsRef.value?.emailFocus
    || loginStepsRef.value?.verificationCodeInputRef?.isFocused
  )) {
    return 'none'
  }
  return 'outside'
})

function handleChange() {
  if (ltLg.value) {
    mixpanel.loginSwiperChange()
  }
  currentIndex.value = carouselRef.value.activeIndex
}
const getAdjustedIndex = computed(() => {
  return (index: number) => {
    // 移端(ltLg为true)时索引从1开始，PC端从0开始
    return ltLg.value ? index : index - 1
  }
})

watch(ltLg, (newVal) => {
  randomKey.value = nanoid()
  currentIndex.value = 0

  if (newVal) { carouselRef.value?.setActiveItem('steps') }
  else { carouselRef.value?.setActiveItem('uploadFiles') }
})

// 当前登录步骤（用于判断是否触发滑动事件）
const loginStep = ref('')
function handleCurrentStep(newVal: string) {
  loginStep.value = newVal
}

// 将触摸事件监听逻辑封装为函数
const setupTouchEvents = () => {
  const box = document.querySelector('.el-carousel') as HTMLElement
  if (!box) { return }

  let startPoint = 0
  let stopPoint = 0

  const resetPoint = () => {
    startPoint = 0
    stopPoint = 0
  }

  // 手指按下
  const handleTouchStart = (e: Event) => {
    startPoint = (e as TouchEvent).changedTouches[0].pageX
  }

  // 手指滑动
  const handleTouchMove = (e: Event) => {
    stopPoint = (e as TouchEvent).changedTouches[0].pageX
  }

  // 手指抬起
  const handleTouchEnd = () => {
    const windowWidth = window.innerWidth

    // 输入email的登录页面，触发滑动切换的距离最大不超过230px
    // 输入验证码的界面就别划拉了，只能通过点击指示器切换
    const triggerPixelDistance = loginStep.value === 'home' ? Math.min(windowWidth * 0.35, 230) : Infinity
    if (stopPoint === 0 || startPoint - stopPoint === 0) {
      resetPoint()
      return
    }

    if (startPoint - stopPoint > triggerPixelDistance) {
      resetPoint()
      carouselRef.value?.next()
      return
    }

    if (startPoint - stopPoint < -triggerPixelDistance) {
      resetPoint()
      carouselRef.value?.prev()
    }
  }

  // 添加事件监听
  box.addEventListener('touchstart', handleTouchStart)
  box.addEventListener('touchmove', handleTouchMove)
  box.addEventListener('touchend', handleTouchEnd)

  // 返回清理函数
  return () => {
    box.removeEventListener('touchstart', handleTouchStart)
    box.removeEventListener('touchmove', handleTouchMove)
    box.removeEventListener('touchend', handleTouchEnd)
  }
}

watch(ltLg, (newVal) => {
  if (newVal) {
    nextTick(() => {
      setupTouchEvents()
    })
  }
})

onMounted(() => {
  setupTouchEvents()
})
</script>

<style lang="scss" scoped>
.login-swiper {
  position: relative;

  :deep(.el-carousel) {
    position: static;
  }

  :deep(.el-carousel__indicators) {
    bottom: 60px;

    @apply absolute-x-center lt-lg:(fixed bottom-18px);
  }

  :deep(.el-carousel__indicator--horizontal .el-carousel__button) {
    width: 10px;
    height: 10px;
    background: #CDCDCD;
    border: 1px solid #CDCDCD;
    border-radius: 50%;
    opacity: 0.3;
  }

  :deep(.el-carousel__indicator--horizontal.is-active .el-carousel__button) {
    opacity: 1;
  }

  // :deep(.el-carousel__indicator--horizontal:last-child .el-carousel__button) {
  //   background: none;
  //   border: none;
  //   filter: contrast(80%);
  //   width: 13px;
  //   height: 13px;
  //   opacity: 1;
  //   background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCAzNiAzNiI+PHBhdGggZmlsbD0iI0REMkU0NCIgZD0iTTM1Ljg4NSAxMS44MzNjMC01LjQ1LTQuNDE4LTkuODY4LTkuODY3LTkuODY4Yy0zLjMwOCAwLTYuMjI3IDEuNjMzLTguMDE4IDQuMTI5Yy0xLjc5MS0yLjQ5Ni00LjcxLTQuMTI5LTguMDE3LTQuMTI5Yy01LjQ1IDAtOS44NjggNC40MTctOS44NjggOS44NjhjMCAuNzcyLjA5OCAxLjUyLjI2NiAyLjI0MUMxLjc1MSAyMi41ODcgMTEuMjE2IDMxLjU2OCAxOCAzNC4wMzRjNi43ODMtMi40NjYgMTYuMjQ5LTExLjQ0NyAxNy42MTctMTkuOTU5Yy4xNy0uNzIxLjI2OC0xLjQ2OS4yNjgtMi4yNDIiLz48cGF0aCBmaWxsPSIjRkRDQjU4IiBkPSJtMzQuMzQ3IDIzLjg5NGwtMy44MjQtMS40MTZsLTEuNDE2LTMuODI0YTEgMSAwIDAgMC0xLjg3Ni0uMDAxbC0xLjQxNiAzLjgyNGwtMy44MjQgMS40MTZhMS4wMDEgMS4wMDEgMCAwIDAgMCAxLjg3NmwzLjgyNCAxLjQxNmwxLjQxNiAzLjgyNGExIDEgMCAwIDAgMS44NzYgMGwxLjQxNi0zLjgyNGwzLjgyNC0xLjQxNmExLjAwMSAxLjAwMSAwIDAgMCAwLTEuODc1bS0yMy0xNi4wMDFsLTIuMzY1LS44NzVsLS44NzUtMi4zNjVhMSAxIDAgMCAwLTEuODc2IDBsLS44NzYgMi4zNjVsLTIuMzY0Ljg3NWExIDEgMCAwIDAgMCAxLjg3NmwyLjM2NS44NzVsLjg3NSAyLjM2NWExIDEgMCAwIDAgMS44NzYgMGwuODc1LTIuMzY1bDIuMzY1LS44NzVhMSAxIDAgMCAwIDAtMS44NzYiLz48L3N2Zz4=");
  //   background-size: contain;
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   border-radius: 0%;
  //   transform: translateY(1px);
  // }

  :deep(.el-carousel__indicator--horizontal.is-active:last-child .el-carousel__button) {
    filter: contrast(120%);
  }
}
</style>
