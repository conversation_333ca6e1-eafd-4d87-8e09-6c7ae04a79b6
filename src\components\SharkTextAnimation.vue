<template>
  <div class="shark-txt inline-block select-none text-14px text-[var(--shark-text-color)]">
    {{ toolName }}
    <span class="loading-dots inline-block text-left" />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  toolName: string
}>()
</script>

<style lang="scss" scoped>
@keyframes shark-txt {
  to {
    background-position: 200%;
  }
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

.shark-txt {
  -webkit-text-fill-color: transparent;
  background:
    linear-gradient(-45deg, rgba(255, 255, 255, 0%) 10%,
    var(--shark-shadow-color),
    rgba(255, 255, 255, 0%) 90%) -100% / 50% no-repeat currentcolor;
  -webkit-background-clip: text;
  animation: shark-txt 2s infinite;
}

.loading-dots::after {
  content: '';
  animation: dots 6s steps(4, end) infinite;
}
</style>
