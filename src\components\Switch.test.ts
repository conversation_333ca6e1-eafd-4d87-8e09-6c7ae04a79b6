import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import userEvent from '@testing-library/user-event'
import { waitFor } from '@testing-library/vue'
import Switch from './Switch.vue'

/**
 * @vitest-environment jsdom
 */
describe('should toggle switch correctly', async () => {
  const wrapper = mount(Switch, {
    props: {
      modelValue: false,
    },
    attrs: {
      'onUpdate:modelValue': (value: boolean) => wrapper.setProps({ modelValue: value }),
    },
  })
  const radioContainer = wrapper.find('.juchats-switch')

  it('default status', (a) => {
    expect(radioContainer.classes()).not.toContain('checked')
  })

  it('Open Switch', async () => {
    await userEvent.click(radioContainer.element)
    await waitFor(() => {
      expect(wrapper.find('.juchats-switch').classes()).toContain('checked')
    })
  })

  it('Close Switch', async () => {
    await userEvent.click(radioContainer.element)
    await waitFor(() => {
      expect(radioContainer.classes()).not.toContain('checked')
    })
  })

  it('Disabled Switch', async () => {
    await wrapper.setProps({ disabled: true })
    await userEvent.click(radioContainer.element)
    await waitFor(() => {
      const classes = wrapper.find('.juchats-switch').classes()
      expect(classes).not.toContain('checked')
      expect(classes).has.toContain('disabled')
    })
  })
})
