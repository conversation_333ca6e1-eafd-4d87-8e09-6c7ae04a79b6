<script lang="ts" setup>
import services from '@/common/axios'
import { mixpanel } from '@/config/mixpanel'
import { useAppStore } from '@/stores/app'

const { userInfo, getUserInfoV2 } = useUserInfo()
const { isPc } = storeToRefs(useAppStore())
const code = ref('')
const giftData: any = ref({})
const giftVisible = ref(false)
const getGift = async () => {
  mixpanel.eggExchangeStart()
  giftData.value = await services.post(`${import.meta.env.VITE_APP_PAY_URL}/order/exchange`, {
    code: code.value,
    headers: {
      noninductive: true,
    },
  })
  await getUserInfoV2()
  giftVisible.value = true
}
</script>

<template>
  <div class="relative h-full flex-y-c flex-col text-primary-400">
    <template v-if="isPc">
      <div v-if="!giftVisible" class="mb-33 flex items-center">
        <PageTitle>
          {{ $t("eggRedemption.eggCodeExchange") }}
        </PageTitle>
      </div>
      <div
        v-if="!giftVisible"
        class="absolute left-[50%] top-[50%] flex-y-c justify-center rounded-5 bg-[var(--egg-redemption-input-bg)] p-2.5 shadow-[0_0_5rem_0_#00000012] -translate-x-1/2 -translate-y-1/2"
      >
        <AvatarFrame :src="userInfo.thumbnailUrl" name="w-12 h-12 rounded-3">
        </AvatarFrame>
        <div
          class="max-w-40 of-hidden text-ellipsis whitespace-nowrap pl-2.5 pr-1 text-align-justify text-6 font-extrabold tracking-normal font-RobotoSlab"
        >
          {{ userInfo.name }}
        </div>
        <div class="mx-10px h-15px w-1px bg-primary-100"></div>
        <input
          v-model="code"
          class="h-[2.2rem] w-[15rem] p-1 text-center text-3.5 text-4.5 font-bold outline-none placeholder-(text-left text-3.5 text-#979797 font-normal)"
          :placeholder="$t('eggRedemption.eggCodePlaceholder')"
          type="text"
        />
        <div
          class="h-[2.2rem] w-[4.5rem] flex cursor-pointer items-center justify-center rounded-[0.63rem] bg-destructive-300 text-[0.8rem] text-[#FFFFFF] shadow-[0_0_3rem_0_rgba(246,81,52,0.46)]"
          @click="getGift"
        >
          {{ $t("eggRedemption.exchange") }}
        </div>
      </div>
      <BillDetail v-else :gift-data="giftData" :code="code"></BillDetail>
    </template>
    <template v-else>
      <div
        v-if="!giftVisible"
        class="absolute left-[50%] top-[30%] flex-y-c flex-col -translate-x-1/2 -translate-y-[30%]"
      >
        <AvatarFrame :src="userInfo.thumbnailUrl" name="w-25 h-25 rounded-3">
        </AvatarFrame>
        <div
          class="max-w-40 of-hidden text-ellipsis whitespace-nowrap pl-2.5 pr-1 text-align-justify text-6 font-extrabold tracking-normal font-RobotoSlab"
        >
          {{ userInfo.name }}
        </div>
        <div
          class="mt-10 flex-y-c justify-center rounded-5 bg-[var(--egg-redemption-input-bg)] p-2.5 shadow-[0_0_5rem_0_#00000012] lt-md:(rounded-2)"
        >
          <input
            v-model="code"
            class="h-8.8 w-60 p-1 text-center text-3.5 text-4.5 font-bold lt-md:(text-left) placeholder-(text-left text-3.5 text-#979797 font-normal)"
            :placeholder="$t('eggRedemption.eggCodePlaceholder')"
            type="text"
            @keydown.enter="getGift"
          />
          <div
            class="h-8.8 w-18 flex cursor-pointer items-center justify-center rounded-[0.63rem] bg-destructive-300 text-[0.8rem] text-[#FFFFFF] shadow-[0_0_3rem_0_rgba(246,81,52,0.46)] lt-md:(rounded-0.75)"
            @click="getGift"
          >
            {{ $t("eggRedemption.exchange") }}
          </div>
        </div>
      </div>
      <BillDetail v-else :gift-data="giftData" :code="code"></BillDetail>
    </template>
  </div>
</template>
