// 大部分是动画，从main.scss移除，单独在PackageBox组件中引入了这个文件


.package-box {
  @keyframes fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }


  .animate__fadeIn {
    animation-name: fade-in;
  }

  @keyframes jack-in-the-box {
    0% {
      opacity: 0;
      transform: scale(0.1) rotate(30deg);
      transform-origin: center bottom;
    }

    50% {
      transform: rotate(-10deg);
    }

    70% {
      transform: rotate(3deg);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }


  .animate__jackInTheBox {
    animation-name: jack-in-the-box;
  }

  @keyframes bounce-in-down {
    0% {
      opacity: 0;
      transform: translate3d(0, -3000px, 0) scaleY(3);
    }
  
    60% {
      opacity: 1;
      transform: translate3d(0, 25px, 0) scaleY(0.9);
    }
  
    75% {
      transform: translate3d(0, -10px, 0) scaleY(0.95);
    }
  
    90% {
      transform: translate3d(0, 5px, 0) scaleY(0.985);
    }
  
    100% {
      transform: translate3d(0, 0, 0);
    }
  }

  .animate__bounceInDown {
    animation-name: bounce-in-down;
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  .animate__animated {
    animation-duration: 1s;
    animation-duration: var(--animate-duration);
    animation-fill-mode: both;
  }

  .moimoi__group {
    &:hover {
      .moimoi__content {
        @extend .animate__animated;
        @extend .animate__fadeIn;

        animation-duration: 2.5s;
        animation-delay: 0.25s;
      }

      .moimoi__1 {
        @extend .animate__animated;
        @extend .animate__jackInTheBox;
      }

      .moimoi__2 {
        @extend .animate__animated;
        @extend .animate__bounceInDown;

        animation-delay: 0.3s;
      }

      .moimoi__3 {
        @extend .animate__animated;
        @extend .animate__bounceInDown;

        animation-delay: 1s;
      }

      .moimoi__4 {
        @extend .animate__animated;
        @extend .animate__bounceInDown;

        animation-delay: 0.75s;
      }
    }
  }
}
