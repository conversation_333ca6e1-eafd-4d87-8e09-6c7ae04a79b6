<script lang="tsx" setup>
import type { OrderRecord } from '@/model/pay'
import type { ColumnProps } from '../Table.vue'
import services from '@/common/axios'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import moment from 'moment'

defineOptions({
  name: 'Subscription',
})
enum ORDER_STATUS {
  /** 支付中 */
  PAYING = 0,
  PAY_SUCCESS = 1,
  PAY_CANCEL = 2,
  PAY_CLOSE = 3,
}
interface SubscriptionOrderProps {
  orderNo: string
  status: ORDER_STATUS
  price: number
  successTime: string
}
/** state area */
const t = useGlobalI18n()
const invitePage = ref(0)
const inviteLoading = ref(false)
const orderList: Ref<SubscriptionOrderProps[]> = ref([])
const orderStatus: Record<ORDER_STATUS, any> = {
  [ORDER_STATUS.PAYING]: {
    text: 'Paying',
    color: 'text-[#ABABAB]',
    bg: 'bg-[#EEEEEE]',
    opText: t('personalCenterSubscription.pay').value,
    opColor: 'text-[#FF8A00]',
    action: async (row: OrderRecord) => {
      const { url }: any = await services.post(
        `${import.meta.env.VITE_APP_PAY_URL}/order/stripe/pay`,
        {
          packageId: row.packageId,
        },
      )
      window.open(url)
      console.log('支付中')
    },
  },
  [ORDER_STATUS.PAY_SUCCESS]: {
    text: 'Paid',
    color: 'text-[#357532]',
    bg: 'bg-[#C8F4C6]',
    // opText: t('personalCenterSubscription.view').value,
    // opColor: 'text-[#49B644]',
  },
  [ORDER_STATUS.PAY_CANCEL]: {
    text: 'Fail',
    color: 'text-[#9A201B]',
    bg: 'bg-[#FFBCBA]',
  },
  [ORDER_STATUS.PAY_CLOSE]: {
    text: 'Fail',
    color: 'text-[#9A201B]',
    bg: 'bg-[#FFBCBA]',
  },
}
const tableColumns: ColumnProps[] = [
  { key: 'orderNo', label: t('packageBox.orderNumber').value },
  { key: 'status', label: t('personalCenterSubscription.status').value, component: ({ status }: { status: ORDER_STATUS }) => <span class={['rounded-md py-1 px-2 text-2.7 font-bold', orderStatus[status].color, orderStatus[status].bg]}>{orderStatus[status].text}</span> },
  { key: 'price', label: t('personalCenterSubscription.payAmount').value, component: ({ price }) => <span>{`${price} USD`}</span> },
  { key: 'successTime', label: t('personalCenterSubscription.payTime').value, component: ({ successTime }: { successTime: string }) => <span>{successTime ? moment(successTime).format('YYYY.MM.DD') : '暂未付款'}</span> },
  { key: 'options', label: t('personalCenterSubscription.relatedOperations').value, component: (row: SubscriptionOrderProps) => <span onClick={() => tableHandler(row)} class={['cursor-pointer', orderStatus[row.status].opColor && orderStatus[row.status].opColor]}>{orderStatus[row.status]?.opText || ''}</span> },
]
/** help methods area */
const tableHandler = (row: SubscriptionOrderProps) => {
  if (!orderStatus[ORDER_STATUS.PAYING].action) { return }
  orderStatus[ORDER_STATUS.PAYING].action(row)
}
/** event hander area */
const getOrderList = async () => {
  invitePage.value++
  inviteLoading.value = true
  const { records }: any = await services.post(`${import.meta.env.VITE_APP_PAY_URL}/order/list`, {
    limit: 10,
    page: invitePage.value,
  })
  if (!records.length) {
    return
  }
  orderList.value.push(...records)
  inviteLoading.value = false
}
onMounted(async () => {
  await getOrderList()
})
</script>

<template>
  <div class="space-y-4">
    <div
      class="flex items-center justify-between border-b border-dialog-100 pb-4"
    >
      <h2 class="text-lg font-semibold">
        {{ t("personalCenterLayout.mySubscription") }}
      </h2>
    </div>
    <div
      v-if="orderList.length"
      v-infinite-scroll="getOrderList"
      :infinite-scroll-disabled="inviteLoading"
    >
      <Table :columns="tableColumns" :data="orderList"></Table>
    </div>
  </div>
</template>
