<!-- eslint-disable vue/custom-event-name-casing -->
<template>
  <Teleport to="body">
    <div
      :class="[isShow ? 'translate-x-0 opacity-100' : '-translate-x-100% opacity-0']"
      class="fixed left-70px top-50% z-19999 min-w-300px w-300px overflow-hidden border-1px rounded-19px border-solid bg-[var(--white)] shadow-[0px_2px_30px_#0000000f] transition-cusbezier-300 -translate-y-1/2 dark:(border-#0000 bg-#272727)"
      @mouseenter="$emit('card-mouseenter')"
      @mouseleave="$emit('card-mouseleave')"
    >
      <div class="h-590px px-40px py-20px">
        <slot />
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
defineEmits(['card-mouseenter', 'card-mouseleave'])
const isShow = defineModel<boolean>('isShow')
</script>

<style lang="scss" scoped>
</style>
