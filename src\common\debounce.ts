export default {
  mounted(el: any, binding: any) {
    const eventType = ['click']
    if (binding.modifiers) {
      Object.keys(binding.modifiers).forEach((eType) => {
        eventType.push(eType)
      })
    }
    let timer: any
    function handler() {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        binding.value()
      }, 400)
    }

    eventType.forEach((eType) => {
      el.addEventListener(eType, handler)
    })
  },
}
