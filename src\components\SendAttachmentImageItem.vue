<script setup lang="ts">
import { IMAGE_SIZE } from '@/enum'
import type { UploadUserFile } from 'element-plus'
import type { PropType } from 'vue'

defineProps({
  file: {
    type: Object as PropType<UploadUserFile>,
  },
  size: {
    type: Number as PropType<IMAGE_SIZE>,
    default: IMAGE_SIZE.BIG,
  },
})
function getImageSrc(file: File) {
  return URL.createObjectURL(file)
}
</script>

<template>
  <div class="img-item relative overflow-hidden border-rd-10px lh-0 shadow-[0_5px_20px_#00000033] transition-all">
    <el-image
      class="h-full w-full border-rd-10px"
      :src="getImageSrc(file?.raw as File)"
      fit="cover"
    />
    <Transition
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <SendAttachmentProgress
        v-if="file?.status !== 'success'"
        :percentage="file?.percentage"
        :stroke-width="size === IMAGE_SIZE.BIG ? 6 : 3"
        :size="size === IMAGE_SIZE.BIG ? 60 : 30"
      ></SendAttachmentProgress>
    </Transition>
  </div>
</template>

<style scoped>
.img-item {
  --el-fill-color-light: rgba(255, 255, 255, 30%);
}
</style>
