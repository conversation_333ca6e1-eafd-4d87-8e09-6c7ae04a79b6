<script lang="tsx" setup>
import { copy, generateQRCode } from '@/common'
import services from '@/common/axios'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useUserStore } from '@/stores/user'
import moment from 'moment'
import QRCodeStyling from 'qr-code-styling'

defineOptions({
  name: 'Invitation',
})
const t = useGlobalI18n()
/** contants area */
const tableColumns = [
  {
    key: 'fromUserName',
    label: t('personalCenterInvitation.userAccount'),
    component: ({
      fromUserName,
      fromUserPhone,
    }: {
      fromUserName: string
      fromUserPhone: string
    }) => <span>{fromUserName || fromUserPhone}</span>,
  },
  { key: 'packageName', label: t('headerBar.buySubscriptionPlan') },
  { key: 'amount', label: t('headerBar.receiveReward') },
  {
    key: 'purchaseTime',
    label: t('headerBar.timeOfPurchase'),
    component: ({ time }: { time: string }) => (
      <span>{moment(time).format('YYYY.MM.DD')}</span>
    ),
  },
]
/** state area */
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const userForm = ref({
  ...userInfo.value,
})
const inviteLink = ref('')
const hasCopied = ref(false)
const invitePage = ref(1)
const commissions: any = ref([])
const commissionsTotal = ref(0)

const codeRef = ref()
const codeStyling = ref()

/** help methods area */
const getInfo = async () => {
  try {
    await userStore.getUserInfo()
    inviteLink.value = `${location.origin}/login/${userInfo.value.inviteCode}`
    codeStyling.value = new QRCodeStyling({
      data: inviteLink.value,
      width: 160,
      height: 160,
      qrOptions: {
        typeNumber: 0,
        mode: 'Byte',
        errorCorrectionLevel: 'L',
      },
      dotsOptions: {
        type: 'dots',
        color: '#000000',
      },
      cornersSquareOptions: {
        type: 'dot',
        color: '#000000',
      },
      cornersDotOptions: {
        type: 'dot',
        color: '#000000',
      },
      backgroundOptions: {
        color: '#FFFFFF',
      },
      imageOptions: {
        margin: 0,
        crossOrigin: 'anonymous',
      },
    })
    codeStyling.value.append(codeRef.value)
    userForm.value = { ...userInfo.value }
  }
  catch (error) {
    console.log('file: Invitation.vue:57 ~ getInfo ~ error:', error)
  }
}
/** event handler area */
const handleCopy = () => {
  copy(inviteLink.value)
  hasCopied.value = true
  setTimeout(() => {
    hasCopied.value = false
  }, 2000)
}
const getCommissions = async (value: number) => {
  invitePage.value = value || 1
  const { records, total }: any = await services.post('/user/commissions', {
    limit: 5,
    page: invitePage.value,
  })
  commissionsTotal.value = total
  commissions.value = [...records]
}
/** lifecycle */
onMounted(async () => {
  await getInfo()
  await getCommissions()
})
</script>

<template>
  <div class="space-y-4">
    <div
      class="flex items-center justify-between border-b border-dialog-100 pb-4 !lt-md:hidden"
    >
      <h2 class="text-lg font-semibold">{{ $t("userInfo.myInvite") }}</h2>
    </div>
    <div>
      <div
        class="flex items-center justify-between lt-md:flex-col [&>*]:(border-1 border-border rounded-lg border-dashed p-4)"
      >
        <div
          class="h-35 w-full flex items-center rounded-r-none bg-[var(--app-bg)] p-2 px-0 lt-md:rounded-b-none"
        >
          <div class="flex flex-1 flex-col items-center px-2 text-center">
            <div class="text-8 text-[#384B67] font-bold dark:text-[#F1F1F1]">
              {{ userInfo?.inviteCount || 0 }}
            </div>
            <div class="text-3.5 text-primary-100">
              {{ $t("headerBar.numberInvited") }}
            </div>
          </div>
          <div class="h-[88px] h-full border-l-1 border-r-1 border-border !border-[#ECF0F5]"></div>
          <div class="flex flex-1 flex-col items-center px-2 text-center">
            <div
              class="w-full overflow-hidden text-ellipsis whitespace-nowrap text-8 text-destructive-100 font-bold"
            >
              {{ userInfo?.commission || 0 }}
            </div>
            <div class="text-3.5 text-primary-100">
              {{ $t("personalCenterInvitation.rewards") }}（USD）
            </div>
          </div>
        </div>
        <div
          class="h-35 w-full flex items-center rounded-l-none border-l-none bg-[#FBFCFF] p-2 lt-md:(rounded-t-none border-l-dashed border-t-none) dark:bg-[#171717]"
        >
          <div
            ref="codeRef"
            class="mr-4 h-80px w-80px [&_canvas]:(h-80px w-80px)"
          />
          <div class="break-all font-[var(--font-family)]">
            <div
              class="flex-y-c text-4.5 text-[var(--black)] font-bold transition-cusbezier-400"
            >
              {{ userForm.inviteCode }}
              <i
                v-if="!hasCopied"
                class="i-ju-copy ml-2 cursor-pointer text-sm text-primary-800"
                @click="handleCopy"
              />
              <i
                v-else
                class="i-ri-check-line ml-2 cursor-pointer text-sm text-primary-800"
              />
            </div>
            <div class="break-all text-sm">{{ inviteLink }}</div>
          </div>
        </div>
      </div>
      <div
        v-if="commissions.length"
        class="!lt-md:hidden"
      >
        <Table :columns="tableColumns" :data="commissions"></Table>
        <el-pagination v-model:current-page="invitePage" :page-size="5" class="pt-4 text-destructive-100" layout="prev, pager, next" :total="commissionsTotal" hide-on-single-page @current-change="getCommissions" />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.el-pager li.is-active, .el-pager li:hover {
  @apply text-destructive-100;
}
</style>
