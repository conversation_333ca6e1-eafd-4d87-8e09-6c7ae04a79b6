import type { NotificationItem } from '@/model/common'
import type { MessageItemModel } from '@/model/messages'
import { ElNotification } from 'element-plus'
import lottie from 'lottie-web'
import moment from 'moment'
import QRCode from 'qrcode'
// import store from '@/common/store'
import { v4 as uuidv4 } from 'uuid'
// @unocss-include
import useClipboard from 'vue-clipboard3'
import lottieJSON2 from '@/assets/json/success2.json'
import lottieJSON3 from '@/assets/json/success3.json'
import lottieJSON1 from '@/assets/json/success.json'
import { LABS_MODES, MODE_TYPE } from '@/enum'
import i18n from '@/i18n'
import { NotificationType } from '@/model/common'
import { useAppStore } from '@/stores/app'
import { useHeaderBarStore } from '@/stores/headerBar'
import { useNotificationStore } from '@/stores/notification'
import { useUserStore } from '@/stores/user'
import { notify } from './tools'

const userStore = useUserStore()
const notificationStore = useNotificationStore()

export function closeNotification(item: NotificationItem) {
  notificationStore.closeNotification(item)
}
export function showNotification(content: string, type = NotificationType.SUCCESS) {
  notificationStore.showNotification({ content, type })
}

// lottie动画
export function lottieAnimation(element: any, lottieJSON?: any) {
  const randomIndex = Math.floor(Math.random() * 3)
  const animationData
    = lottieJSON || (!randomIndex ? lottieJSON1 : randomIndex === 1 ? lottieJSON2 : lottieJSON3)
  return lottie.loadAnimation({
    container: element, // the dom element
    renderer: 'svg',
    loop: true,
    autoplay: true,
    animationData, // the animation data
  })
}
// 复制
export function copy(string: string, options: { copySuccess?: string, copyFail?: string } = {}) {
  const { toClipboard } = useClipboard()
  toClipboard(string)
    .then(() => {
      notify.success({ title: options.copySuccess || i18n.global.t('tipMessage.copySuccess') })
    })
    .catch(() => {
      notify.success({ title: options.copyFail || i18n.global.t('tipMessage.copyFail') })
    })
}
// 生成二维码
export async function generateQRCode(text: any) {
  return await QRCode.toDataURL(text, {
    margin: 0,
  })
}
// 获取plus / free 标识符
export function getFlag() {
  // 确定颜色类名
  let colorClass
  if (userStore.userInfoV2.discountPackage?.includes('Plus')) {
    colorClass = 'text-[#ff8f00]'
  }
  else if (userStore.userInfoV2.discountPackage?.includes('Premium')) {
    colorClass = 'text-[#ff0000]'
  }
  else {
    colorClass = 'text-[var(--p4)]'
  }

  // 确定显示文本
  let displayText
  if (
    moment().isAfter(moment(userStore.userInfoV2.plusExpireTime))
    || userStore.userInfoV2.discountPackage === null
  ) {
    displayText = i18n.global.t('shareFooter.guest')
  }
  else {
    displayText = userStore.userInfoV2.discountPackage?.replace('年', '') || ''
  }

  // 返回HTML字符串
  return `<div class="font-Damion relative select-none ${colorClass}">
  <div class="user-shark-text mt-2px leading-normal text-18px">${displayText}</div>`
}
// 时间补位
export function filterTime(params: any) {
  return params < 10 ? (params < 0 ? '00' : `0${params}`) : params
}
// 生成uuid
export function generateUUID() {
  return uuidv4()
}

export function modeTransPlaceholder(id: number, data: any) {
  const { isPc, showAttachment } = storeToRefs(useAppStore())
  // * 模式类型 Type
  // * 1:GPT3.5（GPT SwiftMix Perview）
  // * 2:GPT4.0（GPT Turbo Preview）
  // * 3:E3
  // * 0:混合模型 （GPT Dynamic Preview ）
  // * 5:groq（Mixtral Dynamics preview）
  // * 6:claude（Claude Opus (free tier 5)）
  // * 7.claude 图片分析 （Claude Dynamics Preview）
  // * 8.Llama3 70B  review

  // * 模式事件 Event
  // 1.GPT混合模型聊天 2.语音聊天 3.GROQ (Mixtral Dynamics preview） 4.Claude Opus (free tier 5) 5.Claude Dynamics Preview
  const array = data.flatMap((element: any) => element.modes)
  const element = array.find((element: any) => element.id === id)
  if (element && [3].includes(element.type)) {
    return i18n.global.t('tipMessage.imgPlaceholder')
  }
  else if (showAttachment.value && isPc.value) {
    return i18n.global.t('tipMessage.filePlaceholder')
  }
  else {
    return i18n.global.t('tipMessage.questionPlaceholder')
  }
}

export function modeTransType(id: number, tts: number, data: any) {
  // * 模式类型 Type
  // * 1:GPT3.5（GPT SwiftMix Perview）
  // * 2:GPT4.0（GPT Turbo Preview）
  // * 3:E3
  // * 0:混合模型 （GPT Dynamic Preview ）
  // * 5:groq（Mixtral Dynamics preview）
  // * 6:claude（Claude Opus (free tier 5)）
  // * 7.claude 图片分析 （Claude Dynamics Preview）
  // * 8.Llama3 70B Preview

  // * 模式事件 Event
  // 1.GPT混合模型聊天 2.语音聊天 3.GROQ (Mixtral Dynamics preview） 4.Claude Opus (free tier 5) 5.Claude Dynamics Preview
  const array = data.flatMap((element: any) => element.modes)
  const element = array.find((element: any) => element.id === id)
  // !id为语音，其余模型根据模型名称区分
  if (!id) {
    // 语音 event 为 2 时
    // Type值 1.文字 -> 语音 (prompt必传) 2: 语音 -> 语音 (fileId必传) 3:语音 ->文字 (fileId必传)
    return tts
  }
  else {
    return element.type
  }
}

export function modeTransSelected(id: number, data: any) {
  // Selected 0:OPENAI 1:CLAUDE 2: MIXTRAL 3:VOICE 4:LABS
  // * 模式类型 Type
  // * 1:GPT3.5（GPT SwiftMix Perview）
  // * 2:GPT4.0（GPT Turbo Preview）
  // * 3:E3
  // * 0:混合模型 （GPT Dynamic Preview ）
  // * 5:groq（Mixtral Dynamics preview）
  // * 6:claude（Claude Opus (free tier 5)）
  // * 7.claude 图片分析 （Claude Dynamics Preview）
  // * 8.Llama3 70B Preview
  // * 9.Gemini
  // * 10.Deepseek

  // * 模式事件 Event
  // 1.GPT混合模型聊天 2.语音聊天 3.GROQ (Mixtral Dynamics preview） 4.Claude Opus (free tier 5) 5.Claude Dynamics Preview
  const array = data.flatMap((element: any) => element.modes)
  const element = array.find((element: any) => element.id === id)
  if (!element) { return }
  if (!id) {
    // VOICE
    return 3
  }
  else if (
    // LABS
    LABS_MODES.includes(element.type)
  ) {
    return 4
  }
  else if (
    // MIXTRAL
    [MODE_TYPE.GROQ].includes(element.type)
  ) {
    return 2
  }
  else if (
    // Claude
    [MODE_TYPE.CLAUDE3, MODE_TYPE.CLAUDE_DYNAMICS_PREVIEW].includes(element.type)
  ) {
    return 1
  }
  else {
    // OPENAI
    return 0
  }
}

// 已弃用，未检索到调用
// export function modeTransEvent(id: number, data: any) {
//   // Selected 0:OPENAI 1:CLAUDE 2: MIXTRAL 3:VOICE 4:LABS
//   // * 模式类型 Type
//   // * 1:GPT3.5（GPT SwiftMix Perview）
//   // * 2:GPT4.0（GPT Turbo Preview）
//   // * 3:E3
//   // * 0:混合模型 （GPT Dynamic Preview ）
//   // * 5:groq（Mixtral Dynamics preview）
//   // * 6:claude（Claude Opus (free tier 5)）
//   // * 7.claude 图片分析 （Claude Dynamics Preview）
//   // * 8.Llama3 70B Preview
//   // * 9.Gemini
//   // * 10.Deepseek
//   // * 11.4O

//   // * 模式事件 Event
//   // 1.GPT混合模型聊天 2.语音聊天 3.GROQ (Mixtral Dynamics preview） 4.Claude Opus (free tier 5) 5.Claude Dynamics Preview
//   const array = data.flatMap((element: any) => element.modes)
//   const element = array.find((element: any) => element.id === id)
//   if (!id) {
//     return 2
//   }
//   else if ([5, 8, 9, 14, 17].includes(element.type)) {
//     return 3
//   }
//   else if ([6, 12, 13, 26].includes(element.type)) {
//     return 4
//   }
//   else if ([7].includes(element.type)) {
//     return 5
//   }
//   else if ([15].includes(element.type)) {
//     return 6
//   }
//   else if ([18].includes(element.type)) {
//     return 7
//   }
//   else {
//     return 1
//   }
// }

export function formatMobile(string: string | undefined) {
  const phoneRegex = /^1\d{10}$/
  if (string && phoneRegex.test(string)) {
    return string.replace(/(\d{3})\d{4}(\d{4})/, '$1····$2')
  }
  else {
    return string
  }
}

export function getActualLength(str: string) {
  let length = 0
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    if (charCode >= 0 && charCode <= 128) {
      length += 1
    }
    else {
      // 中文
      length += 2
    }
  }
  return length
}

export const formatUnitPlus = ({ amount }: any) => {
  let string: any = ''
  switch (amount) {
    case 1:
      string = i18n.global.t('tipMessage.oneDay')
      break
    case 5:
      string = i18n.global.t('tipMessage.fiveDays')
      break
    case 7:
      string = i18n.global.t('tipMessage.sevenDays')
      break
    case 15:
      string = i18n.global.t('tipMessage.fifteenDays')
      break
    case 31:
      string = i18n.global.t('tipMessage.oneMonth')
      break
    case 365:
      string = i18n.global.t('tipMessage.oneYear')
      break
    default:
      string = `${amount}${i18n.global.t('tipMessage.day')}`
      break
  }
  return string
}

export function getSecondLevelDomain(fullUrl: any) {
  if (!fullUrl) {
    return ''
  }
  let string: string = ''
  try {
    const url = new URL(fullUrl)
    string = url.hostname
  }
  catch {
    string = 'fullUrl'
  }
  return string
}

export function upgrade() {
  const appStore = useAppStore()
  appStore.triggerUpgradePackage()
}

export function expire() {
  if (
    moment().isAfter(moment(userStore.userInfo.plusExpireTime))
    && userStore.userInfo.expirePackageName
  ) {
    ElNotification.error({
      dangerouslyUseHTMLString: true,
      message:
        `<span class="notification__flag">${userStore.userInfo.expirePackageName}</span>`
        + `<span>${i18n.global.t('tipMessage.expiry')}</span>`
        + `<span class="notification__expire">${i18n.global.t('tipMessage.renewal')}</span>`,
      onClick: upgrade,
    })
  }
}

export function generateUnionID({ item, messageId }: { item: MessageItemModel, messageId: string | number }) {
  return `${messageId}_${item.id}_${(item.receiving ? 'a' : 'q')}`
}

/**
 * 为元素添加 macOS 风格的滚动条行为
 * @param element 目标元素
 * @param timeout 滚动停止后滚动条消失的延迟时间（毫秒）
 */
export function enableMacScrollbar(element: globalThis.Ref<any, any>, timeout = 1500) {
  let timer: number | null = null

  const showScrollbar = () => {
    if (element.value) {
      element.value.classList.add('show-scrollbar')
    }
  }

  const hideScrollbar = () => {
    if (element.value) {
      element.value.classList.remove('show-scrollbar')
    }
  }

  let lastScrollTop = 0
  const showBottomTrigger = ref(false)
  const scrollDistance = 100

  const handleScroll = (e: Event) => {
    const target = e.target as HTMLElement
    const currentScrollTop = target.scrollTop
    const remainingScrollSpace = target.scrollHeight - target.clientHeight - currentScrollTop

    // 1. 如果剩余滚动空间小于阈值，强制设置为 false
    if (remainingScrollSpace < scrollDistance) {
      showBottomTrigger.value = false
    }
    // 2. 如果是向上滚动，且剩余空间足够，延迟设置为 true
    else if (currentScrollTop < lastScrollTop) {
      // 只有单次向上滚动超过10px才设置为true
      if (lastScrollTop - currentScrollTop > 10) {
        showBottomTrigger.value = true
      }
    }
    // 3. 如果是向下滚动,立即设置为 false
    else if (currentScrollTop > lastScrollTop) {
      showBottomTrigger.value = false
    }

    lastScrollTop = currentScrollTop
    showScrollbar()

    if (timer) {
      window.clearTimeout(timer)
    }

    timer = window.setTimeout(() => {
      hideScrollbar()
      timer = null
    }, timeout)
  }

  onMounted(() => {
    if (!element.value) { return }
    element.value.addEventListener('scroll', handleScroll, { passive: true })
    element.value.addEventListener('mouseenter', showScrollbar)
    element.value.addEventListener('mouseleave', () => {
      if (!element.value.scrollHeight || element.value.scrollHeight === element.value.clientHeight) {
        hideScrollbar()
      }
    })
  })

  onUnmounted(() => {
    if (!element.value) { return }
    element.value.removeEventListener('scroll', handleScroll)
    element.value.removeEventListener('mouseenter', showScrollbar)
    element.value.removeEventListener('mouseleave', hideScrollbar)
    if (timer) {
      window.clearTimeout(timer)
    }
  })

  // 返回清理函数
  return {
    showBottomTrigger,
  }
}
