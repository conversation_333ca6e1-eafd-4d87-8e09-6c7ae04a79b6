<script lang="ts" setup>
import avatar_upload from '@/assets/images/default_upload_avatar.svg'
import services from '@/common/axios'
import { emailRegular } from '@/common/constant'
import { notify } from '@/common/tools'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'

defineOptions({
  name: 'Info',
})

enum UNTIE_TYPE {
  GOOGLE = 2,
  GITHUB = 3,
  EMAIL = 4,
}
// 修改邮箱时的状态
enum EDIT_EMAIL_STATUS {
  NOT_START = 1,
  WAIT_CODE = 2,
  SUCCESS = 3,
}

/** state area */
const t = useGlobalI18n()
const userStore = useUserStore()
const { isClient } = storeToRefs(useAppStore())
const { userInfoV2: userInfo } = storeToRefs(userStore)
const emailCode = ref('')
const imageId = ref('')
const avatarPreviewUrl = ref('')

const currentLink = ref<{ name: string, unite_tpye: UNTIE_TYPE }>({
  name: '',
  unite_tpye: UNTIE_TYPE.GOOGLE,
})
const sendSeconds = ref(0)
const startEditEmail = ref(false)
const editEmailStatus = ref(EDIT_EMAIL_STATUS.NOT_START)
const verifyInterval = ref()
const avatarUrl = ref('')

/** web methods area */
const upload = async (data: any) => {
  const image = new Image()
  image.src = URL.createObjectURL(data.file)
  const canvas = document.createElement('canvas')
  const ctx: any = canvas.getContext('2d')

  image.onload = async function () {
    const minSide = Math.min(image.width, image.height)
    canvas.width = minSide
    canvas.height = minSide

    const offsetX = (image.width - minSide) / 2
    const offsetY = (image.height - minSide) / 2

    ctx.drawImage(
      image,
      offsetX,
      offsetY,
      minSide,
      minSide,
      0,
      0,
      minSide,
      minSide,
    )

    await canvas.toBlob(async (blob: any) => {
      const formdata = new FormData()
      formdata.append(
        'file',
        new File([blob], 'edited_image.png', { type: 'image/png' }),
      )
      const { thumbnailUrl, uuid }: any = await services.post(
        data.action,
        formdata,
      )
      avatarPreviewUrl.value = import.meta.env.VITE_APP_IMAGE_URL + thumbnailUrl
      imageId.value = uuid
    }, 'image/png')
  }
}
const handleCurrentLink = (name: string, type: UNTIE_TYPE) => {
  currentLink.value = {
    name,
    unite_tpye: type,
  }
  startEditEmail.value = false
  editEmailStatus.value = EDIT_EMAIL_STATUS.NOT_START
}

const sendCode = async () => {
  if (sendSeconds.value) {
    return
  }
  if (!userInfo.value.email) {
    return notify.error({ title: t('headerBar.enterYourEmail').value })
  }

  if (!emailRegular.test(userInfo.value.email)) {
    return notify.error({ title: t('headerBar.enterCorrectEmailAccount').value })
  }
  await services.post('/user/email/sendCaptcha', {
    email: userInfo.value.email,
    type: 2,
  })
  editEmailStatus.value = EDIT_EMAIL_STATUS.WAIT_CODE
  if (verifyInterval.value) {
    clearInterval(verifyInterval.value)
  }
  sendSeconds.value = 120
  verifyInterval.value = setInterval(() => {
    if (sendSeconds.value <= 0) {
      clearInterval(verifyInterval.value)
      return
    }
    sendSeconds.value--
  }, 1000)
}
const onEmailCodeInput = async (val: any) => {
  if (val.length !== 6) {
    return
  }
  await services.post('/user/bind/email', {
    code: val,
    email: userInfo.value.email,
  })
  notify.success({ title: t('headerBar.successfulBinding').value })
  editEmailStatus.value = EDIT_EMAIL_STATUS.NOT_START
  startEditEmail.value = false
  emailCode.value = ''
  getInfo()
}

const bindGithub = () => {
  location.href = `https://github.com/login/oauth/authorize?client_id=${import.meta.env.VITE_APP_GITHUB_CLIENT}&redirect_uri=${
    `${location.origin}/oauth?from=HEADER`
  }`
}
const beforeUpload = (file: any) => {
  if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
    notify.error({ title: t('headerBar.avatarFormat').value })
    return false
  }
  else if (file.size / 1024 / 1024 > 5) {
    notify.error({ title: t('headerBar.avatarLimit').value })
    return false
  }
  return true
}
const saveInfo = async () => {
  await services.post('/user/updInfo', {
    name: userInfo.value.name,
    uuid: imageId.value,
  })
  notify.success({ title: t('headerBar.infoSavedSuccessfully').value })
  await getInfo()
  avatarPreviewUrl.value = ''
}
const bindGoogle = async ({ code }: any) => {
  await services.post('/user/bind/thirdParty/login', {
    googleCode: code,
    googleRedirectUri: location.origin,
  })
  await getInfo()
  notify.success({ title: t('headerBar.successfulBinding').value })
}
const handleBind = () => {
  const actionMap: Partial<Record<UNTIE_TYPE, any>> = {
    [UNTIE_TYPE.GITHUB]: bindGithub,
    [UNTIE_TYPE.EMAIL]: () => (startEditEmail.value = true),
  }
  actionMap[currentLink.value.unite_tpye]()
}
const unbind = async () => {
  await services.post('/user/untie', {
    untieType: currentLink.value.unite_tpye,
  })
  notify.success({ title: t('headerBar.successfulUnbind').value })
  await getInfo()
}

/** help methods area */
const getInfo = async () => {
  await userStore.getUserInfoV2()
  const { googleEmail, email } = userInfo.value
  currentLink.value.name = googleEmail || email || ''
  currentLink.value.unite_tpye = UNTIE_TYPE.GOOGLE
  emailCode.value = ''
}
/** lifecycle */
onBeforeMount(async () => {
  await getInfo()
})
</script>

<template>
  <div class="text-primary-300 lt-md:mt-10 space-y-4">
    <div
      class="flex items-center justify-between border-b border-dialog-100 pb-4 lt-md:hidden"
    >
      <h2 class="text-lg font-semibold">{{ $t("userInfo.myProfile") }}</h2>
    </div>
    <div
      v-if="userInfo"
      class="flex flex-1 justify-left text-center lt-md:justify-center"
    >
      <div class="flex flex-col items-start">
        <el-upload
          class="mb-2 lt-md:self-center"
          action="/file/upload"
          :show-file-list="false"
          :http-request="upload"
          :before-upload="beforeUpload"
          :on-error="() => notify.error({ title: t('headerBar.avatarError').value })"
        >
          <AvatarFrame
            upload
            :border="false"
            :src="avatarPreviewUrl || userInfo.thumbnailUrl"
            name="w-30 h-30"
          ></AvatarFrame>
        </el-upload>
        <InputBox
          v-model="userInfo.name"
          class="[&>input]:font-Lexend h-10 w-70 [&>input]:(font-800) ![&>input]:pl-0"
          :label="$t('personalCenterInfo.nickName')"
        >
        </InputBox>
        <div class="flex items-center justify-center gap-4 py-4.7 pl-3">
          <div
            :data-id="userInfo?.googleEmail || false"
            class="cursor-pointer text-5 text-[var(--black)] dark:text-#E7E9ED data-[id=false]:text-primary-900 hover:text-primary-400"
            @click="
              handleCurrentLink(userInfo?.googleEmail || '', UNTIE_TYPE.GOOGLE)
            "
          >
            <i class="i-ju-google"></i>
          </div>
          <div
            :data-id="userInfo?.githubUserId || false"
            class="cursor-pointer text-5 text-[var(--black)] dark:text-#E7E9ED data-[id=false]:text-primary-900 hover:text-primary-400"
            @click="
              handleCurrentLink(
                userInfo?.githubUserName || '',
                UNTIE_TYPE.GITHUB,
              )
            "
          >
            <i class="i-ju-github"></i>
          </div>
          <div
            :data-id="userInfo.email || false"
            class="cursor-pointer text-5 text-[var(--black)] dark:text-#E7E9ED data-[id=false]:text-primary-900 hover:text-primary-400"
            @click="handleCurrentLink(userInfo.email || '', UNTIE_TYPE.EMAIL)"
          >
            <i class="i-ju-email"></i>
          </div>
        </div>
        <div v-if="!startEditEmail" class="w-full flex justify-between pl-3">
          <p
            :data-current-link-name="currentLink?.name || ''"
            class="font-Lexend w-48 overflow-hidden text-ellipsis whitespace-nowrap text-left text-3.5 text-primary-400 data-[current-link-name='']:(text-[#CBD5E1])"
          >
            {{ currentLink.name || $t("personalCenterInfo.unbindedLogin") }}
          </p>
          <div>
            <span
              v-if="currentLink.name"
              class="cursor-pointer text-destructive"
              @click="unbind"
            >{{ $t("headerBar.actionUnbind") }}</span>
            <span v-else class="cursor-pointer text-blue-4">
              <template v-if="currentLink.unite_tpye === UNTIE_TYPE.GOOGLE">
                <GoogleLogin :callback="bindGoogle">
                  <span>{{ $t("bindLogin.bind") }}</span>
                </GoogleLogin>
              </template>
              <template v-else>
                <span @click="handleBind">{{ $t("bindLogin.bind") }}</span>
              </template>
            </span>
          </div>
        </div>
        <template v-else>
          <InputBox
            v-if="editEmailStatus === EDIT_EMAIL_STATUS.NOT_START"
            v-model="userInfo.email"
            class="h-10 w-70 [&>input]:(font-800 font-RobotoSlab) ![&>input]:pl-0"
            :label="$t('headerBar.labelEmail')"
            :placeholder="$t('personalCenterInfo.unbindedEmail')"
          >
            <template #suffix>
              <div v-debounce="() => sendCode()">
                {{ $t("headerBar.sendCode") }}
              </div>
            </template>
          </InputBox>
          <InputBox
            v-if="editEmailStatus === EDIT_EMAIL_STATUS.WAIT_CODE"
            v-model="emailCode"
            class="h-10 w-70 [&>input]:(font-800 font-RobotoSlab) ![&>input]:pl-0"
            :label="$t('headerBar.labelEmail')"
            :placeholder="$t('headerBar.enterCode')"
            @input="onEmailCodeInput"
          >
            <template #suffix>
              <div v-debounce="() => sendCode()">
                {{
                  sendSeconds
                    ? $t("headerBar.resendCode", {
                      sendSeconds,
                    })
                    : $t("headerBar.sendCode")
                }}
              </div>
            </template>
          </InputBox>
        </template>
        <div
          class="mt-9 h-9 w-20 cursor-pointer rounded-md bg-destructive-300 pb-5 text-white leading-9 shadow-[0_0_3rem_0_rgba(246,81,52,0.46)]"
          @click="saveInfo"
        >
          <span class="text-3.5">{{ $t("personalCenterInfo.save") }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
