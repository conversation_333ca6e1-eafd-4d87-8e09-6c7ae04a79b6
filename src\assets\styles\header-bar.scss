// 现在这个文件是没有使用的
.header-bar {
  height: 80px;
  background: #fff;
  padding: 0 30px;
  box-shadow: 0 0 20px 0 #00000012;
  position: fixed;
  width: 100%;
  z-index: 10;

  @include narrow {
    height: 50px;
  }

  &__avatar {
    width: 48px;
    height: 48px;
    border-radius: 15px;
    transition: box-shadow 0.3s;
  }

  &__info {
    cursor: pointer;
    font-family: 'Roboto Slab';
    font-size: 20px;

    i {
      color: #d6d7d6;
    }

    @include narrow {
      padding-right: 30px;
    }
  }

  &__icon {
    transition: transform 0.3s var(--ani-bezier);

    &--active {
      transform-origin: center;
      transform: rotate(180deg);
    }
  }

  &__placeholder {
    height: 80px;

    @include narrow {
      height: 50px;
    }
  }

  &__side {
    top: 0;
    right: 0;
    position: fixed;
    width: 450px;
    height: 100%;
    background: #fff;
    transition: all 0.3s var(--ani-bezier);
    box-shadow: 0 0 30px 0 #00000012;
    z-index: 1;
    display: flex;
    flex-direction: column;

    &__title {
      padding-bottom: 30px;
      padding-right: 30px;
      margin: 110px 0 0 30px;
      border-bottom: 1px solid #f1f1f1;

      i {
        color: #cbd5e1;
      }
    }

    &__avatar {
      width: 160px;
      height: 160px;
    }

    &__button {
      div {
        flex: 1;
        height: 60px;
        text-align: center;
        color: #818181;
        cursor: pointer;

        &:last-child {
          background: #f65134;
          color: #fafafa;
        }
      }
    }

    &__container {
      padding: 0 30px;
      height: 100%;
      overflow: auto;
    }

    &__free,
    &__plus {
      width: 175px;
      border-radius: 10px;
      text-align: center;
      padding: 25px 0;

      > div:first-child {
        font-family: 'Lily Script One';
        font-weight: 400;
        font-size: 36px;
      }

      > div:last-child {
        font-size: 14px;

        > div {
          padding-top: 15px;
        }
      }
    }

    &__free {
      background: #fff;
      border: 1px dashed #0092ff;

      > div:first-child {
        color: #0092ff;
      }

      > div:last-child {
        color: #64748b;
      }
    }

    &__plus {
      background: #f65134;
      border: 1px solid #f65134;
      box-shadow: 0 0 20px 0 #f6513421;

      > div:first-child {
        color: #fff;
      }

      > div:last-child {
        color: #fff;
      }
    }

    &__package {
      width: 175px;
      height: 150px;
      cursor: pointer;
      background: #fff;
      border: 1px dashed #cbd5e1;
      border-radius: 10px;
      margin-bottom: 30px;
      transition: all 0.3s var(--ani-bezier);
      position: relative;

      > div:first-child {
        padding: 20px 15px 0;
      }

      > div:last-child {
        border-top: 1px dashed #cbd5e1;
        border-radius: 10px;
        padding: 10px 0;
        transition: all 0.3s var(--ani-bezier);
        position: absolute;
        bottom: -1px;
        left: -1px;
        width: calc(100% + 2px);
      }

      &__month {
        color: #f65134;
        font-family: 'Roboto Slab';
        font-size: 36px;
        line-height: 38px;
        font-weight: 700;
      }

      &:hover {
        > div:last-child {
          color: #f65134;
        }
      }

      &--active {
        > div:last-child {
          color: #fff;
          background: #f65134;
          border: 1px solid #f65134;
          font-family: 'Roboto Slab';
        }

        &:hover {
          > div:last-child {
            color: #fff;
          }
        }
      }

      &--disable {
        > div:last-child {
          background: #f9f9f9;
          color: #bcbcbc;
          border: 1px dashed #cbd5e1;
        }

        &:hover {
          > div:last-child {
            color: #bcbcbc;
          }
        }
      }
    }

    &__card {
      height: 120px;
      position: relative;
      background: radial-gradient(circle at 10px 10px, transparent 0, #f65134 0) left -9px top 0
        repeat-y,
        radial-gradient(circle at 10px 9px, transparent 8px, #f65134 5px) right -11px top 10px repeat-y;
      background-size: 100% 120px, 20px 28px;

      &::after {
        top: 0;
        left: 0;
        position: absolute;
        content: 'Plus';
        color: #ffffff15;
        font-size: 140px;
        line-height: 130px;
        font-family: 'Lily Script One';
      }

      &__title {
        font-size: 80px;
        color: #fff;
        font-family: 'Roboto Slab';
        font-weight: 700;
      }
    }

    &__box {
      padding: 20px 30px;
      border: 1px dashed #cbd5e1;
      border-radius: 10px;
      font-family: 'Roboto Slab';

      .text-13 {
        font-size: 13px;
      }
    }

    &__code {
      border: 1px dashed #cbd5e1;
      border-radius: 5px;
      padding: 40px 20px 30px;
      transition: all 0.3s var(--ani-bezier);
      width: 172px;
      height: 200px;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      img,
      iframe {
        width: 120px;
        height: 120px;
        margin: auto;
        overflow: hidden;
      }
    }

    &__lottie {
      position: relative;
      padding-top: 180px;
      text-align: center;

      &__title {
        font-family: 'Lily Script One';
        font-size: 80px;
        color: #ffb200;
      }

      &__subtitle {
        font-size: 18px;
        font-weight: 600;
      }

      > svg {
        position: absolute;
        top: -60px;
        width: 100% !important;
        height: 600px !important;
        left: 0;
        right: 0;
        margin: auto;
      }
    }

    &__invite {
      &__card {
        width: 170px;
        height: 180px;
        background: #fff;
        border: 1px dashed #f65134;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        &__title {
          color: #f65134;
          font-size: 50px;
          font-weight: 600;
          font-family: 'Roboto Slab';
        }

        &:first-child {
          border: 1px dashed #cbd5e1;
        }
      }

      &__code {
        padding: 25px;
        border: 1px dashed #cbd5e1;
        border-radius: 10px;

        img {
          width: 120px;
          margin-right: 25px;
          flex-shrink: 1;
        }

        &__title {
          font-family: 'Roboto Slab';
          font-weight: 600;
        }

        &__subtitle {
          font-family: 'Roboto Slab';
        }

        &__button {
          background: #f65134;
          border: 1px solid #f65134;
          box-shadow: 0 0 20px 0 #f6513421;
          border-radius: 50px;
          width: 100px;
          padding: 8px 0;
          font-size: 14px;
          color: #fff;
          text-align: center;
          cursor: pointer;
        }
      }

      &__list {
        font-family: 'Roboto Slab';
      }
    }
  }

  &__menu {
    top: 80px;
    right: 0;
    z-index: 5;
    position: fixed;
    width: 560px;
    background: #fff;
    box-shadow: 0 0 20px 0 #00000012;

    .el-calendar__body {
      padding: 0;

      table {
        border: none;
      }

      table > thead {
        display: none;
      }
    }

    .el-calendar__header {
      padding: 0;
      border-bottom: none;
    }

    .el-calendar-table {
      td {
        padding: 0;
        background: transparent !important;
        border: none !important;
      }

      .prev,
      .next {
        opacity: 0;
        pointer-events: none;
      }
    }

    .el-calendar-day {
      padding: 0 !important;
      width: 20px;
      line-height: 20px;
      height: 20px !important;
      background: transparent !important;
    }

    .el-calendar-table {
      display: none;
    }
  }

  &__mask {
    position: fixed;
    width: 100%;
    height: 100%;
    opacity: 0.9;
    background: #fff;
    transition: all 0.3s var(--ani-bezier);
    z-index: 1;
  }

  &__divider {
    padding-top: 5px;

    @include narrow {
      padding: 5px 8px 0 !important;
    }
  }
}
