<template>
  <component :is="wrapperElement" class="relative m-auto inline text-72px font-600 font-Kanit font-italic">
    <p class="inline-block px-8px py-4px">
      <a
        ref="contentElement"
        class="cursor-default text-inherit no-underline"
        :class="currentIndex === 0 ? 'pr-2px' : 'pr-36px'"
      />
    </p>
  </component>
</template>

<script setup lang="ts">
const props = defineProps({
  content: {
    type: String,
    default: 'Juchats infinite love',
  },
  wrapperElement: {
    type: String,
    default: 'div',
  },
  typeDelay: {
    type: Number,
    default: 800,
  },

})

const contentElement = ref() // 用于输入文字的元素
const currentIndex = ref(0) // 当前输入的字符索引

// 添加下一个字符
function addNextCharacter() {
  if (contentElement.value && currentIndex.value < props.content.length) {
    contentElement.value.textContent += props.content[currentIndex.value]
    currentIndex.value++
  }
}

// 开始输入
function startTyping() {
  if (currentIndex.value < props.content.length) {
    setTimeout(() => {
      addNextCharacter()
      startTyping()
      // 随机延迟 30-180ms 输入下一个字符
    }, Math.random() * 150 + 30)
  }
}

onMounted(() => {
  // 默认延迟 800ms 开始输入
  setTimeout(() => startTyping(), props.typeDelay)
})

watchEffect(() => {
  currentIndex.value = 0
  if (contentElement.value) {
    contentElement.value.textContent = ''
  }
})
</script>

<style lang="scss" scoped>
@property --pointer-color {
  syntax: '<color>';
  inherits: false;
  initial-value: #fff;
}

a {
  background:
    linear-gradient(
      90deg,
      transparent,
      transparent calc(100% - 3px),
      var(--pointer-color) calc(100% - 3px),
      var(--pointer-color)
    );
  background-size: 100% 62%;
  background-position: 0 20px;
  background-repeat: no-repeat;
  animation: color-change 0.8s linear infinite;

  @apply text-[var(--black)];
}

@keyframes color-change {
  0%, 40% {
    --pointer-color: var(--black);
  }

  60%, 100% {
    --pointer-color: transparent;
  }
}
</style>
