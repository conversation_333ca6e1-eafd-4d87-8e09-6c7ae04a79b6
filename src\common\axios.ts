import { notify } from '@/common/tools'
import i18n from '@/i18n'
import { NotificationType } from '@/model/common'
import router from '@/router'
import axios from 'axios'
import { isNull, isUndefined } from 'lodash'
import { showNotification, upgrade } from '.'
import { mixpanel } from '../config/mixpanel'

const route = router.currentRoute

// 定义不需要显示错误提示的API路径列表
const SILENT_APIS = [
  '/gpt/generateCompletions',
  '/gpt/shareInfo',
  '/gpt/generateDialogTitle',
  '/gpt/requestStatus',
  '/user/setStyle',
]

const service = axios.create({
  baseURL: '/gw/chatweb',
  timeout: 1000 * 60 * 5, // 5分钟
})

service.interceptors.request.use(
  (config: any) => {
    const url = config.url
    if (url.includes(import.meta.env.VITE_APP_PAY_URL) || url.includes(import.meta.env.VITE_APP_GPT_URL)) {
      config.baseURL = ''
    }
    const localeLang = localStorage.getItem('localeLang') || ''
    config.headers = {
      ...config.headers,
      'Jtoken': localStorage.token,
      'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)
service.interceptors.response.use(
  (res: any) => {
    const data = res.data
    const url = res.config.url
    // 检查是否是静默API
    const isSilentApi = SILENT_APIS.some(api => url.includes(api))

    if ([500].includes(data.code) || ['操作过快, 请稍后尝试', '无法支付'].includes(data.msg)) {
      // 如果是静默API，直接reject而不显示错误提示
      if (isSilentApi) {
        return Promise.reject(data.msg)
      }

      if (route.value.name === 'Home' || route.value.name === 'Gift') {
        notify.error({
          title: data.msg,
        })
      }
      else {
        notify.error({
          title: data.msg,
          message: data.data ? data.data.join(',') : '',
        })
      }

      return Promise.reject(data.msg)
    }
    else if ([401].includes(data.code)) {
      if (localStorage.token) {
        localStorage.removeItem('token')
        notify.error({
          title: i18n.global.t('tipMessage.loginFail'),
        })
        const query: { isLogin: string } = { isLogin: 'true' }
        router.push({
          name: 'Home',
          query,
        })
      }

      return Promise.reject(data.msg)
    }
    else if ([403].includes(data.code)) {
      notify.error({
        dangerouslyUseHTMLString: true,
        message: `${data.msg}<span class="notification__upgrade">${i18n.global.t('tipMessage.upgrade')}</span>`,
        onClick: upgrade,
      })
    }
    else if ([402].includes(data.code)) {
      return Promise.reject(data)
    }
    return !isUndefined(data.data) && !isNull(data.data) ? data.data : data
  },
  (error) => {
    const url = error.config?.url
    // 检查是否是静默API
    const isSilentApi = url && SILENT_APIS.some(api => url.includes(api))

    // 检查是否是取消的请求
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }

    // 检查是否是可取消的请求
    if (error.config && error.config.cancelable) {
      return Promise.reject(error)
    }

    // 对于其他错误，如果不是静默API才显示通用错误消息
    if (!isSilentApi) {
      notify.error({
        title: i18n.global.t('tipMessage.error'),
      })
    }

    return Promise.reject(error)
  },
)

export default service
