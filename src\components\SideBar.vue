<template>
  <div
    class="relative z-10 h-full flex flex-col bg-primary-600 text-sm text-primary-300 transition-cusbezier-400 lt-md:(absolute bottom-0 left-0 top-0 z-99 max-h-100dvh bg-[var(--white)] shadow-none) dark:(text-[var(--s2)])"
    :class="[
      // isZenMode先控制横向运动和阴影
      isZenMode && '-ml-[260px] shadow-2xl',
      // isZenModeDelay会在isZenMode为false之后，继续为true一段时间，用于控制动画延迟
      isZenModeDelay && '!duration-800 !transition-delay-300',
      (isZenModeDelay && !expandSidebar) && '!ml-0',
    ]"
    :style="sideBarStyle"
  >
    <div
      class="mb-6 grow overflow-x-hidden overflow-y-auto"
      :class="[
        shrink ? 'invisible opacity-0' : 'visible opacity-100',
      ]"
    >
      <div class="h-full w-full overflow-hidden">
        <div class="h-full flex flex-col px-5 lt-md:px-0">
          <div v-if="isPc" class="w-55">
            <div class="w-full flex-c" :class="[isClient ? 'mt-6' : '']">
              <div class="relative mb-5 h-10 w-full rounded-1.5 bg-secondary">
                <div class="absolute inset-0 p-1">
                  <div
                    class="h-8 w-28 rounded-1 bg-white shadow-[#00000012] shadow-[0_1.25rem_5rem_0] transition-cusbezier-400"
                    :class="selected === 0 ? 'translate-x-0' : 'translate-x-25'"
                  ></div>
                </div>
                <div
                  class="relative z-1 h-full w-full flex items-center bg-transparent p-1.5 text-sm text-[var(--sidebar-option-text)] font-400 [&>div]:(h-full flex-c flex-1 cursor-pointer)"
                >
                  <div
                    :data-selected="selected === 0"
                    class="font-500 font-Lexend data-[selected=true]:text-black"
                    @click="select(0)"
                  >
                    {{ $t("sideBar.normalDialog") }}
                  </div>
                  <div
                    :data-selected="selected === 1"
                    class="font-500 font-Lexend data-[selected=true]:text-black"
                    @click="select(1)"
                  >
                    {{ $t("sideBar.groupDialog") }}
                  </div>
                </div>
              </div>
            </div>
            <div class="w-full flex-c cursor-pointer" @click="newMessage()">
              <div
                class="h-10 w-full flex-y-c select-none justify-between border-1px border-[var(--sidebar-new-border)] rounded-1.25 border-dashed px-5 py-2.75 text-3.25 text-secondary-100 hover:border-[var(--sidebar-new-border-hover)]"
              >
                <span class="font-Lexend"> {{ $t("sideBar.createDialog") }}</span>

                <div class="mr-[-11px] flex items-center gap-x-2 text-14px">
                  <div class="h-22px w-22px flex items-center justify-center rounded-5px bg-#edf0f4 dark:(border-1px border-#272727 bg-transparent shadow-sm)">
                    <i class="i-ju-f7shift"></i>
                  </div>
                  <div class="h-22px w-22px rounded-5px bg-#edf0f4 text-center font-300 leading-22px dark:(border-1px border-#272727 bg-transparent shadow-sm)">N</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="px-6 pt-6">
            <i
              class="i-ju-back mb-5 h-4.5 w-4.5 text-primary-100"
              @click="expandSidebar = false"
            ></i>
            <div class="flex whitespace-nowrap">
              <div class="[&_.llm-second-level]:(max-w-22.5 of-hidden text-ellipsis whitespace-nowrap)">
                <LLMSelect :panel-width="`${sideBarWidth}px`" />
              </div>
            </div>
            <div class="mt-6 w-70 flex-c cursor-pointer" @click="newMessage()">
              <div
                class="h-10 w-full flex-c border-1px border-[var(--sidebar-new-border)] rounded-2.5 border-dashed text-center text-3.25 text-[var(--sidebar-option)] hover:border-[var(--sidebar-new-border-hover)]"
              >
                <span>{{ $t("sideBar.createDialog") }}</span>
              </div>
            </div>
          </div>
          <ChatHistoryList
            ref="chatHistoryListRef"
            v-model:group-name="groupName"
            v-model:message-name="messageName"
            class="no-scrollbar h-full overflow-y-auto"
            :in-panel="false"
            :selected="selected"
            :current-message-list="currentMessageList"
            :menu-data="menuData"
            :message-id="messageId"
            :loading="loading"
            :groups="groups"
            :editing="editing"
            :messages="messages"
            :format-message="formatMessage"
            @delete-group="handleDeleteGroup"
            @delete-group2="deleteGroup"
            @hide-modal="hideModal"
            @select-group-message="selectGroupMessage"
            @select-message="selectMessage"
            @operation="handleOperation"
            @reset-title="resetTitle"
            @edit-message="editMessage"
            @blur-message="blurMessage"
            @switch-message="switchMessage"
            @rename-group="renameGroup"
            @focus-group="focusGroup"
            @drag-update="onDragUpdate"
            @edit-group="editGroup"
            @move-group="moveGroup"
            @operation-group="handleOperationGroup"
          />

          <FloatCard
            v-if="isPc && isZenMode"
            v-model:is-show="floatCardIsShow"
            @card-mouseenter="manualFloatCardHovered = true"
            @card-mouseleave="manualFloatCardHovered = false"
          >
            <ChatHistoryList
              ref="chatHistoryListRef"
              v-model:group-name="groupName"
              v-model:message-name="messageName"
              :in-panel="true"
              :selected="selected"
              :current-message-list="currentMessageList"
              :menu-data="menuData"
              :message-id="messageId"
              :loading="loading"
              :groups="groups"
              :editing="editing"
              :messages="messages"
              :format-message="formatMessage"
              @delete-group="handleDeleteGroup"
              @delete-group2="deleteGroup"
              @hide-modal="hideModal"
              @select-group-message="selectGroupMessage"
              @select-message="selectMessage"
              @operation="handleOperation"
              @reset-title="resetTitle"
              @edit-message="editMessage"
              @blur-message="blurMessage"
              @switch-message="switchMessage"
              @rename-group="renameGroup"
              @focus-group="focusGroup"
              @drag-update="onDragUpdate"
              @edit-group="editGroup"
              @move-group="moveGroup"
              @operation-group="handleOperationGroup"
            />
          </FloatCard>
        </div>
      </div>
    </div>
    <UpdateNote v-if="!shrink" />
    <!-- 这里曾经使用v-if，但是会导致侧边栏在展开时无动画，故使用v-show -->
    <UserInfo v-show="!shrink" ref="userInfoRef" class="flex-shrink-0"></UserInfo>
    <Dialog v-if="isVisible"></Dialog>
    <div
      v-if="isPc"
      ref="sideBarRef"
      class="side-bar__shrink z-20"
      :class="[
        shrink && 'side-bar__shrink--shrink',
        (floatCardIsShow && isZenMode) && '!opacity-3',
      ]"
      @click="toggleSideBar"
    >
      <div></div>
      <div></div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import type { Instance as PopperInstance } from '@sxzz/popperjs-es'
import service from '@/common/axios'
import { formatTimestampCategory, notify, toTimestamp } from '@/common/tools'
import { mixpanel } from '@/config/mixpanel'
import { GENERATE_IMAGE_MODES, LLMS_MODES, MODE_TYPE } from '@/enum'
import { useDialog } from '@/hooks/useDialog'
import { getI18nTranslate, useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts'
import { useModalPosition } from '@/hooks/useModalPosition'
import { useShareChat } from '@/hooks/useShare'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useAppStore } from '@/stores/app'
import { useModalsStore } from '@/stores/modals'

const props = defineProps({
  groups: {
    type: Array as any,
    default: () => [],
  },
  messageId: {
    type: Number,
    default: null,
  },
  messages: {
    type: Array as any,
    default: () => [],
  },
  models: {
    type: Array as any,
    default: () => [],
  },
  getFlag: {
    type: Function,
    default: () => {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits([
  'new',
  'delete',
  'deleteGroup',
  'select',
  'switch',
  'create',
  'grouping',
  'edit',
  'blur',
  'share',
  'deleteChats',
  'resetTitle',
  'moveGroup',
])

function toggleSideBar() {
  if (!isZenMode.value) {
    shrink.value = !shrink.value
  }
}

const chatHistoryListRef = ref()
const { modalStyle } = useModalPosition()
const t = useGlobalI18n()
const appStore = useAppStore()
const { expandSidebar, isPc, isClient, chatViewData, isZenMode, isZenModeDelay } = storeToRefs(appStore)
const { groups } = toRefs(chatViewData.value)
const modalsStore = useModalsStore()
function moveGroup(param: { groupId: number, dialogId: number }) {
  emits('moveGroup', param)
}

const sideBarWidth = computed(() => {
  return isPc.value ? 260 : 330
})

// sidebar的宽度行内样式对象，为了将当前展开的sidebar宽度传给子组件
const sideBarStyle = computed(() => {
  return {
    width: shrink.value ? '0' : `${sideBarWidth.value}px`,
  }
})
const { getUserInfo } = useUserInfo()
const { shareContent } = useShareChat()
const { openDialog, closeDialog, isVisible } = useDialog()
watch(
  () => props.messages,
  () => {
    getMenus()
  },
)
const timer: any = ref()
const editing = ref(false)
const userInfoRef = ref<any>(null)
const popoverRef = ref<Record<number | string, any>>({})
const chatInputRef = ref<HTMLInputElement[]>([])

const deleteChatsIds = ref<any>([])
const currentMessageList = ref<any>([])

const groupName = ref('')
const messageName = ref('')
const sideBarRef = useTemplateRef<HTMLDivElement>('sideBarRef')
const manualFloatCardHovered = ref(false)

const sideBarIsHovered = useElementHover(sideBarRef, {
  delayLeave: 300,
})
const chatHistoryListIsHovered = useElementHover(chatHistoryListRef, {
  delayLeave: 300,
})

const floatCardIsShow = computed(() => {
  return sideBarIsHovered.value || chatHistoryListIsHovered.value || manualFloatCardHovered.value
})
const shrink = computed({
  get() {
    return !expandSidebar.value
  },
  set(val) {
    expandSidebar.value = !val
  },
})

const childGroups: any = computed(() => {
  return props.groups.filter(
    (element: any) => !element.starFlag && !element.defaultFlag,
  )
})

const menuData = ref<any>([
  { key: 'llms', label: 'Chat', data: 'LLMS', visible: false, visibleEdit: false },
  { key: 'images', label: 'Vision', data: 'IMAGES', visible: false, visibleEdit: false },
  { key: 'artifacts', label: 'Artifacts', data: 'ARTIFACTS', visible: false, visibleEdit: false },
  { key: 'voice', label: 'Voice', data: 'VOICE', visible: false, visibleEdit: false },
])

const selected = ref(0)
const select = (param: number) => {
  if (param === 0) {
    mixpanel.regularDialog()
  }
  else if (param === 1) {
    mixpanel.groupDialog()
  }
  selected.value = param
}
const newMessage = () => {
  hideModal()
  if (!isPc.value) {
    expandSidebar.value = false
  }
  emits('new')
}
const hideModal = () => {
  const currentModal = modalsStore.popModal()
  currentModal?.hide()
}
const selectMessage = ({ id, ttsLanguageTypeId, ttsType, modeId, editing, groupId }: any) => {
  hideModal()
  if (editing) {
    return
  }
  if (!isPc.value) {
    expandSidebar.value = false
  }
  emits('select', { id, ttsLanguageTypeId, ttsType, modeId, groupId })
}

const selectGroupMessage = (item: any) => {
  if (item.selected) {
    deleteChatsIds.value = deleteChatsIds.value.filter(
      (item: any) => item !== item.id,
    )
  }
  else {
    deleteChatsIds.value.push(item.id)
  }
  item.selected = !item.selected
}

const handleOperation = async (operation: {
  id: number
  type: string
  mode: 'MESSAGE' | 'GROUP'
  name: string
}) => {
  const { type, id, mode, name } = operation
  const popoverInstance = unref(popoverRef.value[id])
  popoverInstance?.hide()
  if (type === 'edit') {
    messageName.value = name
    emits('edit', {
      messageId: id,
      type: mode,
      messageName: messageName.value,
    })
    nextTick(() => {
      chatInputRef.value
        .find(item => Number(item.getAttribute('data-id')) === id)
        ?.focus()
    })
  }
  else if (type === 'delete') {
    if (!isPc.value) {
      expandSidebar.value = false
    }
    openDialog({
      titleContent: t('sideBar.removeDialog'),
      style: modalStyle.value,
      content: () => (
        <p>
          {getI18nTranslate('sideBar.willRemove')}
          「
          <strong>{name.length > 20 ? `${name.slice(0, 20)}...` : name}</strong>
          」
          {' '}
          {getI18nTranslate('sideBar.irreversible')}
        </p>
      ),
      contentProps: {
        text: name,
      },
      onSubmit: () => {
        emits('delete', { id })
        // 参数为false，代表不是取消按钮触发的关闭
        closeDialog(false)
      },
      onCancel: () => {
        // 如果点击了取消，再自动展开
        if (!isPc.value) {
          expandSidebar.value = true
        }
      },
    })
  }
  else {
    if (!isPc.value) {
      expandSidebar.value = false
    }
    openDialog({
      titleContent: t('sideBar.shareDialog'),
      style: modalStyle.value,
      content: await shareContent(id, name),
      showFooter: false,
      onCancel: () => {
        if (!isPc.value) {
          // 如果点击了取消，再自动展开
          expandSidebar.value = true
        }
      },
    })
  }
}

const editSelects = computed(() => {
  return currentMessageList.value
    .filter((it: any) => it.selected)
    .map((it: any) => it.id)
})

const handleOperationGroup = async (
  event: Event,
  type: 'cancel' | 'delete',
  item: any,
) => {
  event.stopPropagation()
  if (type === 'cancel') {
    deleteChatsIds.value = []
    item.visibleEdit = !item.visibleEdit
  }
  else {
    openDialog({
      titleContent: t('sideBar.removeDialog'),
      style: modalStyle.value,
      content: ContentText,
      contentProps: {
        text: item?.label || 'GPT Forte',
        deleteAll: editSelects.value.length === currentMessageList.value.length,
      },
      onSubmit: () => {
        item.visibleEdit = !item.visibleEdit
        emits('deleteChats', editSelects.value)
        closeDialog()
      },
    })
  }
}

const ContentText = defineComponent({
  props: ['text', 'deleteAll'],
  setup: (props: { text: string, deleteAll: boolean }) => {
    return () => (
      <p>
        {props.deleteAll
          ? (
              <>
                {getI18nTranslate('sideBar.deleteConfirmation')}
                <span class="py-1 font-bold">
                  {props.text.length > 20
                    ? `${props.text.slice(0, 20)}...`
                    : props.text}
                </span>
                {getI18nTranslate('sideBar.below')}
                {getI18nTranslate('sideBar.allConversations')}
                {getI18nTranslate('sideBar.conversations')}
              </>
            )
          : getI18nTranslate('sideBar.deletePart')}
      </p>
    )
  },
})
const handleDeleteGroup = (
  event: Event,
  config: {
    type: 'delete-all' | 'delete-select'
    list?: any[]
    groupInfo?: any
  },
) => {
  event.stopPropagation()
  // const popoverInstance = unref(popoverRef.value['chat-group'])
  // popoverInstance?.hide()
  const { type, list, groupInfo } = config

  if (type === 'delete-all') {
    if (!isPc.value) {
      expandSidebar.value = false
    }
    openDialog({
      titleContent: t('sideBar.removeDialog'),
      style: modalStyle.value,
      content: ContentText,
      contentProps: {
        text: groupInfo?.label || 'GPT Forte',
        deleteAll: true,
      },
      showClose: false,
      onSubmit: () => {
        emits(
          'deleteChats',
          list?.map(item => item.id),
        )
        closeDialog()
      },
      onCancel: () => {
        // 如果点击了取消，再自动展开
        if (!isPc.value) {
          expandSidebar.value = true
        }
      },
    })
  }
  else {
    // 如果当前未展开列表，则自动展开后再操作
    if (!groupInfo.visible) {
      chatHistoryListRef.value?.keyVisible(groupInfo, 'visible')
    }
    chatHistoryListRef.value?.keyVisible(groupInfo, 'visibleEdit')
    currentMessageList.value = JSON.parse(
      JSON.stringify(formatMessage.value[groupInfo.data]),
    ).map((it: any) => {
      it.selected = false
      return it
    })
  }
}

const switchMenuBar = ({ modeId, ttsType }: any) => {
  const array: any = menuData.value
  const modes = props.models.flatMap((element: any) => element.modes)
  const element = modes.find((element: any) => element.id === modeId)
  if (!element) {
    return
  }
  let targetKey = ''
  if (ttsType) {
    targetKey = 'voice'
  }
  else {
    if (element.type === MODE_TYPE.ARTIFACTS) {
      targetKey = 'artifacts'
    }
    else if (LLMS_MODES.includes(element.type)) {
      targetKey = 'llms'
    }
    else if (GENERATE_IMAGE_MODES.includes(element.type)) {
      targetKey = 'images'
    }
  }
  array.forEach((element: any) => {
    if (element.key === targetKey) {
      element.visible = true
    }
  })
  // menuData.value = array.sort((a: any) => a.key === targetKey ? -1 : 1)
}

const switchMessage = ({ groupId, reload, editing2 }: any) => {
  if (['boolean'].includes(typeof editing2) && editing2) {
    return
  }
  if (timer.value) {
    clearTimeout(timer.value)
  }
  timer.value = setTimeout(() => {
    emits('switch', { groupId, reload })
  }, 300)
}

const createGroup = async ({ name }: any) => {
  const data: any = await service.post('/gpt/createGroup', { name })
  emits('grouping', { name, id: JSON.parse(data) })
}

const editGroup = () => {
  if (editing.value) {
    createGroup({ name: groupName.value })
    groupName.value = ''
  }
  editing.value = !editing.value
}

const editMessage = ({ messageId, type }: any) => {
  emits('edit', { messageId, type, messageName: messageName.value })
  messageName.value = ''
}

const blurMessage = ({ messageId, type }: any) => {
  emits('blur', { messageId, type })
  messageName.value = ''
}

const renameGroup = ({ groupId, editing2 }: any) => {
  clearTimeout(timer.value)
  if (editing2) {
    return
  }
  childGroups.value.forEach((element: any) => {
    if (element.id === groupId) {
      element.editing2 = !element.editing2
      nextTick(() => {
        chatInputRef.value
          .find(item => Number(item.getAttribute('data-id')) === groupId)
          ?.focus()
      })
    }
  })
}

const deleteGroup = ({ id }: any) => {
  emits('deleteGroup', { id })
}

const focusGroup = async ({ groupId, editing2 }: any) => {
  if (!groupName.value) {
    notify.error({
      title: t('chatView.groupPlaceholder').value,
    })
    return
  }
  await service.post('/gpt/updGroupName', {
    id: groupId,
    name: groupName.value,
  })
  childGroups.value.forEach((element: any) => {
    if (element.id === groupId) {
      element.name = groupName.value
      groupName.value = ''
      renameGroup({ groupId, editing2 })
    }
  })
}
const scrollTop = chatHistoryListRef.value?.scrollTop

const getMenus = () => {
  if (!localStorage.menus) {
    localStorage.setItem('menus', JSON.stringify(menuData.value))
  }
  try {
    const changed = localStorage.menus !== JSON.stringify(menuData.value)
    // const store = JSON.parse(localStorage.menus)
    // const len = store.length
    // 旧聊天结构初始化
    if (changed) {
      localStorage.setItem('menus', JSON.stringify(menuData.value))
    }
    else {
      menuData.value = JSON.parse(localStorage.getItem('menus') as string)
    }
  }
  catch (e) {
    console.log('get menus storage error', e)
  }
}
const resetVoice = (array: any) => {
  const uniqueSet = new Set(array.map((item: any) => item.id))
  return Array.from(uniqueSet).map(id =>
    array.find((item: any) => item.id === id),
  )
}
const formatMessage: any = computed(() => {
  const LLMS: any = []
  const IMAGES: any = []
  const VOICE: any = []
  const ARTIFACTS: any = []
  const showVoice = false // 不再展示Voice
  props.messages.forEach((message: any) => {
    message.updateTimestamp = toTimestamp(message.updateTime || message.createTime)
    message.dateCategory = formatTimestampCategory(message.updateTimestamp)
    if (message.ttsType && showVoice) {
      VOICE.push(message)
    }
    else if (message.type === MODE_TYPE.ARTIFACTS) {
      ARTIFACTS.push(message)
    }
    else {
      if (LLMS_MODES.includes(message.type)) {
        LLMS.push(message)
      }
      else if (GENERATE_IMAGE_MODES.includes(message.type)) {
        IMAGES.push(message)
      }
    }
  })
  return {
    LLMS: LLMS.sort((a: any, b: any) => b.updateTimestamp - a.updateTimestamp),
    IMAGES: IMAGES.sort((a: any, b: any) => b.updateTimestamp - a.updateTimestamp),
    VOICE: resetVoice(VOICE).sort((a: any, b: any) => b.updateTimestamp - a.updateTimestamp),
    ARTIFACTS: ARTIFACTS.sort((a: any, b: any) => b.updateTimestamp - a.updateTimestamp),
  }
})

const onDragUpdate = async ({ array, data }: any) => {
  const moveDialogId = data.id
  // array中id等于moveDialogId的上一个对象 为preDialogId
  // 注：因/dialogs接口获取的数据为倒叙 则preIndex为下一个对象的index
  const preIndex = array.findIndex((item: any) => item.id === moveDialogId) + 1
  const preDialogId = array[preIndex]?.id
  await service.post('/gpt/moveDialogPosition', { moveDialogId, preDialogId })
}

const upgrade = () => {
  userInfoRef.value.upgrade()
}

function resetTitle(id: string) {
  const popoverInstance = unref(popoverRef.value[id])
  popoverInstance?.hide()
  if (!id) {
    return
  }
  emits('resetTitle', id)
}

defineExpose({
  scrollTop,
  select,
  upgrade,
  selectMessage,
  switchMenuBar,
})
onMounted(async () => {
  await getUserInfo()
  useKeyboardShortcuts('Shift+N', () => {
    newMessage()
  })
})

// 根据移动端和PC端切换收缩状态
watchEffect(() => {
  nextTick(() => {
    if (!isZenMode.value) {
      shrink.value = !isPc.value
    }
  })
})
</script>

<style scoped lang="scss">
.sidebar-list {
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }
}

.side-bar__shrink {
  left: 280px;
  top: 50%;
  transform: translateY(calc(-50% - 40px));
  position: absolute;
  cursor: pointer;
  transition: all 0.3s var(--ani-bezier);
  padding: 50px 50px 50px 0;
  opacity: 0.1;

  > div {
    width: 6px;
    height: 12px;
    transition: all 0.3s var(--ani-bezier);
    background: var(--black);
    border-radius: 9999px;
  }

  > div:first-child {
    transform: rotate(0deg) translateZ(0);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  > div:last-child {
    transform: rotate(0deg) translateZ(0);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  &:hover {
    opacity: 1;

    > div:first-child {
      transform: translateY(0.05rem) rotate(15deg) translateZ(0);
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    > div:last-child {
      transform: translateY(-0.05rem) rotate(-15deg) translateZ(0);
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }
  }

  &--shrink {
    left: 10px;

    > div:first-child {
      transform: translateY(0.05rem) rotate(-15deg) translateZ(0);
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    > div:last-child {
      transform: translateY(-0.05rem) rotate(15deg) translateZ(0);
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }

    &:hover {
      > div:first-child {
        transform: translateY(0.05rem) rotate(-15deg) translateZ(0);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }

      > div:last-child {
        transform: translateY(-0.05rem) rotate(15deg) translateZ(0);
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes checkmark {
  0% {
    stroke-dashoffset: 45;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes uncheckmark {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 45;
  }
}

.animate-check {
  animation: checkmark 0.3s ease forwards;
}

.animate-uncheck {
  animation: uncheckmark 0.3s ease forwards;
}

.group_dropdown {
  :deep(.el-dropdown-menu__item) {
    background: transparent !important;
    font-size: 13px;
    color: inherit;

    &:hover {
      font-weight: 600;
    }
  }
}
</style>
