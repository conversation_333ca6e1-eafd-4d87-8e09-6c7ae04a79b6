import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3'
import Mermaid from './Mermaid.vue'

const meta: Meta<typeof Mermaid> = {
  title: 'component/Mermaid',
  component: Mermaid,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

const splitTextCode = `%%{init: {"theme": "default", "themeVariables": {"primaryColor": "#FFD700", "secondaryColor": "#FF8C00", "tertiaryColor": "#8B0000"}}}%%
mindmap
  root((根节点))
    subnode1(子节点1)
    subnode2(子节点2)`

export const Primary: Story = {
  args: {
    code: splitTextCode,
  },
}
