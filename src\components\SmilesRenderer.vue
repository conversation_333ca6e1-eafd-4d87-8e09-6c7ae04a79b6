<template>
  <div :class="{ 'fixed left-0 top-0 size-full inset-0 z-[100] bg-white dark:bg-#1c1f24': isFullscreen, 'relative': !isFullscreen }">
    <div class="mb-2 h-48px flex-y-c gap-2 bg-inherit -ml-3px -mt-32px" :class="{ '!mt-8px !ml-8px !items-start': isFullscreen }">
      <button class="smiles-opration-button" @click="zoomIn">
        <i class="i-ri-zoom-in-line" />
      </button>
      <button class="smiles-opration-button" @click="zoomOut">
        <i class="i-ri-zoom-out-line" />
      </button>
      <button class="smiles-opration-button" @click="resetZoom">
        <i class="i-ri-refresh-line" />
      </button>
      <el-popover
        trigger="click"
        popper-class="mermaid-popover"
        :show-arrow="false"
        placement="bottom-start"
        class="min-w-auto"
        :width="20"
      >
        <template #reference>
          <button class="smiles-opration-button">
            <i class="i-ri-download-line" />
          </button>
        </template>
        <div class="flex flex-col gap-2 text-12px font-Lexend">
          <button class="smiles-dropdown-item" @click="() => downloadPNG({ backgroundColor: isDark ? '#000000' : '#ffffff' })">PNG</button>
          <button class="smiles-dropdown-item" @click="downloadSVG">SVG</button>
        </div>
      </el-popover>
      <button
        class="smiles-opration-button"
        @click="toggleFullscreen"
      >
        <i :class="isFullscreen ? 'i-ri-fullscreen-exit-line' : 'i-ri-fullscreen-line'" />
      </button>
    </div>
    <div
      ref="containerRef"
      class="smiles-container relative overflow-hidden"
      :class="[isFullscreen ? 'h-[calc(100vh-48px)] lt-md:mt-12' : 'h-[200px] md:h-[400px]']"
      @mousedown="startDrag"
      @mousemove="onDrag"
      @mouseup="stopDrag"
      @mouseleave="stopDrag"
      @wheel="handleWheel"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <div
        ref="smilesContainerRef"
        class="smiles-renderer absolute size-full flex items-center justify-center"
        :style="{ transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)` }"
      >
        <svg :id="svgId" class="size-full"></svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useThemeChange } from '@/hooks/useThemeChange'
import { useViewerControls } from '@/hooks/useViewerControls'

const props = defineProps({
  smiles: {
    type: String,
    required: true,
  },
})

const { isDark } = useThemeChange()
const themeName = computed(() => (isDark.value ? 'dark' : 'light'))
const svgId = ref(`smiles-svg-${Math.random().toString(36).substring(2, 9)}`)
const containerRef = ref<HTMLElement | null>(null)
const smilesContainerRef = ref<HTMLElement | null>(null)

declare const SmiDrawer: any

const {
  scale,
  position,
  isFullscreen,
  zoomIn,
  zoomOut,
  resetZoom,
  startDrag,
  onDrag,
  stopDrag,
  handleWheel,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd,
  toggleFullscreen,
  downloadPNG,
  downloadSVG,
} = useViewerControls({
  containerRef,
  contentRef: smilesContainerRef,
  onReset: () => {
    // 切换分子式时重置视图
    drawMolecule()
  },
}, 'Smiles')

// 绘制函数
const drawMolecule = () => {
  if (props.smiles && typeof SmiDrawer !== 'undefined') {
    const smiDrawer = new SmiDrawer({
      height: 300,
      compactDrawing: false,
    })
    smiDrawer.draw(props.smiles, `#${svgId.value}`, themeName.value, false)
  }
}

onMounted(() => {
  drawMolecule()
})

const clearSvg = () => {
  const svgElement = document.getElementById(svgId.value)
  if (svgElement) { svgElement.innerHTML = '' }
}

// 监听 prop 的变化并重新绘制
watch(() => props.smiles, () => {
  // 清空之前的SVG内容
  clearSvg()
  resetZoom() // 切换分子式时重置视图
  drawMolecule()
})

watch(
  themeName,
  () => {
    clearSvg()
    drawMolecule()
  },
)
</script>

<style scoped>
.smiles-renderer {
  transform-origin: center center;
  cursor: grab;
  touch-action: none;
}

.smiles-renderer:active {
  cursor: grabbing;
}

.smiles-container {
  touch-action: none;
}

.smiles-opration-button {
  @apply border rounded px-2 py-1 size-30px text-sm dark:border-color-#575E69 dark:text-#D8D8D8;
}

.smiles-dropdown-item {
  @apply block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black;
}
</style>
