<script setup lang="ts">
// import TheWelcome from '@/components/TheWelcome.vue'
</script>

<template>
  <div class="control-button__links">
    <!-- <i class="i-ri-translate"></i>
      <i class="i-ri-moon-line"></i> -->
    <!-- <i class="i-ri-wechat-2-fill"></i>
      <i class="i-ri-twitter-fill"></i>
      <i class="i-ri-send-plane-fill"></i> -->
  </div>
</template>

<style lang="scss" scoped>
.control-button__links {
  i {
    @apply transition-cusbezier-300 text-22px ml-20px p-9px cursor-pointer;

    background: url("//hermosssvip.herm.tools/images/border_image.svg") no-repeat;

    &:first-child {
      margin-left: 0;
    }

    &:hover {
      filter: drop-shadow(1px 1px 5px #636363);
    }
  }
}
</style>
