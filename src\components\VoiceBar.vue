<template>
  <div class="voice-bar">
    <div class="flex-start flex items-center">
      <div
        ref="voiceBar"
        :class="`voice-bar__box flex items-center justify-between cursor-pointer ${
          !receiving && 'voice-bar__box--active'
        }`"
        @click="fetchData"
      >
        <i v-if="running" class="i-ri-loader-4-line rotate leading-[0px]"></i>
        <i v-else class="i-ri-chat-poll-line"></i>
        <div v-if="file.seconds">{{ Number(file.seconds).toFixed(2) }}″</div>
      </div>
      <div class="ml-3 space-x-3">
        <i
          v-if="receiving"
          class="i-ri-translate cursor-pointer text-xl"
          @click="switchTranslate"
        ></i>
        <i
          v-if="receiving"
          class="i-ri-download-2-line cursor-pointer text-xl"
          @click="downloadVoice"
        ></i>
      </div>
    </div>
    <Transition name="fade" mode="out-in">
      <div v-if="translate && receiving" class="mt-25px border-1px border-[#d6d6d6] border-dashed p-25px text-sm font-[Roboto-Slab]">
        {{ file.content }}
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
const props: any = defineProps({
  file: {
    type: Object,
    default: () => {},
  },
  receiving: {
    type: Boolean,
    default: false,
  },
})
const translate = ref(false)
const running: any = ref(false)
const duration = ref(0)
const voiceBar = ref()
const switchTranslate = () => {
  translate.value = !translate.value
}
const downloadVoice = () => {
  location.href = `${import.meta.env.VITE_APP_IMAGE_URL}${props.file.filePath}`
}
const fetchData = () => {
  if (duration.value) {
    return
  }
  const audioContext = new (window.AudioContext || window.AudioContext)()
  fetch(`${import.meta.env.VITE_APP_IMAGE_URL}${props.file.filePath}`)
    .then(response => response.arrayBuffer())
    .then(buffer => audioContext.decodeAudioData(buffer))
    .then((decodedData) => {
      let audioSource: any = null
      duration.value = JSON.parse(decodedData.duration.toFixed(0))
      voiceBar.value.addEventListener('click', () => {
        if (running.value) {
          audioSource.stop(0)
          running.value = false
          return
        }
        audioSource = audioContext.createBufferSource()
        audioSource.buffer = decodedData
        audioSource.connect(audioContext.destination)
        audioSource.addEventListener('ended', () => {
          running.value = false
        })
        audioSource.start(0)
        running.value = true
      })
      voiceBar.value.click()
    })
    .catch((error) => {
      throw new Error(`解码音频文件出错:${error}`)
    })
}
onMounted(() => {})
</script>

<style lang="scss" scoped>
.voice-bar__box {
  @apply w-193px h-40px border-rd-[18px_31px_31px_0] px-15px py-0 bg-[#fff] text-[#000] text-14px shadow-[0_5px_20px_0_#00000012];

  i {
    @apply text-18px text-[#cbd5e1];
  }
}

.voice-bar__box--active {
  @apply bg-[#ffbb6a] text-[#fff];

  i {
    @apply I_text-[#fff];
  }
}

.voice-bar__dashed {
  @apply p-25px mt-25px border-1px border-dashed border-[#d6d6d6] font-RobotoSlab;
}

.voice-bar {
  .rotate {
    animation: rotate 2s linear infinite;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
