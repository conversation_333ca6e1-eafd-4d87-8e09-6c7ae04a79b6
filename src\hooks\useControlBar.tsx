// @unocss-include
import type { Ref } from 'vue'
import { encode } from 'base-64'
import { ElLoading } from 'element-plus'
import html2canvas from 'html2canvas'
import moment from 'moment'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import XLSX from 'xlsx-js-style'
import { copy } from '@/common'
import services from '@/common/axios'
import { notify } from '@/common/tools'
import ShareSelector from '@/components/ShareSelector.vue'
import { useDialog } from '@/hooks/useDialog'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useModalPosition } from '@/hooks/useModalPosition'
import { useShareChat } from '@/hooks/useShare'
import i18n from '@/i18n'
import { useAppStore } from '@/stores/app'
import { useChatStore } from '@/stores/chat'
import { SectionType } from '@/types/index'
import { mixpanel } from '../config/mixpanel'

const { shareContent } = useShareChat()
const { openDialog, closeDialog } = useDialog()
const appStore = useAppStore()
const { isPc, chatViewData } = storeToRefs(appStore)

interface Section {
  receiving: boolean
  createTime?: string
  date?: string
  content: string
  originalUrls?: string[]
}

interface DownloadOptions {
  messageId: number
  inviteCode: string | undefined
  type: 'PDF' | 'PNG' | 'TXT' | 'MARKDOWN' | string
  sections: Section[]
}

export function useControlBar() {
  const t = useGlobalI18n()
  const canvas: Ref<string> = ref('')
  const visible: Ref<boolean> = ref(false)
  const chatStore = useChatStore()

  function downloadTxt(string: string, filename: string): void {
    const blob = new Blob([string], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const downloadLink = document.createElement('a')
    downloadLink.href = url
    downloadLink.download = filename
    document.body.appendChild(downloadLink)
    downloadLink.click()
    URL.revokeObjectURL(url)
    document.body.removeChild(downloadLink)
  }

  function downloadMarkdown(string: string, filename: string): void {
    const blob = new Blob([string], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const downloadLink = document.createElement('a')
    downloadLink.href = url
    downloadLink.download = filename
    document.body.appendChild(downloadLink)
    downloadLink.click()
    URL.revokeObjectURL(url)
    document.body.removeChild(downloadLink)
  }

  async function downloadBase64(base64Data: string, fileName: string, fileType: string) {
    const response = await fetch(`data:${fileType};base64,${base64Data}`)
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    notify.closeAll()
    notify.info({
      title: t('headerMore.downloadSuccess').value,
    })
  }

  function removeJuchatsThinkTags(text: string) {
    return text.replace(/\s*<juchats-thinking>[\s\S]*?<\/juchats-thinking>/g, '')
  }

  async function download({ messageId, inviteCode, type, sections }: DownloadOptions): Promise<void> {
    const filename = `Juchats 对话 ${moment(new Date()).format('YYYYMMDDHHmmss')}`

    mixpanel.exportChat(type)
    const localeLang = localStorage.getItem('localeLang') || ''
    switch (type) {
      case 'EXCEL': {
        const tablesContainer = document.querySelector('.chat-view__content')
        const tables = tablesContainer?.querySelectorAll('table')
        if (!tables?.length) {
          notify.error({
            title: t('headerMore.noTable').value,
          })
          return
        }
        // 创建工作簿
        const wb = XLSX.utils.book_new()

        tables.forEach((table, index) => {
          const data: string[][] = []
          const rows = table.querySelectorAll('tr')

          rows.forEach((row) => {
            const rowData: string[] = []
            const cells = row.querySelectorAll('th, td')
            cells.forEach((cell) => {
              rowData.push(cell.textContent?.trim() || '')
            })
            data.push(rowData)
          })

          try {
            // 创建单个Sheet并添加数据
            const ws = XLSX.utils.aoa_to_sheet(data)

            // 设置列宽
            const colWidths = data[0].map(cell => ({
              wch: Math.min(50, Math.max(10, cell.length * 2)),
            }))
            ws['!cols'] = colWidths

            // 设置表头样式
            const headerStyle = {
              fill: { fgColor: { rgb: '000000' } },
              font: {
                name: '阿里巴巴普惠体 2.0 45 Light',
                sz: 12,
                color: { rgb: 'FFFFFF' },
              },
            }

            // 设置全局字体
            const globalStyle = {
              font: {
                name: '阿里巴巴普惠体 2.0 45 Light',
                sz: 12,
              },
            }

            if (!ws['!ref']) {
              throw new Error('工作表没有引用范围')
            }

            // 应用表头样式
            if (data.length > 0) {
              const range = XLSX.utils.decode_range(ws['!ref'] || '')
              for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C })
                if (!ws[cellAddress]) { continue }
                ws[cellAddress].s = headerStyle
              }
            }

            // 应用全局样式
            const range = XLSX.utils.decode_range(ws['!ref'] || '')
            for (let R = range.s.r; R <= range.e.r; ++R) {
              for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C })
                if (!ws[cellAddress]) {
                  ws[cellAddress] = { t: 's', v: '' }
                }
                if (!ws[cellAddress].s) {
                  ws[cellAddress].s = globalStyle
                }
                else {
                  ws[cellAddress].s = { ...ws[cellAddress].s, ...globalStyle.font }
                }
              }
            }

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, `Sheet${index + 1}`)
          }
          catch (error) {
            console.error('导出表格失败:', error)
            notify.error({
              title: t('headerMore.downloadError').value,
            })
          }
        })
        // 生成并下载文件
        XLSX.writeFile(wb, 'table.xlsx')
        break
      }
      case 'WORD': {
        const uri = `${import.meta.env.VITE_APP_IMAGE_URL || location.origin}/export?shareCode=${encode(
          `${messageId}_${inviteCode}`,
        )}`
        const response = await fetch(
          `${import.meta.env.VITE_APP_EXPORT_API}/api/app/file/export`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Jtoken': localStorage.token,
              'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
            },
            body: JSON.stringify({
              uri,
              mode: 'md2word',
              lang: i18n.global.locale.value,
            }),
          },
        )

        const contentType = response.headers.get('Content-Type')
        if (contentType === 'application/json') {
          const { msg } = await response.json()
          notify.error({
            title: msg,
          })
          return
        }

        if (!response.body) { return }

        const decoder = new TextDecoder('utf-8')
        let originBuffer = ''
        const transformStream = new TransformStream({
          transform(chunk, controller) {
            originBuffer += decoder.decode(chunk, { stream: true })
            let boundary = originBuffer.indexOf('\n\n')
            while (boundary !== -1) {
              const data = originBuffer.slice(0, boundary)
              originBuffer = originBuffer.slice(boundary + 2)
              boundary = originBuffer.indexOf('\n\n')
              try {
                const object = JSON.parse(data.slice(6))
                controller.enqueue(object)
              }
              catch { }
            }
          },
        })
        const outputStream = response.body.pipeThrough(transformStream)
        const reader = outputStream.getReader()
        let base64 = ''
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            downloadBase64(base64, `${filename}.docx`, 'application/docx')
            break
          }

          const { chunk, message } = value
          if (message) {
            notify.info({
              title: message,
              duration: 0,
            })
          }
          if (chunk) {
            base64 += chunk
          }
        }
        break
      }
      case 'PDF': {
        const uri = `${import.meta.env.VITE_APP_IMAGE_URL || location.origin}/export?shareCode=${encode(
          `${messageId}_${inviteCode}`,
        )}`
        const response = await fetch(
          `${import.meta.env.VITE_APP_EXPORT_API}/api/app/file/export`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Jtoken': localStorage.token,
              'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
            },
            body: JSON.stringify({
              uri,
              mode: 'pdf',
              lang: i18n.global.locale.value,
            }),
          },
        )

        const contentType = response.headers.get('Content-Type')
        if (contentType === 'application/json') {
          const { msg } = await response.json()
          notify.error({
            title: msg,
          })
          return
        }

        if (!response.body) { return }

        const decoder = new TextDecoder('utf-8')
        let originBuffer = ''
        const transformStream = new TransformStream({
          transform(chunk, controller) {
            originBuffer += decoder.decode(chunk, { stream: true })
            let boundary = originBuffer.indexOf('\n\n')
            while (boundary !== -1) {
              const data = originBuffer.slice(0, boundary)
              originBuffer = originBuffer.slice(boundary + 2)
              boundary = originBuffer.indexOf('\n\n')
              try {
                const object = JSON.parse(data.slice(6))
                controller.enqueue(object)
              }
              catch { }
            }
          },
        })
        const outputStream = response.body.pipeThrough(transformStream)
        const reader = outputStream.getReader()
        let base64 = ''
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            downloadBase64(base64, `${filename}.pdf`, 'application/pdf')
            break
          }

          const { chunk, message } = value
          if (message) {
            notify.info({
              title: message,
              duration: 0,
            })
          }
          if (chunk) {
            base64 += chunk
          }
        }
        break
      }
      case 'PNG': {
        const uri = `${import.meta.env.VITE_APP_IMAGE_URL || location.origin}/export?shareCode=${encode(
          `${messageId}_${inviteCode}`,
        )}`

        const response = await fetch(
          `${import.meta.env.VITE_APP_EXPORT_API}/api/app/file/export`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Jtoken': localStorage.token,
              'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
            },
            body: JSON.stringify({
              uri,
              mode: 'img',
              lang: i18n.global.locale.value,
            }),
          },
        )

        const contentType = response.headers.get('Content-Type')
        if (contentType === 'application/json') {
          const { msg } = await response.json()
          notify.error({
            title: msg,
          })
          return
        }

        if (!response.body) { return }

        const decoder = new TextDecoder('utf-8')
        let originBuffer = ''
        const transformStream = new TransformStream({
          transform(chunk, controller) {
            originBuffer += decoder.decode(chunk, { stream: true })
            let boundary = originBuffer.indexOf('\n\n')
            while (boundary !== -1) {
              const data = originBuffer.slice(0, boundary)
              originBuffer = originBuffer.slice(boundary + 2)
              boundary = originBuffer.indexOf('\n\n')
              try {
                const object = JSON.parse(data.slice(6))
                controller.enqueue(object)
              }
              catch { }
            }
          },
        })
        const outputStream = response.body.pipeThrough(transformStream)
        const reader = outputStream.getReader()
        let base64 = ''
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            downloadBase64(base64, `${filename}.png`, 'image/png')
            break
          }
          const { chunk, message } = value
          if (message) {
            notify.info({
              title: message,
              duration: 0,
            })
          }
          if (chunk) {
            base64 += chunk
          }
        }
        break
      }
      case 'TXT': {
        let content = ''
        sections.forEach((element) => {
          content = content + (element.receiving ? 'GPT：' : 'USER：')
          content = `${content}[${element.createTime
            ? moment(element.createTime).format('YYYY.MM.DD HH:mm')
            : moment(element.date).format('YYYY.MM.DD HH:mm')
          }]\n`
          content = `${content + element.content}\n`
        })
        downloadTxt(removeJuchatsThinkTags(content), `${filename}.txt`)
        break
      }
      case 'MARKDOWN': {
        let string = ''
        sections.forEach((element) => {
          string += `${element.date}\n\n`
          if (element.originalUrls && element.originalUrls.length && element.receiving) {
            element.originalUrls.forEach((url) => {
              string += `![${url}](${url})`
            })
          }
          else if (element.content && element.content.includes('HERMSTDUIO') && element.receiving) {
            string += `${element.content.split('HERMSTDUIO')[0]}\n\n`
            const { searchResult, searchResults } = JSON.parse(element.content.split('HERMSTDUIO')[1])
            const array = searchResult ? JSON.parse(searchResult) : searchResults
            array.forEach((element: { title: string, link: string }) => {
              string += `[${element.title}](${element.link})` + '\n\n'
            })
          }
          else {
            string += `${element.content}\n\n`
          }
        })
        downloadMarkdown(removeJuchatsThinkTags(string), `${filename}.md`)
        break
      }
    }
  }

  enum ShareType {
    Closed = 0,
    Chat = 1,
    Artifact = 2,
    Both = 3,
  }

  async function toShare(id: number, name: string): Promise<void> {
    const info = await services.post<{}, { type: ShareType, shareUrl: string }>('/gpt/getShareUrl', { id })
    // 为 null 表示从未开启过分享
    const shareType = info.type
    const shareLink = info.shareUrl
    const shared = shareType !== null && shareType !== ShareType.Closed

    const juchatsAnswer = chatViewData.value.sections.filter(item => item.nickName === 'Juchats')
    const isOnlyArtifact = juchatsAnswer.every(item => item.toolsType === SectionType.Artifact)
    const isOnlyChat = juchatsAnswer.every(item => Boolean(item.toolsType) === false)
    const { modalStyle } = useModalPosition()

    const openChat = shareType === ShareType.Chat || shareType === ShareType.Both || (isOnlyChat && shareType === null)
    const openArtifact = shareType === ShareType.Artifact || shareType === ShareType.Both || (isOnlyArtifact && shareType === null)

    openDialog({
      titleContent: i18n.global.t('sideBar.shareSelectorDialog'),
      style: modalStyle.value,
      content: (
        <ShareSelector
          shared={shared}
          defaultOpenChat={openChat}
          defaultOpenArtifact={openArtifact}
          disabledChat={isOnlyArtifact}
          disabledArtifact={isOnlyChat}
          onChange={({ shareChat, shareArtifact }) => {
            const type = (() => {
              if (shareArtifact && shareChat) { return ShareType.Both }
              if (shareArtifact) { return ShareType.Artifact }
              if (shareChat) { return ShareType.Chat }
              return ShareType.Closed
            })()

            const shareCode = chatStore.getShareCodeById(id)

            if (shareCode) {
              services.post('/gpt/getShareUrl', {
                id,
                type,
              })
            }
            else {
              notify.error({
                title: 'Save failed',
              })
            }
          }}
          onCreate={async ({ shareChat, shareArtifact }) => {
            const type = (() => {
              if (shareArtifact && shareChat) { return ShareType.Both }
              if (shareArtifact) { return ShareType.Artifact }
              if (shareChat) { return ShareType.Chat }
              return ShareType.Closed
            })()

            const shareCode = chatStore.getShareCodeById(id)

            if (shareCode) {
              if (shared) {
                copy(shareLink, {
                  copySuccess: t('tipMessage.copyLinkSuccess').value,
                })
                closeDialog()
              }
              else {
                const res = await services.post<{}, { shareUrl: string }>('/gpt/getShareUrl', {
                  id,
                  type,
                })
                copy(res.shareUrl, {
                  copySuccess: t('tipMessage.copyLinkSuccess').value,
                })
                closeDialog()
              }
            }
            else {
              notify.error({
                title: 'Chat not found',
              })
            }
          }}
        />
      ),
      showClose: false,
      showFooter: false,
    })
  }

  return {
    canvas,
    visible,
    download,
    toShare,
  }
}
