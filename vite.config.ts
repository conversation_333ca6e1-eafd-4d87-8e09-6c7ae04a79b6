import process from 'node:process'
import { fileURLToPath, URL } from 'node:url'
import { sentryVitePlugin } from '@sentry/vite-plugin'
// import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'
import VueDevTools from 'vite-plugin-vue-devtools'
import { injectManifest } from './plugin/inject-manifest'
import { injectScript } from './plugin/inject-script'

// https://vitejs.dev/config/
export default async ({ mode }: { mode: string }) => {
  const DEV = mode === 'development'
  const env = loadEnv(mode, process.cwd())

  return defineConfig({
    plugins: [
      injectManifest(mode),
      DEV ? {} : injectScript,
      sentryVitePlugin({
        org: 'hermstudio',
        project: 'juchats',
        disable: mode !== 'test',
        authToken: env.VITE_SENTRY_AUTH_TOKEN,
      }),
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag: string) => tag === 'marquee',
          },
        },
      }),
      vueJsx(),
      UnoCSS(),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
        ],
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
        dts: 'src/typings/auto-imports.d.ts',
        resolvers: [ElementPlusResolver()],
        dirs: [
          'src/hooks/**',
          'src/store',
        ],
      }),
      Components({
        dirs: ['./src/components'],
        resolvers: [ElementPlusResolver()],
        extensions: ['vue'],
        dts: 'src/typings/components.d.ts',
      }),
      VueDevTools({
        launchEditor: 'cursor',
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
          @use "sass:color";
          @import "@/assets/styles/common.scss";
          `,
        },
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        'vue': 'vue/dist/vue.esm-bundler.js',
      },
    },
    build: {
      sourcemap: true,
      // 使用terser压缩
      target: 'es2020',
      minify: 'terser',
      terserOptions: {
        ecma: 2020,
        compress: {
          drop_console: mode === 'production',
          drop_debugger: true,
        },
      },
    },
    define: {
      global: {
        postMessage: () => { },
      },
    },
    server: {
      host: '0.0.0.0',
      proxy: {
        '/api': {
          target: 'https://dev.hermchats.agency',
          changeOrigin: true,
        },
        '/gw/chatweb': {
          target: 'https://dev.hermchats.agency/gw/chatweb',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/gw\/chatweb/, ''),
        },
        '/gw/chatgpt': {
          target: 'https://dev.hermchats.agency/gw/chatgpt',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/gw\/chatgpt/, ''),
        },
        '/gw/chatpay': {
          target: 'https://dev.hermchats.agency/gw/chatpay',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/gw\/chatpay/, ''),
        },
      },
    },
  })
}
