import { test, expect } from 'vitest'
import { citationExtension } from './marked-citation'
import { marked } from 'marked'

test('marked citation', () => {
  const mockContent = `我将为您搜索杭州最新的天气信息。

嘿嘿 <cite>{"i":1}</cite><cite>{"i":1}</cite>。

根据最新天气信息，为您整理杭州天气情况：

### 实时天气
- 当前温度：20°C
- 天气状况：晴
- 相对湿度：54%
- 风向风力：南风3级 <cite>{"i":5}</cite>

### 今日天气（5月12日）
- 温度范围：18-30°C
- 天气状况：晴
- 风力：小于3级 <cite>{"i":1}</cite>

### 未来天气预报
1. 5月13日（明天）
   - 温度：20-32°C
   - 天气：多云转晴
   - 风力：小于3级 <cite>{"i":1}</cite>

2. 5月14日（后天）
   - 温度：23-33°C
   - 天气：晴转多云
   - 风力：小于3级 <cite>{"i":1}</cite>

3. 5月15日（周四）
   - 温度：20-28°C
   - 天气：多云转中雨
   - 风力：小于3级 <cite>{"i":1}</cite>

### 生活指数
- 穿衣指数：炎热，建议穿短衫、短裤等清凉夏季服装
- 紫外线指数：很强，建议涂擦SPF20以上，PA++护肤品，避强光
- 运动指数：较适宜，请适当减少运动时间，降低运动强度
- 洗车指数：较适宜，无雨且风力较小，易保持清洁度 <cite>{"i":5}</cite>`

  const input = marked.use({ extensions: [citationExtension()] }).parse(mockContent)
  expect(input).toMatchInlineSnapshot(`
    "<p>我将为您搜索杭州最新的天气信息。</p>
    <p>嘿嘿 <span class="citation">1</span><span class="citation">1</span>。</p>
    <p>根据最新天气信息，为您整理杭州天气情况：</p>
    <h3>实时天气</h3>
    <ul>
    <li>当前温度：20°C</li>
    <li>天气状况：晴</li>
    <li>相对湿度：54%</li>
    <li>风向风力：南风3级 <span class="citation">5</span></li>
    </ul>
    <h3>今日天气（5月12日）</h3>
    <ul>
    <li>温度范围：18-30°C</li>
    <li>天气状况：晴</li>
    <li>风力：小于3级 <span class="citation">1</span></li>
    </ul>
    <h3>未来天气预报</h3>
    <ol>
    <li><p>5月13日（明天）</p>
    <ul>
    <li>温度：20-32°C</li>
    <li>天气：多云转晴</li>
    <li>风力：小于3级 <span class="citation">1</span></li>
    </ul>
    </li>
    <li><p>5月14日（后天）</p>
    <ul>
    <li>温度：23-33°C</li>
    <li>天气：晴转多云</li>
    <li>风力：小于3级 <span class="citation">1</span></li>
    </ul>
    </li>
    <li><p>5月15日（周四）</p>
    <ul>
    <li>温度：20-28°C</li>
    <li>天气：多云转中雨</li>
    <li>风力：小于3级 <span class="citation">1</span></li>
    </ul>
    </li>
    </ol>
    <h3>生活指数</h3>
    <ul>
    <li>穿衣指数：炎热，建议穿短衫、短裤等清凉夏季服装</li>
    <li>紫外线指数：很强，建议涂擦SPF20以上，PA++护肤品，避强光</li>
    <li>运动指数：较适宜，请适当减少运动时间，降低运动强度</li>
    <li>洗车指数：较适宜，无雨且风力较小，易保持清洁度 <span class="citation">5</span></li>
    </ul>
    "
  `)
})
