<template>
  <ResizablePanel
    v-model:visible="visible" v-model:full-visible="fullVisible" v-model:active-tab="tab"
    :tabs="panelTabs" :is-share-page="false" background-color="#F9FAFC" :full-screenable="false"
  >
    <template #content>
      <section v-if="tab === 'browsing'" class="panel-content">
        <div v-if="loading">
          <ElSkeleton>
            <template #template>
              <div v-for="index in 10" :key="index" class="mb-[50px] flex">
                <div class="shrink">
                  <el-skeleton-item variant="circle" />
                </div>
                <ElSkeleton class="ml-[20px]" :rows="2" animated />
              </div>
            </template>
          </ElSkeleton>
        </div>
        <template v-else>
          <div class="header">
            <div class="title">Search Results</div>
            <div class="flex flex-wrap justify-end gap-[10px]">
              <div class="border border-#ECF0F5 rounded-full px-20px text-12px leading-36px font-Lexend">
                {{
                  data.searchResults.length }} Web Pages
              </div>
              <div
                v-if="twitters.length > 0"
                class="border border-#ECF0F5 rounded-full px-20px text-12px leading-36px font-Lexend"
              >
                {{
                  twitters.length }} 𝕏 Post
              </div>
            </div>
          </div>
          <div class="flex flex-col gap-[35px]">
            <div
              v-for="(item, i) in data.searchResults" :key="i" class="group relative cursor-pointer"
              @click="open(item.link)"
            >
              <div
                class="absolute left-50% top-50% h-[calc(100%+40px)] w-[calc(100%+40px)] translate-x-[-50%] translate-y-[-50%] rounded-10px transition-all duration-200 group-hover:bg-[var(--s)]"
              >
              </div>
              <div class="relative mb-[8px] text-[14px] font-medium leading-[18px] font-Lexend">{{ item.title }}</div>
              <div v-if="item.snippet" class="relative mb-[11px] text-[13px]">
                {{
                  item.snippet }}
              </div>
              <div class="relative flex items-center justify-between">
                <div class="flex items-center text-[12px] font-bold font-Inter border-0!">
                  <div class="mr-[6px] w-[18px] flex items-center justify-center">
                    <img
                      class="w-[100%] rounded-full"
                      :src="`//s2.googleusercontent.com/s2/favicons?domain=${item.link.toString()}`" alt=""
                    >
                  </div>
                  <span class="text-[12px] text-[#727272] font-normal font-Lexend">{{ item.host }}</span>
                </div>
                <div
                  class="flex items-center gap-[8px] opacity-0 transition-opacity duration-200 group-hover:opacity-100"
                >
                  <svg
                    width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg"
                    class="icon-group cursor-pointer"
                    @click="$emit('relatedLink', { title: item.title, link: item.link })"
                  >
                    <g id="Juchats-Dev-Browing" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="Website-Index-Browsing" transform="translate(-1846, -290)" stroke-width="1.5">
                        <g id="Copy-Text" transform="translate(1847, 291)">
                          <path
                            id="路径"
                            d="M0,8 C0,4.53353846 0,2.80036923 0.939803077,1.62190769 C1.14091077,1.36972308 1.36972308,1.14091077 1.62190769,0.939803077 C2.80036923,0 4.53353846,0 8,0 C11.4664615,0 13.1996308,0 14.3780923,0.939803077 C14.6302769,1.14091077 14.8590769,1.36972308 15.0601846,1.62190769 C16,2.80036923 16,4.53353846 16,8 C16,11.4664615 16,13.1996308 15.0601846,14.3780923 C14.8590769,14.6302769 14.6302769,14.8590769 14.3780923,15.0601846 C13.1996308,16 11.4664615,16 8,16 C4.53353846,16 2.80036923,16 1.62190769,15.0601846 C1.36972308,14.8590769 1.14091077,14.6302769 0.939803077,14.3780923 C0,13.1996308 0,11.4664615 0,8 Z"
                          ></path>
                          <g id="编组" transform="translate(4, 6)" stroke-linecap="round">
                            <path
                              id="形状"
                              d="M3.12384918,0.977006392 C3.12384918,0.291326392 3.12384918,-0.0515099716 2.8977619,-0.264528153 C2.67166736,-0.477539062 2.30777645,-0.477539062 1.57999463,-0.477539062 C0.852212813,-0.477539062 0.488321903,-0.477539062 0.262227358,-0.264528153 C0.0361328125,-0.0515099716 0.0361328125,0.291326392 0.0361328125,0.977006392 C0.0361328125,1.66268639 0.0361328125,2.00552276 0.262227358,2.21854094 C0.488321903,2.43155185 0.852212813,2.43155185 1.57999463,2.43155185 C2.30777645,2.43155185 2.67166736,2.43155185 2.8977619,2.21854094 C3.12384918,2.00552276 3.12384918,1.66268639 3.12384918,0.977006392 Z M3.12384918,0.977006392 L3.12384918,2.48150094 C3.12384918,3.81493366 2.15287463,4.94579185 0.808060085,5.34064276 M8.03613281,0.977006392 C8.03613281,0.291326392 8.03613281,-0.0515099716 7.81002372,-0.264528153 C7.58391463,-0.477539062 7.22006009,-0.477539062 6.49227827,-0.477539062 C5.76448918,-0.477539062 5.40059827,-0.477539062 5.17450372,-0.264528153 C4.94841645,-0.0515099716 4.94841645,0.291326392 4.94841645,0.977006392 C4.94841645,1.66268639 4.94841645,2.00552276 5.17450372,2.21854094 C5.40059827,2.43155185 5.76448918,2.43155185 6.49227827,2.43155185 C7.22006009,2.43155185 7.58391463,2.43155185 7.81002372,2.21854094 C8.03613281,2.00552276 8.03613281,1.66268639 8.03613281,0.977006392 Z M8.03613281,0.977006392 L8.03613281,2.48150094 C8.03613281,3.81493366 7.06515099,4.94579185 5.72034372,5.34064276"
                            ></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </template>
      </section>
      <section v-else class="panel-content">
        <div class="header">
          <div class="title">Related</div>
        </div>
        <ul grid grid-cols-1 gap-20px>
          <li v-for="(item, i) in data.relatedSearchQueries" :key="i" cursor-pointer @click="$emit('related', item)">
            <span mr-10px h-7px w-7px inline-flex rounded-full bg-black dark:bg-white />
            <span font-Inter-medium text-13px leading-25px>{{ item }}</span>
          </li>
        </ul>
      </section>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { ElSkeleton } from 'element-plus'
import { useAppStore } from '@/stores/app'

defineEmits<{
  (e: 'related', value: string): void
  (e: 'relatedLink', value: { title: string, link: string }): void
}>()
const appStore = useAppStore()
const {
  isClient,
} = storeToRefs(appStore)

const visible = defineModel<boolean>('visible', {
  default: false,
})

const data = defineModel<{
  searchResults: {
    host: string
    title: string
    link: string
    snippet: string
    position: number
    date?: undefined
  }[]
  relatedSearchQueries: string[]
}>('data', { required: true, default: [] })

const tab = ref<'browsing' | 'related'>('browsing')
const fullVisible = ref(false)
const loading = ref(true)

const panelTabs = [
  { key: 'browsing', label: 'Browsing' },
  { key: 'related', label: 'Related' },
]

const open = (link: string) => {
  if (isClient.value && window.__TAURI__?.shell) {
    window.__TAURI__.shell.open(link)
  }
  else {
    window.open(link, '_blank', 'noreferrer,noopener')
  }
}

const twitters = computed(() => {
  return data.value.searchResults.filter((item) => {
    return ['x.com', 'twitter.com'].includes(item.host)
  })
})

const loadingTime = 1000
let timer: NodeJS.Timeout | null = null
watch(() => visible.value, (open) => {
  timer && clearTimeout(timer)
  if (open) {
    loading.value = true
    timer = setTimeout(() => {
      loading.value = false
    }, loadingTime)
  }
})
</script>

<style lang="scss" scoped>
.panel-content {
  @apply bg-[#f9fafc] dark:bg-black;
  @apply py-[28px] px-[45px];

  .header {
    @apply mb-[28px] flex justify-between items-center;

    .title {
      @apply text-[20px] font-Lexend;
    }
  }
}

.icon-group {
  path {
    @apply stroke-[#A8AFBF];
  }

  &:hover {
    path {
      @apply stroke-[#000000] dark:stroke-[#FFFFFF];
    }
  }
}
</style>
