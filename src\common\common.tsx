import { createApp } from 'vue'
import Mermaid from '@/components/Mermaid.vue'
import SmilesRenderer from '@/components/SmilesRenderer.vue'
import { useAppStore } from '../stores/app'
import { svgText } from './svgText'

const appInstance = new Map<Element, {
  app: any
  renderContainer: HTMLDivElement
}>()

// @ts-expect-error 忽略 window.renderMermaid 的类型错误
window.renderMermaid = function () {
  const renderButton = this as unknown as HTMLDivElement
  const rendered = renderButton.classList.toggle('rendered')
  const code = `${decodeURIComponent(renderButton.dataset.code!)}`

  if (renderButton) {
    const codeContainer = renderButton.closest('.language-mermaid')
    if (codeContainer) {
      let renderContainer: HTMLDivElement | undefined
      const codeWrapper = codeContainer.querySelector('.code-wrapper') as HTMLDivElement
      const toggleWrapBtn = codeContainer.querySelector('.toggle-wrap-btn') as HTMLButtonElement

      if (rendered) {
        codeWrapper.style.display = 'none'
        // 如果正在渲染，则隐藏代码换行按钮
        if (toggleWrapBtn) {
          toggleWrapBtn.style.display = 'none'
        }
        renderContainer = document.createElement('div')
        codeContainer.appendChild(renderContainer)
        const app = createApp(Mermaid, { code })
        appInstance.set(codeContainer, { app, renderContainer })
        app.mount(renderContainer)
      }
      else {
        codeWrapper.style.display = 'inline-block'
        if (toggleWrapBtn) {
          toggleWrapBtn.style.display = 'flex'
        }
        const app = appInstance.get(codeContainer)
        app && app.app.unmount()
        app && app.renderContainer.remove()
      }
    }
    else {
      console.error(`codeContainer not found`)
    }
  }
  else {
    console.error(`renderButton not found`)
  }
}

// @ts-expect-error 忽略 window.renderSmiles 的类型错误
window.renderSmiles = function () {
  const renderButton = this as unknown as HTMLDivElement
  const rendered = renderButton.classList.toggle('rendered')
  const code = `${decodeURIComponent(renderButton.dataset.code!)}`

  if (renderButton) {
    const codeContainer = renderButton.closest('.language-smiles')
    if (codeContainer) {
      let renderContainer: HTMLDivElement | undefined
      const codeWrapper = codeContainer.querySelector('.code-wrapper') as HTMLDivElement
      const toggleWrapBtn = codeContainer.querySelector('.toggle-wrap-btn') as HTMLButtonElement

      if (rendered) {
        codeWrapper.style.display = 'none'
        // 如果正在渲染，则隐藏代码换行按钮
        if (toggleWrapBtn) {
          toggleWrapBtn.style.display = 'none'
        }
        renderContainer = document.createElement('div')
        codeContainer.appendChild(renderContainer)
        const app = createApp(SmilesRenderer, { smiles: code })
        appInstance.set(codeContainer, { app, renderContainer })
        app.mount(renderContainer)
      }
      else {
        codeWrapper.style.display = 'inline-block'
        if (toggleWrapBtn) {
          toggleWrapBtn.style.display = 'flex'
        }
        const app = appInstance.get(codeContainer)
        app && app.app.unmount()
        app && app.renderContainer.remove()
      }
    }
    else {
      console.error(`codeContainer not found for smiles`)
    }
  }
  else {
    console.error(`renderButton not found for smiles`)
  }
}

export const collapseBtnMarkup = `<div class='collapse-btn pl-12px pr-12px h-30px rounded-full border-1px border-[var(--artifacts-code-border)] flex items-center justify-center absolute top-[-48px] left-0 cursor-pointer select-none'
    onclick='this.classList.toggle("collapse-thinking");'>
    <span class='mr-5px font-Lexend text-[12px] leading-18px'>Thinking</span>
    <span class='font-Lexend text-12px mt-[2px] mr-5px thinking-time'></span>
    <svg width="6px" height="3.66667714px" viewBox="0 0 6 3.66667714" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="Juchats-Dev-Thinking" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Website-Index-Model-Thinking" transform="translate(-840, -296)" fill="#384A67" fill-rule="nonzero">
                <g id="编组-6" transform="translate(748, 282)">
                    <polygon id="路径" transform="translate(95, 15.8333) scale(1, -1) translate(-95, -15.8333)" points="94.9999906 16.3333134 97.3333417 14 98 14.666663 94.9999906 17.6666771 92 14.666663 92.6666677 14"></polygon>
                </g>
            </g>
        </g>
    </svg>
</div>`

export function generateCodeTitleMarkup(lang: string, code: string) {
  const { thinkingTimeFunctional } = useAppStore()

  if (lang === 'thinking' && thinkingTimeFunctional) {
    return collapseBtnMarkup
  }

  // 如果是 mermaid 语言，添加一个渲染按钮到右下角
  let renderButton = ''
  if (lang === 'mermaid') {
    renderButton = `<div
        class="flex items-center cursor-pointer render-mermaid absolute bottom-3 right-3 z-10"
        data-code="${encodeURIComponent(code)}"
        onclick="renderMermaid.call(this)"
      >
        <div class="mermaid-toggle-button font-Lexend">
          <div class="active-indicator"></div>
          <div class="segment">Code</div>
          <div class="segment">Preview</div>
        </div>
      </div>`
  }
  else if (lang === 'smiles') {
    renderButton = `<div
        class="flex items-center cursor-pointer render-smiles absolute bottom-3 right-3 z-10"
        data-code="${encodeURIComponent(code)}"
        onclick="renderSmiles.call(this)"
      >
        <div class="mermaid-toggle-button font-Lexend">
          <div class="active-indicator"></div>
          <div class="segment">Code</div>
          <div class="segment">Preview</div>
        </div>
      </div>`
  }

  return `<div class="code-title flex items-center relative">
  <div class="grow flex-y-c lang-text">
    <span>${lang || 'plaintext'}</span>

    <button
      class="expand-and-collapse"
      onclick="this.classList.toggle('rotate--90'); this.closest('.hljs').classList.toggle('code-block-collapsed');"
    >
      ${svgText.expand}
    </button>
  </div>
  <div class="hljs__action">
    <div
      class="flex items-center cursor-pointer"
      onclick="this.closest('.hljs')?.classList.toggle('code-height-max')"
    >
      <div class="show-more-icon flex-c size-34px">
        ${svgText.showMore}
      </div>
    </div>
    <div
      class="flex items-center cursor-pointer toggle-wrap-btn"
      onclick="window.toggleCodeWrap(this)"
    >
      <div class="toggle-wrap-icon flex-c">
        ${svgText.codeWrap}
      </div>
    </div>
    ${''/* 移除了 mermaid 渲染按钮，将在外部添加 */}
    
    <div
      class="flex items-center hljs__copy__btn cursor-pointer copy-btn"
      value="${encodeURIComponent(code)}"
    >
      <div class="copy-icon flex-c">
        ${svgText.copy}
      </div>
    </div>
  </div>
</div>${renderButton}<div class="code_shadow"></div>`
}

export function escapeKatex(text: string) {
  const regDouble = /\$\$[^\n`](.+?)\$\$/g
  // `/\$[^$\n\d`]([^$]+)\$/g` 改为 `/\$([^$\n`]+)\$/g`，移除了对 `$` 后第一个字符的特殊限制
  const regSingle = /\$([^$\n`]+)\$/g
  const codeBlock = /```[\s\S]*?```/g
  const clodeInline = /`.*?`/g
  const strong = /\*\*(.*?)\*\*/g

  const originalCodeBlock = text.match(codeBlock)
  const originalCodeInline = text.match(clodeInline)
  const originalStrong = text.match(strong)

  // 把包含公式的 * *  去掉加粗，因为无效
  if (originalStrong) {
    originalStrong.forEach((item) => {
      if (item.includes('\(') || item.includes('\[') || item.includes('\&')) {
        text = text.replaceAll(item, item.replaceAll('\*\*', ''))
      }
    })
  }

  text = text
    .replaceAll(regDouble, ` \$ $1 \$`)
    .replaceAll(regSingle, ` \$ $1 \$ `)
    .replaceAll('\\[', '\n\$\$\$\n')
    .replaceAll('\\]', '\n\$\$\$\n')
    .replaceAll('\\(', ' \$\$')
    .replaceAll('\\)', '\$\$ ')

  const newCodeBlock = text.match(codeBlock)
  const newCodeInline = text.match(clodeInline)

  if (originalCodeBlock && newCodeBlock) { text = replaceCode(originalCodeBlock, newCodeBlock, text) }

  if (originalCodeInline && newCodeInline) { text = replaceCode(originalCodeInline, newCodeInline, text) }
  return text
}

function replaceCode(originalCodeArr: RegExpMatchArray, newCodeArr: RegExpMatchArray, sourceText: string) {
  for (let i = 0; i < newCodeArr.length; i++) {
    if (originalCodeArr[i].includes('$')) {
      originalCodeArr[i] = originalCodeArr[i].replaceAll('$', '$$$')
    }
    sourceText = sourceText.replaceAll(newCodeArr[i], originalCodeArr[i])
  }
  return sourceText
}

export function isVideo(url: string): boolean {
  try {
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'flv', 'm4v', 'wmv']
    // 移除 URL 中的查询参数
    const urlWithoutQuery = url.split('?')[0]
    // 获取文件扩展名并转换为小写
    const ext = urlWithoutQuery.split('.').pop() ?? ''
    return videoExtensions.includes(ext.toLowerCase())
  }
  catch {
    return false
  }
}

/**
 * 获取资源URL的文件扩展名
 * @param url 资源URL
 * @returns 小写的文件扩展名（不包含点号），如果没有扩展名则返回空字符串
 */
export function getFileExtension(url: string): string {
  try {
    // 移除 URL 中的查询参数
    const urlWithoutQuery = url.split('?')[0]
    // 获取文件扩展名并转换为小写
    const ext = urlWithoutQuery.split('.').pop() ?? ''
    return ext.toLowerCase()
  }
  catch {
    return ''
  }
}
