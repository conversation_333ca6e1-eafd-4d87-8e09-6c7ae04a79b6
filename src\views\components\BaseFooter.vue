<template>
  <div class="footer absolute w-full flex items-center justify-between lt-xl:justify-center">
    <div class="f-left flex items-center lt-md:(block space-y-8px)">
      <div class="copy flex items-center lt-md:!mr-0">
        <span class="grey">&copy;</span>
        <div class="studio">HERM STUDIO</div>
        <span class="grey">//</span>
        <span>JUCHATS {{ new Date().getFullYear() }}</span>
      </div>
      <div class="terms lt-md:text-center">
        <a href="javascript:;" class="link-visual" @click="showProtocol('TERMS')">{{ $t("baseFooter.terms") }}</a>
        <span class="grey">//</span>
        <a href="javascript:;" class="link-visual" @click="showProtocol('PRIVACY')">{{ $t("baseFooter.privacyPolicy") }}</a>
      </div>
      <div class="client flex items-center lt-md:hidden">
        <span class="grey">//</span>
        <span style="margin-left: 0">{{ $t("baseFooter.client") }}</span>
        <i class="i-ri-apple-fill"></i>
        <i class="i-ri-android-fill"></i>
        <i class="i-ri-windows-fill"></i>
        <span>{{ $t("baseFooter.comingSoon") }}</span>
        <span class="star">✨</span>
      </div>
    </div>
    <div class="f-right flex items-center lt-xl:hidden">
      <span class="connecting-text">{{ $t("baseFooter.connecting") }}</span>
      <span><img src="@/assets/images/connections.png" alt="" /></span>
    </div>
  </div>
  <CustomDialog v-model:visible="visibleProtocol" :key-control="!isProtocolPage" :show-cancel="!isProtocolPage">
    <div v-if="type == 'TERMS'" class="c-scrollbar protocol-box" v-html="termsHTML"></div>
    <div v-if="type == 'PRIVACY'" class="c-scrollbar protocol-box" v-html="privacyHTML"></div>
  </CustomDialog>
</template>

<script setup lang="ts">
import CustomDialog from '@/components/CustomDialog.vue'
import { lowerCase } from 'lodash'
import { marked } from 'marked'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const emit = defineEmits<{
  (e: 'toggleProtocol', showProtocol: boolean): void
}>()
const route = useRoute()
const visibleProtocol = ref(false)
const privacyHTML = ref('')
const termsHTML = ref('')
const type = ref('TERMS')

onMounted(() => {
  init()
  window.addEventListener('popstate', () => {
    if (document.location.pathname === '/') {
      visibleProtocol.value = false
    }
  })
})

function init() {
  if (route.name === 'Terms') {
    showProtocol('TERMS')
  }
  else if (route.name === 'Privacy') {
    showProtocol('PRIVACY')
  }
}

const isProtocolPage = computed(() => {
  return route.name === 'Terms' || route.name === 'Privacy'
})

watch(visibleProtocol, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    emit('toggleProtocol', newVal)
    if (!newVal) {
      history.back()
    }
  }
})

function showProtocol(showType = 'TERMS') {
  type.value = showType
  visibleProtocol.value = true
  history.pushState({}, '', `/${lowerCase(showType)}`)
  if (
    (type.value === 'TERMS' && termsHTML.value !== '')
    || (type.value === 'PRIVACY' && privacyHTML.value !== '')
  ) {
    return
  }
  getMD()
}

function getMD() {
  const path = type.value === 'TERMS' ? '/static/terms_of_use.md' : '/static/privacy_policy.md'

  fetch(path)
    .then(response => response.text())
    .then((markdownContent) => {
      const html = marked(markdownContent)
      if (type.value == 'TERMS') {
        termsHTML.value = html
        return
      }
      if (type.value == 'PRIVACY') {
        privacyHTML.value = html
      }
    })
    .catch((error) => {
      console.error('Error fetching the Markdown file:', error)
    })
}
</script>

<style lang="scss">
.f-left {
  line-height: 1;
}

.client {
  > * {
    margin: 0 4px;
  }

  i {
    font-size: 16px;
    transition: 0.3s;

    &.i-ri-apple-fill {
      margin-top: -3px;
    }

    &.i-ri-android-fill {
      font-size: 15px;
      margin-top: -1px;
    }

    &.i-ri-windows-fill {
      font-size: 17px;
    }

    &:hover {
      opacity: 1;
    }
  }

  i,
  span:not(.star) {
    opacity: 0.3;
  }
}

.protocol-box {
  height: 70vh;
  max-width: 1500px;
  overflow: auto;

  a {
    color: #fff;
    border-bottom: 1px dashed white;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  ul,
  ol {
    padding-left: 2em;
  }

  h1 {
    text-align: center;
    font-size: 48px;
    font-weight: bold;
    font-style: italic;
    padding: 30px 0;
  }

  h2 {
    font-size: 18px;
    font-weight: 600;
    padding: 15px 0;
  }

  p {
    color: #bdbdbd;
    line-height: 1.4em;
    margin: 10px 0;
  }
}
</style>

<style lang="scss" scoped>
.footer {
  left: 0;
  bottom: 0;
  padding: 40px;
  border-top: 1px solid rgba(white, 0.2);
  font-size: 14px;

  a {
    transition: 0.3s;
    color: var(--black);

    &:hover {
      text-shadow: var(--black) 1px 0 10px;
    }
  }

  .studio {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      display: block;
      width: 8px;
      height: 8px;
      background-color: white;
      border-radius: 50%;
      right: 1px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .connecting-text {
    margin-right: 15px;
    opacity: 0.3;
  }

  .copy {
    margin-right: 30px;
  }

  .grey {
    opacity: 0.3;
    margin: 0 5px;
  }

  .f-right {
    img {
      height: 22px;
    }
  }
}
</style>
