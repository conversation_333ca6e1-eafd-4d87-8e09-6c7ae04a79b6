import type { PluginOption } from 'vite'

export const injectManifest: (mode: string) => PluginOption = (mode) => {
  const devENV = mode === 'development' || mode === 'test'
  const href = devENV ? '/manifest.dev.json' : '/manifest.json'

  return {
    name: 'inject-script',
    transformIndexHtml(html) {
      return html.replace('</head>', `<link rel="manifest" href="${href}"></head>`);
    },
  }
}
