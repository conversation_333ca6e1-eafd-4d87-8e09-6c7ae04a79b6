<template>
  <div class="max-w-[766px] w-full">
    <div class="inline-flex flex-wrap justify-center gap-4">
      <SimpleButton
        v-for="(item, index) in items"
        :key="index"
        :style="{
          'animation-delay': `${index * 0.1}s`,
          '--animate-duration': '0.5s',
          'animation-timing-function': 'var(--ani-bezier)',
        }"
        @click="onHandleClick(item)"
      >
        <i class="mr-[4px]" :class="item.icon" :style="{ fontSize: `${item.size}px` }"></i>
        <span class="align-middle text-[12px] leading-[42px]">{{ item.label }}</span>
      </SimpleButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SendMessageParams } from '@/stores/chat'
import { mixpanel } from '@/config/mixpanel'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useAppStore } from '@/stores/app'
import { useChatStore } from '@/stores/chat'

const t = useGlobalI18n()
const chatStore = useChatStore()

interface PromptItem {
  icon: string
  label: ComputedRef<string>
  prompt: ComputedRef<string>
  name: string
  size: number
  attachments?: Array<{
    fileId: string
    fileName: string
    fileType: string
  }>
}

const items: PromptItem[] = [
  {
    icon: 'i-ri-twitter-x-line mb-[1px]',
    label: t('guildPrompt.summaryTweets'),
    prompt: t('guildPrompt.summaryTweetsPrompt'),
    name: 'Summary Tweets',
    size: 14,
  },
  {
    icon: 'i-ri-apple-line mb-[3px]',
    label: t('guildPrompt.appleFinancial'),
    prompt: t('guildPrompt.appleFinancialPrompt'),
    name: 'Apple Financial',
    size: 16,
  },
  {
    icon: 'i-ri-apps-2-ai-line',
    label: t('guildPrompt.mermaid'),
    prompt: t('guildPrompt.mermaidPrompt'),
    name: 'Mermaid',
    size: 16,
  },
  {
    icon: 'i-ri-image-circle-ai-line',
    label: t('guildPrompt.createImage'),
    prompt: t('guildPrompt.createImagePrompt'),
    name: 'Create Image',
    size: 15,
  },
  {
    icon: 'i-ri-map-pin-3-line',
    label: t('guildPrompt.travelPlan'),
    prompt: t('guildPrompt.travelPlanPrompt'),
    name: 'Travel Plan',
    size: 14.67,
  },
  {
    icon: 'i-ri-file-pdf-2-line',
    label: t('guildPrompt.documentInterpretation'),
    prompt: t('guildPrompt.documentInterpretationPrompt'),
    name: 'Document Interpretation',
    size: 16,
    attachments: [
      {
        fileId: 'U2FsdGVkX194ils7IiAzYjglO8C3fdOz8jv1FTlZyKPkffR0CLDtcc3Q0IeXVcMzXI4Q3Y3VnHC2i4JSIdl3yQ==',
        fileName: '底层逻辑.pdf',
        fileType: 'TEXT',
      },
    ],
  },
  {
    icon: 'i-ri-link-m',
    label: t('guildPrompt.summaryLink'),
    prompt: t('guildPrompt.summaryLinkPrompt'),
    name: 'Summary Link',
    size: 17,
  },
  // {
  //   icon: 'i-ri-profile-line',
  //   label: t('guildPrompt.mixedMode'),
  //   prompt: t('guildPrompt.mixedModePrompt'),
  //   name: 'Mixed Mode',
  //   size: 16,
  // },
  {
    icon: 'i-ri-page-separator',
    label: t('guildPrompt.twitterThread'),
    prompt: t('guildPrompt.twitterThreadPrompt'),
    name: 'Twitter Thread',
    size: 16,
  },
]
const { modelSelectInfo } = storeToRefs(useAppStore())
const onHandleClick = (item: PromptItem) => {
  mixpanel.guideUserUsePrompt(item.name)
  const obj: SendMessageParams = {
    prompt: item.prompt.value,
    sendDirectly: false,
  }
  if (item.attachments) {
    obj.attachments = item.attachments
  }
  setTimeout(() => {
    modelSelectInfo.value.mode = 36
    chatStore.sendMessage(obj)
  }, 500)
}
</script>
