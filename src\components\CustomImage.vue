<template>
  <div
    ref="imgBox"
    v-loading="isLoading"
    element-loading-background="transparent"
    style="min-height: 100px"
  >
    <Transition enter-active-class="animate__animated animate__fadeIn">
      <p v-if="!isLoading" v-html="imgText" class="mt-4"></p>
    </Transition>
    <Transition enter-active-class="animate__animated animate__fadeIn">
      <div v-show="!isLoading">
        <img
          v-if="imgSrc"
          ref="img"
          class="chat-img"
          :class="{ hover }"
          :src="imgSrc"
          :alt="imgAlt"
          lazy
          @click="onClickImg"
          @load="onLoaded"
        />
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  uuid: {
    type: String,
    default: '',
  },
  src: {
    type: String,
    default: '',
  },
  alt: {
    type: String,
    default: '',
  },
  img: {
    type: String,
    default: '',
  },
  imgText: {
    type: String,
    default: '',
  },
  wrapper: {
    type: HTMLElement,
  },
  hover: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:img', 'update:imgText', 'clickImg'])

const img = ref<HTMLImageElement>()
// const imgSrc = ref(props.src)
const imgAlt = ref(props.alt)
const isLoading = ref(false)
const imgBox = ref<HTMLDivElement>()

const imgSrc = computed(() => props.src)

function onClickImg() {
  emit('clickImg')
}

function onLoaded() {
  isLoading.value = false
}

onMounted(() => {
  init()
})

async function init() {
  if (props.src) {
    emit('update:img', props.src)
  }
  if (props.alt) {
    emit('update:imgText', props.alt)
  }
}
</script>
