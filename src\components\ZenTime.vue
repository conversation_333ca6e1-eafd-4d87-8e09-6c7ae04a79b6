<template>
  <div class="h-40px w-fit flex-y-c border-1px border-#f1f1f1 border-rd-5px border-solid bg-#f9f9f9 px-7px font-Lexend space-x-10px dark:(border-#0000 bg-#000)">
    <button
      class="relative h-26px min-w-48px w-48px flex-c overflow-hidden border-1px border-#e7e9ed border-rd-3px border-solid bg-#fdfdfd text-13px font-bold shadow-[0px_2px_5px_#0000000f]"
      @click="isZenMode = false"
      @mouseenter="hovering = true"
      @mouseleave="hovering = false"
    >
      <div class="h-16px overflow-hidden">
        <div class="flex flex-col transition-cusbezier-300" :class="{ '-translate-y-16px': hovering }">
          <div class="slot-item dark:text-#000">ZEN</div>
          <div class="slot-item text-#f00">ESC</div>
        </div>
      </div>
    </button>
    <div class="min-w-68px text-left text-#384A67 dark:text-#CDCDCD">{{ formattedTime }}</div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const appStore = useAppStore()
const { isZenMode } = storeToRefs(appStore)
const hovering = ref(false)
const elapsedTime = ref(0)
const timerInterval = ref<number | null>(null)

// 格式化时间
const formattedTime = computed(() => {
  const hours = Math.floor(elapsedTime.value / 3600).toString().padStart(2, '0')
  const minutes = Math.floor((elapsedTime.value % 3600) / 60).toString().padStart(2, '0')
  const seconds = (elapsedTime.value % 60).toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
})

const startTimer = () => {
  elapsedTime.value = 0
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
  }
  timerInterval.value = window.setInterval(() => {
    // 限制最大时间为 999:59:59 (3,599,999秒)
    if (elapsedTime.value < 3599999) {
      elapsedTime.value++
    }
  }, 1000)
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
  elapsedTime.value = 0
}

watch(isZenMode, (newValue) => {
  if (newValue) {
    startTimer()
  }
  else {
    stopTimer()
  }
}, { immediate: true })

onMounted(() => {
  if (isZenMode.value) {
    startTimer()
  }
})

onUnmounted(() => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
  }
})
</script>

<style lang="scss" scoped>
.slot-item {
  @apply h-16px flex-c;
}
</style>
