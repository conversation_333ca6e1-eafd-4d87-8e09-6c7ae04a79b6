<template>
  <div class="messages flex flex-col items-end">
    <TransitionGroup
      enter-active-class="animate__animated animate__fadeInRight"
      leave-active-class="animate__animated animate__fadeOutRight"
    >
      <div
        v-for="item in notificationList"
        :key="item.id"
        class="message-item common-border flex items-center"
      >
        <span class="message-icon" :class="[item.type]">//</span>
        <div class="message-content">{{ item.content }}</div>
        <span class="icon-close" @click="closeNotification(item)"></span>
      </div>
    </TransitionGroup>
  </div>
</template>

<script lang="ts" setup>
import { closeNotification } from '@/common/index'
import { useNotificationStore } from '@/stores/notification'

const notificationStore = useNotificationStore()

const notificationList = computed(() => {
  return notificationStore.notificationList
})
</script>

<style lang="scss" scoped>
.messages {
  position: fixed;
  right: 40px;
  top: 120px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: Kanit;
  color: var(--black);
  font-size: 14px;

  .message-item {
    margin-bottom: 20px;
  }

  .icon-close {
    margin-left: 100px;
    font-size: 20px;
    cursor: pointer;
  }

  .message-icon {
    font-weight: bold;
    margin-right: 10px;

    &.success {
      color: #6dfe86;
    }

    &.error {
      color: red;
    }

    &.warn {
      color: #ffb255;
    }
  }
}
</style>
