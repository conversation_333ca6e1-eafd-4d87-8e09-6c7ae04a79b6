<template>
  <div class="juchats-switch" :class="{ checked, disabled }" @click="disabled || $emit('update:modelValue', !checked)">
    <div class="dot"></div>
  </div>
</template>

<script lang='ts' setup>
defineProps<{
  disabled?: boolean
}>()

const checked = defineModel<boolean>('modelValue', { default: false })
</script>


<style scope>
.juchats-switch {
  @apply w-24px h-14px bg-#F5F5F5 dark:bg-#E7E9ED rounded-full cursor-pointer p-3px box-border transition-all duration-100;

  &.disabled {
    @apply cursor-not-allowed opacity-80;
  }

  .dot {
    @apply w-8px h-8px rounded-full bg-white transition-all duration-100;
  }

  &.checked {
    @apply bg-#49C355 dark:bg-#49C355;

    .dot {
      @apply ml-10px;
    }
  }
}
</style>
