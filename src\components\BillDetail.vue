<script lang="ts" setup>
import { formatUnitPlus, lottieAnimation } from '@/common'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useAppStore } from '@/stores/app'
import JsBarcode from 'jsbarcode'
import moment from 'moment'

const props = defineProps({
  giftData: {
    type: Object,
    required: true,
  },
  code: String,
})
const router = useRouter()
const { userInfo, getUserInfo } = useUserInfo()
const { isPc } = storeToRefs(useAppStore())
const { giftData, code } = toRefs(props)
const lottieElement = ref()
onMounted(async () => {
  lottieAnimation(lottieElement.value)
  if (isPc.value) {
    JsBarcode('.gift-view__ticket__barcode', String(giftData.value.ticketNo), {
      format: 'CODE39',
      width: 1.8,
      height: 18,
      displayValue: false,
      textMargin: 0,
      fontSize: 15,
      margin: 0, // 设置条形码周围的空白边距
    })
  }
  await getUserInfo()
})
</script>

<template>
  <div class="bill-detail relative h-full w-full flex-y-c flex-col items-center">
    <div
      ref="lottieElement"
      class="absolute z-10 h-screen -top-100px lt-md:(top-0 h-full)"
    ></div>
    <div class="z-1 pt-40 lt-md:bg-transparent">
      <div
        class="relative mt-[-5rem] flex items-center justify-center lt-md:(flex-col)"
      >
        <div
          class="flex items-center rounded-[1.25rem] p-[0.5rem] lt-md:(flex-col)"
        >
          <AvatarFrame
            :src="userInfo.thumbnailUrl"
            name="w-12 h-12 rounded-3 lt-md:w-25 lt-md:h-25"
          >
          </AvatarFrame>
          <div
            class="max-w-40 of-hidden text-ellipsis pl-2.5 pr-1 text-align-justify text-6 font-extrabold tracking-normal font-RobotoSlab"
          >
            {{ userInfo.name }}
          </div>
        </div>
        <div class="mx-[1.2rem] text-[1.4rem] !lt-md:hidden">×</div>
        <div
          class="flex text-14 text-[#FF0000] font-['Damion'] lt-md:(mt-10 text-8)"
        >
          <div>{{ giftData.title?.replace("年", "") }}</div>
        </div>
      </div>
      <div class="relative pb-[1.5rem] pt-[0.5rem] text-center text-[1rem]">
        {{
          $t("packageBox.congratulate", {
            title: giftData.title?.replace("年", ""),
          })
        }}
      </div>
    </div>

    <div class="gift-view__outlet z-1 !lt-md:hidden" />
    <div v-if="isPc" class="gift-view__mask">
      <div class="gift-view__ticket text-primary-300">
        <div class="gift-view__ticket__content">
          <div
            class="relative left-[50%] top-29px w-[calc(100%-55px)] overflow-hidden pt-6 text-center text-xs text-[#EDEDEDFF] -translate-x-50% dark:text-[#727272]"
          >
            ////////////////////////////////////////////////////////////////////////////////////////////////////
          </div>
          <div class="px-25px py-0 font-RobotoMono">
            <div class="flex pt-10 text-sm">
              <div>{{ $t("packageBox.subscriptionPackages") }}：</div>
              <div class="text-[1.2rem] text-[#FF0000] font-['Damion']">
                {{ giftData.title }}
              </div>
            </div>
            <div class="flex pt-2 text-sm">
              <div>{{ $t("packageBox.subscriptionTerm") }}：</div>
              <div class="text-[#B3B3B3]">
                {{ formatUnitPlus({ amount: giftData.expireDays }) }}
              </div>
            </div>
            <div class="flex pb-10 pt-2 text-sm">
              <div>{{ $t("packageBox.cutOffDate") }}：</div>
              <div class="text-[#B3B3B3]">
                {{ moment(giftData.expireTime).format("YYYY.MM.DD") }}
              </div>
            </div>
            <div class="gift-view__ticket__point">
              {{ moment(giftData.expireTime).format("YYYY / MM / DD") }}
            </div>
            <div class="gift-view__ticket__point">
              {{ $t("billDetail.serialNumber") }} {{ code }}
            </div>
            <div class="pb-5 pt-2">
              <svg class="gift-view__ticket__barcode"></svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="z-10 mt-6">
      <div
        class="h-11 w-57.5 flex cursor-pointer items-center justify-center rounded-2 bg-destructive-300 text-4 text-[#FFFFFF] shadow-[0_0_3rem_0_rgba(246,81,52,0.46)]"
        @click="() => router.go(0)"
      >
        {{ $t("billDetail.start") }}
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@keyframes shake-order {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(-24px);
  }
}

.gift-view__mask {
  @apply relative overflow-hidden z-2 -top-36px px-40px;

  height: calc(100% - 10px);
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}

.bill-detail {
  &::before {
    content: '';

    @apply absolute top-220px z-2000 w-440px h-25px m-auto dark:([--outlet-from:#000] [--outlet-to:#000]) hidden md:block;

    background-image: linear-gradient(180deg, #00000014 0%, #fff0 99%);
  }

}

.gift-view__outlet {
  @apply w-440px h-50px;

  --outlet-from: #00000014;
  --outlet-to: #fff0;

  &::after {
    content: '';

    @apply absolute z-2 w-440px h-25px m-auto dark:([--outlet-from:#000] [--outlet-to:#000]) bg-#F1F1F1 dark:bg-transparent;
  }
}

.gift-view__ticket {
  @apply animate-[shake-order_2s_linear_forwards] w-420px drop-shadow-[0_30px_20px_#00000010] m-auto pb-20px pt-8px;

  background-image: radial-gradient(
    circle at 50% 100%,
    transparent 13px,
    var(--egg-redemption-ticket-bg) 14px
  );
  background-size: 46px 27px;
  background-position: center bottom;
  background-repeat: repeat-x;

  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 25px;
    margin-bottom: -20px;

    @apply bg-white dark:bg-transparent;
  }

  .gift-view__ticket__content {
    @apply relative overflow-hidden;

    background: radial-gradient(
      circle at 45px 45px,
      transparent 15px 15px,
      var(--egg-redemption-ticket-bg) 15px 15px
    )
      repeat-x -45px 15px / 100% 72px,
      linear-gradient(
      to bottom,
      var(--egg-redemption-ticket-bg),
      var(--egg-redemption-ticket-bg)
    )
      no-repeat 100% 80px,
      linear-gradient(
      to bottom,
      var(--egg-redemption-ticket-bg),
      var(--egg-redemption-ticket-bg)
    )
      no-repeat 100% 175px / 100% 100%;
  }

  .gift-view__ticket__code {
    @apply text-120px leading-110px text-destructive-300 text-center;

    font-family: EXEPixelPerfect;
  }

  .gift-view__ticket__container {
    @apply px-25px py-0 font-RobotoMono;
  }

  .gift-view__ticket__point {
    @apply text-16px;
  }

  .gift-view__ticket__barcode {
    @apply h-18px;
  }
}
</style>
