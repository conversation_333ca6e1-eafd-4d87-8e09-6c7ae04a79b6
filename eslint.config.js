import antfu from '@antfu/eslint-config'

export default antfu(
  {
    unocss: true,
    stylistic: {
      overrides: {
        // 允许顶层使用箭头函数
        'antfu/top-level-function': 'off',
        'no-console': 'off',
      },
    },
  },
  {
    files: ['**/*.vue'],
    rules: {
      'vue/block-order': ['error', { order: [['script', 'template'], 'style'] }], // vue格式文件的3个tag顺序, 前两个顺序无所谓
      'vue/custom-event-name-casing': ['error', 'camelCase'], // vue的自定义事件名必须是小驼峰
      'vue/singleline-html-element-content-newline': 'off', // 不检查DOM标签的单行内容换行
      'vue/html-self-closing': 'off', // 不检查html标签自闭合
      'import/order': 'off', // improt sort ignore
      'ts/no-use-before-define': 'off', // 不检查vue文件setup定义顺序
      'vue/one-component-per-file': 'off', // 多个组件定义在一个文件中
    },
  },

  {
    rules: {
      'eqeqeq': ['error', 'always'],
      'quote-props': ['error', 'consistent-as-needed'],
      'curly': ['error', 'all'],
    },
  },
)
