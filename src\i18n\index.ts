import { createI18n } from 'vue-i18n'
// 语言包
import zhCn from './lang/zh'
import en from './lang/en'

export const getDefualtLang = () => {
  const lang = navigator.language
  if (lang.includes('zh')) {
    return 'zhCn'
  }
  else {
    return 'en'
  }
}

const i18n = createI18n({
  legacy: false, // 设置为 false，启用 composition API 模式
  locale: localStorage.getItem('localeLang') || getDefualtLang(),
  messages: {
    zhCn,
    en,
  },
  warnHtmlMessage: false
})
export default i18n
