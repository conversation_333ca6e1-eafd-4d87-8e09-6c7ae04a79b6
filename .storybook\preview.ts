import type { Preview } from '@storybook/vue3'
import type { App } from 'vue'
import { setup } from '@storybook/vue3'
import { createPinia } from 'pinia'
import 'uno.css'
import '@/assets/main.scss'
import '@/assets/global.css'
import '@/assets/vars.scss'
import '@/assets/styles/notion-light-enhanced.scss'
import 'element-plus/dist/index.css'
import i18n from '../src/i18n/index'

const pinia = createPinia()

setup((app: App) => {
  app.use(pinia)
  app.use(i18n)
})

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
}

export default preview
