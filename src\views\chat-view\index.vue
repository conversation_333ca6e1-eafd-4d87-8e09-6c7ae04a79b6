<template>
  <div ref="uploaderDropZoneRef" :class="{ 'thinking-time-functional': thinkingTimeFunctional, 'hidden-thinking-time-functional': hiddenThinkingTimeFunctional }">
    <BindLogin></BindLogin>

    <HeaderBar2
      ref="headerBar" :class="[
        isZenMode && '-mt-[var(--header-bar-height)] !shadow-xl z-1 relative',
        isZenModeDelay && '!transition-delay-800 !duration-800',
      ]" @logo="throttledNewMessage" @refresh="() => selectMessage({
        id: messageId,
        ttsLanguageTypeId: languageId,
        ttsType: ttsId,
        modeId: mode,
        contextId,
        groupId,
      })"
    ></HeaderBar2>
    <div
      class="chat-view h-[794px] flex justify-between transition-cusbezier-300" :class="{
        'receiving': loading,
        '!duration-800 !transition-delay-800': isZenMode,
        '!h-100dvh': isZenModeDelay,
      }"
    >
      <template v-if="isPc">
        <SideBar
          ref="sideBar" :groups="groups" :messages="generalDialogs" :loading="loading" :message-id="messageId"
          :get-flag="getFlag" :models="models" @new="throttledNewMessage" @edit="editMessage" @blur="blurMessage"
          @delete-group="deleteGroup" @delete="deleteMessage" @delete-chats="deleteChatsGroup" @select="selectMessage"
          @switch="switchMessage" @grouping="createGroup" @reset-title="resetTitle" @move-group="moveGroup"
        ></SideBar>
      </template>
      <template v-else>
        <Teleport to="body">
          <SideBar
            ref="sideBar" :groups="groups" :messages="generalDialogs" :loading="loading" :message-id="messageId"
            :get-flag="getFlag" :models="models" @new="newMessage(false)" @edit="editMessage" @blur="blurMessage"
            @delete-group="deleteGroup" @delete="deleteMessage" @delete-chats="deleteChatsGroup" @select="selectMessage"
            @switch="switchMessage" @grouping="createGroup"
          ></SideBar>
          <div
            v-if="expandSidebar"
            class="fixed inset-0 z-15 h-full w-full bg-[var(--mobile-sidebar-shadow-bg)] backdrop-blur-[3px]" @click="() => {
              expandSidebar = false;
            }
            "
          ></div>
        </Teleport>
      </template>
      <div class="chat-view-container relative min-w-0 w-full flex flex-1 justify-center" @click="onChatViewClick">
        <div class="h-[calc(100vh - 60px)] w-full flex-1">
          <el-collapse-transition>
            <div v-if="isZenMode" class="h-[78px] flex items-center justify-end pr-[39px]">
              <ZenTime />
            </div>
          </el-collapse-transition>
          <template v-if="['READY'].includes(status)">
            <Transition name="fade" mode="out-in">
              <EmptySpace v-if="!shakeVisible" :get-flag="getFlag"></EmptySpace>
            </Transition>
          </template>

          <template v-if="['START'].includes(status)">
            <ChatContent
              ref="chatContent" v-model:sections="sections" v-model:enable-scroll="enableScroll"
              v-model:llm-streaming="llmStreaming" class="outline-none"
              :skeleton-visible="skeletonVisible" :loading="loading" :message-id="messageId"
              :section-skeleton-visible="sectionSkeletonVisible" :selected-mode="selectedMode"
              :is-batch-loading="isBatchLoading" :send-box-focus="sendBoxFocus" section-shadow
              @send="sendQuestion" @resend="onResend" @retry="onRetry"
              @deploy="onDeploy"
            ></ChatContent>
          </template>
          <SceneButton
            v-if="sceneVisible" v-model:context-id="contextId" v-model:context-title="contextTitle"
            class="chat-view__scene" :class="{ 'chat-view__scene--active': ['START'].includes(status) }"
            @change="sceneChange"
          ></SceneButton>
          <Transition name="fade" mode="out-in">
            <CoverBox v-if="sceneVisible" @click="onScene(false)"></CoverBox>
          </Transition>
          <SendBox
            ref="sendBox" v-model:size="size" v-model="input" v-model:uuid="uuid" v-model:voice="voice"
            v-model:focus="sendBoxFocus" :loading="loading" :tts-id="ttsId" :mode-type="selectedMode?.type"
            :max-token="selectedMode?.maxToken" :selected="selected" :scene="sceneVisible"
            :show-attachment="showAttachment" class="chat-view__send chat-view__send--active" :class="{
              'chat-view__send--scene': sceneVisible,
              'opacity-0 translate-y-[20px]': shakeVisible,
            }" :placeholder="contextId
              ? `请向我提问「${contextTitle}」的问题`
              : modeTransPlaceholder(mode, models)
            " @scene="onScene" @enter="onEnter" @break-chat="onBrekChat" @resend="onResend"
            @on-assistant-message="onAssistantMessage" @on-silence-action="onSilenceAction"
            @on-receive-done="onReceiveDone"
          >
          </SendBox>

          <FooterBar class="chat-view__footer"></FooterBar>
        </div>
      </div>
    </div>
    <DragAttachmentPlaceHolder v-if="isOverDropZone"></DragAttachmentPlaceHolder>
  </div>
</template>

<script setup lang="ts">
import type { GenerateImageModel } from '@/model/image'
import type { SSEResponse } from '@/types'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { toRefs, useStorage } from '@vueuse/core'
import { decode, encode } from 'base-64'

import { ElNotification } from 'element-plus'
import { throttle } from 'lodash'
import moment from 'moment'
import avatar_gpt_dark from '@/assets/images/ai-avatar-dark.svg'
import avatar_gpt_light from '@/assets/images/ai-avatar-light.svg'
import avatar_default from '@/assets/images/user-default-avatar.svg'
import {
  generateUUID,
  getFlag,
  modeTransPlaceholder,
  modeTransSelected,
  modeTransType,
  upgrade,
} from '@/common'
import services from '@/common/axios'
import { accepts, DefaultModelId, file_accept, image_accept } from '@/common/constant'
import { clearFormatedCache } from '@/common/marked'
import messageDB from '@/common/message'
import { getFullImageUrl, isAcceptedFileType, notify, ThinkingMarkdownProcessor } from '@/common/tools'
import BindLogin from '@/components/BindLogin.vue'
import { mixpanel } from '@/config/mixpanel'
import { IMAGE_SPLIT, MODE_TYPE } from '@/enum'
import { useDevice } from '@/hooks/useDevice'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { createMessage } from '@/hooks/useMobildMessage/useMobileMessage'
import { usePluginAgent } from '@/hooks/usePluginAgent'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useAppStore } from '@/stores/app'
import { useChatStore } from '@/stores/chat'
import { useHeaderBarStore } from '@/stores/headerBar'
import { useLangStore } from '@/stores/i18n'
import { useTools } from '@/stores/tools'
import { useUserStore } from '@/stores/user'
import { SectionType } from '@/types/index'
import { provideChatView } from './provide'
import { processPrompt } from './tool'

const t = useGlobalI18n()
const { isDark } = useThemeChange()
const avatar_gpt = computed(() =>
  isDark.value ? avatar_gpt_light : avatar_gpt_dark,
)

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const headerBarStore = useHeaderBarStore()
const { fetchTools, showTool, tools, deepThinking, toolStatus, isModelSupportTools } = useTools()
const { isCelHiveLink } = usePluginAgent()

const { userInfoV2 } = storeToRefs(userStore)
const {
  expandSidebar,
  isPc,
  isClient,
  restartTrigger,
  newMessageTrigger,
  isZenMode,
  isZenModeDelay,
  currentArtifacts,
  upgradePackageTrigger,
  isResizablePanelVisible,
}
  = storeToRefs(useAppStore())
const { removeAllFiles, uploadManually, setActiveResendModelId } = useAppStore()
const appStore = useAppStore()

// 监听升级套餐触发器
watch(
  () => upgradePackageTrigger.value.count,
  () => {
    if (sideBar.value && sideBar.value.upgrade) {
      sideBar.value.upgrade()
    }
  },
)

// 创建一个临时变量用于存储最近收到的 HTML 内容
const latestArtifactsHtml = ref('')

watch(
  () => restartTrigger.value,
  () => {
    onBrekChat()
  },
)

watch(
  () => newMessageTrigger.value,
  () => {
    // 如果当前已经是一个新的对话，则不执行「新建对话」
    if (chatViewData.value.status === 'READY') {
      return
    }
    newMessage(false)
  },
)
const uploaderDropZoneRef = ref<HTMLDivElement>()
const dataTypes = ref(['image/jpeg', 'image/png', 'text/plain'])
const { isOverDropZone } = useDropZone(uploaderDropZoneRef, {
  dataTypes,
  onDrop(files) {
    if (!files) {
      return
    }
    if (!canUploadAttachment.value) {
      ElNotification.error({
        message: t('sendAttachment.notSupported').value,
      })
      return
    }

    // 当模式为25, 28, 29时，只接受图片格式
    if ([25, 28, 29].includes(selectedMode.value?.type ?? 0)) {
      if (files.some((element: any) => !isAcceptedFileType(element, image_accept))) {
        ElNotification.error({
          message: t('sendAttachment.onlyImage').value,
        })
        return
      }
    }

    // 当模式为7时，只接受文件格式
    if ([7].includes(selectedMode.value?.type ?? 0)) {
      if (files.some((element: any) => !isAcceptedFileType(element, file_accept))) {
        ElNotification.error({
          message: t('sendAttachment.onlyFile').value,
        })
        return
      }
    }

    if (
      isClient.value
      && files.every((element: any) => !isAcceptedFileType(element, accepts))
    ) {
      ElNotification.error({
        message: t('chatView.fileTypeMessage').value,
      })
      return
    }

    if (!showAttachment.value) {
      ElNotification.error({
        message: t('chatView.modeMessage').value,
      })
      return
    }
    if (files.length > 0) {
      uploadManually(files)
    }
  },
  // specify the types of data to be received.
  // 这里有些问题,accepts里面有类似text/markdown的类型,但是VueUse并不会在拖拽上去时候有反应
  // eslint-disable-next-line ts/ban-ts-comment
  // @ts-expect-error
  dataTypes: isClient.value ? undefined : accepts,
})

const loading = ref(false)
const llmStreaming = ref(false)

const groupId: any = ref(null)
const ctrl = ref()
const {
  chatViewData,
  selectedMode,
  thinkingTimeFunctional,
  hiddenThinkingTimeFunctional,
  deleteMessageTrigger,
  favoriteMessageTrigger,
  moveGroupTrigger,
  modelSelectInfo,
  showAttachment,
  isGenerateImage,
  attachmentsRes,
  modelData,
  canUploadAttachment,
  currentChatAttachment,
} = storeToRefs(useAppStore())
// 这里的toRefs是vueuse提供的而非vue提供，旨在解构ref的modelSelectInfo
const {
  selected,
  mode,
  ttsId,
  languageId,
  languageType,
  types,
  models,
  modelName,
} = toRefs(modelSelectInfo)
const { sendControlBarSelect, resetModel, getModelNameById } = useAppStore()
const chatStore = useChatStore()
const { generalDialogs, dialogs } = toRefs(chatStore)
const status = ref('READY')
const uuid = ref('')
const size = ref('1:1')
const voice = ref([])
const input = ref('')
const sections: any = ref([])
const messageId = ref(0)

const contextId: any = ref(null)
const contextTitle: any = ref('')
const groups: any = ref([])
const sideBar = ref()
const headerBar = ref()
const sendBox = ref()
const sceneVisible = ref(false)
const shakeVisible = ref(false)
const chatContent = ref()
const enableScroll = ref(true)
const skeletonVisible = ref(false)
const sectionSkeletonVisible = ref(false)

const switchMessage = async ({ groupId, reload, status }: any) => {
  groups.value.map(
    async (element: { id: number, array: object[], visible: boolean }) => {
      if (groupId === element.id) {
        if (!element.array || reload) {
          const array: any = await services.post(
            '/gpt/dialogs',
            {
              id: groupId,
            },
            {
              headers: {
                noninductive: true,
              },
            },
          )
          element.array = array.reverse().map((element: any) => ({
            ...element,
            editing: false,
          }))
          chatStore.appendDialogs(array)
        }
        if (['OPEN'].includes(status)) {
          element.visible = true
        }
        else if (['CLOSE'].includes(status)) {
          element.visible = false
        }
        if (!reload && !status) {
          element.visible = !element.visible
        }
      }
    },
  )
}

const getStarGroup = async ({ status }: any) => {
  const groupId = groups.value.find((element: any) => element.starFlag).id
  await switchMessage({ groupId, reload: true, status })
}

const getGroups = async () => {
  const groupsData: any = await services.get('/gpt/groups', {
    headers: {
      noninductive: true,
    },
  })
  const array = groupsData.map((element: any) => {
    const item = {
      ...element,
      defaultFlag: 0,
      groupType: 'startGroup',
      visible: false,
    }

    if (item.name === '星标对话') {
      item.name = t('sideBar.starDialog')
    }
    else if (item.name === '默认分组') {
      item.name = t('sideBar.defaultGroup')
    }

    return item
  })
  array.push({
    createTime: '',
    defaultFlag: 1,
    groupType: 'scenceGroup',
    dialogCount: null,
    id: 0,
    name: t('sideBar.groupDialog'),
    starFlag: 0,
    array: [],
    visible: true,
  })
  groups.value = array
}

const getMessages = async () => {
  await chatStore.loadDialogs()
}
// 添加sidebar的聊天标题
const addMessages = (data: any) => {
  const existingMessage = generalDialogs.value.find(
    (element: any) => element.id === data.id,
  )
  if (!existingMessage) {
    sideBar.value.switchMenuBar({ modeId: data.modeId, ttsType: data.ttsType })
    const newDialog = {
      id: data.id,
      name: '新对话',
      type: data.type,
      modeId: data.modeId,
      ttsType: data.ttsType,
      groupId: data.groupId,
      contextId: data.contextId,
      ttsLanguageTypeId: data.ttsLanguageTypeId,
      createTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      shareCode: '',
      updateTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    }
    generalDialogs.value.unshift(newDialog)

    // 同时将新对话添加到chatStore.dialogs中，侧边栏会立即更新
    chatStore.appendDialogs([newDialog])
  }
}

const create = async (name: string) => {
  const data: any = await services.post('/gpt/createDialog', {
    dialogType: 1,
    name: name.trim().substring(0, 50),
    type: modeTransType(mode.value, ttsId.value, models.value), // 1: GPT 2:VOICE 3:Midjourney 4: LLMs 5:E3 6:4v 7.groq 8:claude 9:claude image
    ttsLanguageTypeId: languageId.value,
    ttsType: ttsId.value,
    modeId: mode.value,
    contextId: contextId.value,
  })
  groupId.value = null
  messageId.value = JSON.parse(data)
  sideBar.value && sideBar.value.scrollTop && sideBar.value.scrollTop()
  addMessages({
    id: messageId.value,
    name,
    type: modeTransType(mode.value, ttsId.value, models.value), // 1: GPT 2:VOICE 3:Midjourney 4: LLMs 5:E3 6:4v 7.groq 8:claude 9:claude image
    ttsLanguageTypeId: languageId.value,
    ttsType: ttsId.value,
    modeId: mode.value,
    contextId: contextId.value,
    groupId: groupId.value,
  })
  router.push({
    name: 'Chat',
    params: {
      id: encode(JSON.stringify(messageId.value)),
    },
  })
}

const scrollSction = () => {
  if (!enableScroll.value) {
    return
  }
  const interval = setInterval(() => {
    if (chatContent.value) {
      chatContent.value.scrollBottom()
      clearInterval(interval)
    }
  })
}

const onDeploy = () => {
  if (route.params.id) {
    const target = generalDialogs.value.find(
      (element: any) => String(element.id) === decode(String(route.params.id)),
    )
    target && target.id && sideBar.value.selectMessage(target)
  }
}

const getInfo = () => {
  if (headerBar.value?.getInfo) {
    headerBar.value.getInfo()
  }
}

const foldThink = (lastSectionId: string) => {
  const lastSection = document.querySelector(`[data-id="${lastSectionId}"]`)
  if (lastSection) {
    if (lastSection.ownerDocument === document) {
      lastSection.querySelectorAll('.collapse-btn:not(.collapse-thinking)').forEach((item: any) => {
        item.click()
      })
    }
    else {
      console.warn('last section document')
    }
  }
  else {
    console.warn('lost last section dom')
  }
}

async function sendFlux({ prompt }: { prompt: string }) {
  const dialogId = messageId.value
  const id = await services.post(
    `${import.meta.env.VITE_APP_GPT_URL}/replicate/generate`,
    {
      aspectRatio: size.value,
      prompt,
      modeId: mode.value,
      dialogId,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  return await getFluxRecords(id, dialogId)
}

const createLeptonDialog = async (id: number) => {
  loading.value = true
  sectionSkeletonVisible.value = false
  try {
    if (['READY'].includes(status.value)) {
      groupId.value = null
      messageId.value = id
      sideBar.value.scrollTop()
      addMessages({
        id: messageId.value,
        name: '新对话',
        type: modeTransType(mode.value, ttsId.value, models.value), // 1: GPT 2:VOICE 3:Midjourney 4: LLMs 5:E3 6:4v 7.groq 8:claude 9:claude image
        ttsLanguageTypeId: languageId.value,
        ttsType: ttsId.value,
        modeId: mode.value,
        contextId: contextId.value,
        groupId: groupId.value,
      })
      router.push({
        name: 'Chat',
        params: {
          id: encode(JSON.stringify(messageId.value)),
        },
      })
      status.value = 'START'
    }
  }
  catch { }
}

async function getFluxRecords(id: any, dialogId: any) {
  if (dialogId !== messageId.value) {
    throw new Error('IS_NOT_CURRENT_DIALOG')
  }
  let res: any = await services.post(
    '/gpt/chatRecord',
    {
      id,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  if (
    res.createTime
    && moment.duration(moment().diff(moment(res.createTime))).asMinutes() > 10
  ) {
    throw new Error('Time out')
  }
  const resultArr = res?.thumbnailUrls
  if (!resultArr?.length || resultArr[0].startsWith('https')) {
    await new Promise(resolve => setTimeout(resolve, 5000))
    res = await getFluxRecords(id, dialogId)
  }
  return res
}

async function sendIdeogram({ prompt }: { prompt: string }) {
  const url = `${import.meta.env.VITE_APP_GPT_URL}/ideogram/generate`
  return await services.post<GenerateImageModel, GenerateImageModel>(
    url,
    {
      prompt,
      aspectRatio: `ASPECT_${size.value.replace(':', '_')}`,
      modeId: mode.value,
      dialogId: messageId.value,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
}
async function sendGPTImage({ prompt }: { prompt: string }) {
  // 判断是否有图片上传
  const hasImages = attachmentsRes.value.IMAGE.length > 0

  // 如果有图片上传，则是图生图
  if (hasImages) {
    const url = `${import.meta.env.VITE_APP_GPT_URL}/images/edits`
    const formData = new FormData()
    formData.append('prompt', prompt)
    formData.append('modeId', mode.value.toString())
    formData.append('dialogId', messageId.value.toString())

    // 添加uuid参数，多个图片uuid用英文逗号拼接
    const uuids = attachmentsRes.value.IMAGE.map(item => item.uuid).join(',')
    formData.append('uuid', uuids)
    removeAllFiles()
    return await services.post<any, any>(
      url,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
  }
  else {
    // 没有图像上传，则是文生图
    const url = `${import.meta.env.VITE_APP_GPT_URL}/images/generations`
    return await services.post<any, any>(
      url,
      {
        prompt,
        modeId: mode.value,
        dialogId: messageId.value,
      },
    )
  }
}

async function sendSD({ prompt }: { prompt: string }) {
  const url = `${import.meta.env.VITE_APP_GPT_URL}/stable-image/generate/ultra`
  return await services.post<any, { imageURL: string }>(
    url,
    {
      aspectRatio: size.value,
      prompt,
      modeId: mode.value,
      dialogId: messageId.value,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
}

const setFeedback = (content: string) => {
  const lastSection = sections.value[sections.value.length - 1]
  if (lastSection && lastSection.nickName === 'Juchats') {
    lastSection.content = content
  }
  else {
    // 空块
  }
}

const getNewMessageId = async () => {
  // 新发送的对话没有id 重新获取当前对话的id 重新提问及删除需要
  try {
    const {
      records: [{ id, artifactsList }],
    }: any = await services.post(
      '/gpt/chatRecords',
      {
        id: messageId.value,
        limit: 1,
        page: 1,
      },
      {
        headers: {
          noninductive: true,
        },
      },
    )
    sections.value[sections.value.length - 1].id = id
    if (artifactsList) {
      sections.value[sections.value.length - 1].artifactsList = artifactsList
      currentArtifacts.value = artifactsList[artifactsList.length - 1]
    }

    nextTick(() => {
      foldThink(id)
    })
  }
  catch (err) {
    console.error('获取新消息ID失败:', err)
  }
}

// @TODO 目前tts对话没法和常用对话通用，后期拆分每个不同type类型的聊天enter action
const onAssistantMessage = (response: SSEResponse, userMessage: string) => {
  const { requestId, content = '' } = response
  const existingSection = sections.value.find((element: any) => {
    return element.requestId === requestId && element.receiving
  })
  const object: any = {
    unionid: generateUUID(),
    avatar: avatar_gpt,
    question: userMessage,
    content: '',
    requestId,
    receiving: true,
    isNew: true,
    file: [],
    date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
    nickName: 'Juchats',
  }
  // 新建assistant消息
  if (!existingSection) {
    // 更新提问的内容
    setFeedback(userMessage)
    sections.value.push({ ...object, page: 1, children: [object] })
  }
  else {
    // 更新assistant消息
    existingSection.content += content
    existingSection.children[0].content += content
  }
  scrollSction()
}
// 创建新提问
const onSilenceAction = async (id: number) => {
  loading.value = true
  sectionSkeletonVisible.value = false
  try {
    if (['READY'].includes(status.value)) {
      groupId.value = null
      messageId.value = id
      sideBar.value.scrollTop()
      addMessages({
        id: messageId.value,
        name: '新对话',
        type: modeTransType(mode.value, ttsId.value, models.value), // 1: GPT 2:VOICE 3:Midjourney 4: LLMs 5:E3 6:4v 7.groq 8:claude 9:claude image
        ttsLanguageTypeId: languageId.value,
        ttsType: ttsId.value,
        modeId: mode.value,
        contextId: contextId.value,
        groupId: groupId.value,
      })
      router.push({
        name: 'Chat',
        params: {
          id: encode(JSON.stringify(messageId.value)),
        },
      })
      status.value = 'START'
    }
  }
  catch {
    return
  }
  const requestId = generateUUID()
  const latestSection = {
    unionid: generateUUID(),
    avatar: userInfoV2.value.thumbnailUrl || avatar_default,
    requestId,
    content: '新对话',
    file: [],
    date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
    nickName: userInfoV2.value.name,
  }
  sections.value.push(latestSection)
}
const langStore = useLangStore()
const { language } = storeToRefs(langStore)

const onReceiveDone = async (id?: string, force = false) => {
  try {
    // 修改message标题
    if (sections.value.length <= 2 || force) {
      const title: string = await services.post(
        `gpt/generateDialogTitle`,
        {
          id: id || messageId.value,
          language: language.value === 'zhCn' ? '' : 'english',
        },
        {
          baseURL: '/gw/chatgpt',
        },
      )
      let currentText = ''
      for (const char of title) {
        currentText += char
        generalDialogs.value.forEach((item: any) => {
          if (item.id === messageId.value) {
            item.name = currentText
          }
        })
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
  }
  catch {
  }
}

// 重置标题
function resetTitle(id: string) {
  if (!id) {
    return
  }
  notify.success({
    title: t('sideBar.resetTitleEmitSuccess').value,
  })
  onReceiveDone(id, true)
}
// 获取多语言下的工具名称
const getToolName = (toolFlag?: string) => {
  if (!toolFlag) {
    return ''
  }
  else {
    return t(`tools.${toolFlag}`).value || ''
  }
}

// 用于更新聊天内容后的工具调用动画
function updateChatContentToolsLoading(showAnimation: boolean, toolName: string) {
  if (chatContent.value) {
    chatContent.value.showSharkAnimation = showAnimation
    chatContent.value.currentToolName = toolName
  }
}

// 后端返回的工具调用type以及对应的处理函数
const ACTION_HANDLERS = {
  TOOL_CALL_START: (toolName?: string) => {
    updateChatContentToolsLoading(true, getToolName(toolName))
  },
  TOOL_CALL_END: () => {
    updateChatContentToolsLoading(false, '')
  },
  CHOOSE_TOOLS: () => {
    updateChatContentToolsLoading(true, getToolName('CHOOSE_TOOLS'))
  },
}
type ActionType = keyof typeof ACTION_HANDLERS
const { getUserInfoV2 } = useUserInfo()
const onEnter = async (params: any, resend?: boolean) => {
  if (loading.value) {
    return
  }
  if (input.value?.trim() === '' && !uuid.value) {
    return
  }

  if (params?.retryChatId) {
    params.relationId = undefined
  }

  scrollSction()

  sectionSkeletonVisible.value = true
  sceneVisible.value = false
  const content = input.value || `对话${moment(new Date()).format('HH:mm:ss')}`
  const fileUuid = uuid.value
  const file = voice.value
  loading.value = true
  enableScroll.value = true
  try {
    if (['READY'].includes(status.value) || Number(messageId.value) === 0) {
      await create(content)
      status.value = 'START'
    }
  }
  catch (err) {
    console.error('创建对话失败:', err)
    loading.value = false
    return
  }

  input.value = ''
  uuid.value = ''
  voice.value = []
  sendBox.value.resize()
  const requestId = generateUUID()
  let imgsUUIDS = ''
  if (showAttachment.value && attachmentsRes.value.IMAGE.length > 0) {
    imgsUUIDS = attachmentsRes.value.IMAGE.map((item) => {
      return item.uuid
    }).join(',')
  }

  messageDB.addOrUpdateMessage({
    messageID: messageId.value,
    messageContent: content,
    messageFile: file,
    uuid: imgsUUIDS,
    thumbnailUrls: attachmentsRes.value.IMAGE.map((item) => {
      return getFullImageUrl(item.thumbnailUrl!)
    }),
    attachments: attachmentsRes.value.DATA.length || attachmentsRes.value.TEXT.length || attachmentsRes.value.AUDIO.length
      ? [
          ...attachmentsRes.value.DATA,
          ...attachmentsRes.value.TEXT,
          ...attachmentsRes.value.AUDIO,
        ]
      : [],
  })

  let latestSection: any
  // 如果不是重新提问（没有relationId）则添加用户发送的对话信息
  if (!params?.relationId) {
    latestSection = {
      unionid: generateUUID(),
      avatar: userInfoV2.value.thumbnailUrl || avatar_default,
      requestId,
      content,
      file,
      date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
      nickName: userInfoV2.value.name,
    }

    if (
      attachmentsRes.value.DATA.length > 0
      || attachmentsRes.value.TEXT.length > 0
      || attachmentsRes.value.AUDIO.length > 0
    ) {
      latestSection.attachments = [
        ...attachmentsRes.value.DATA,
        ...attachmentsRes.value.TEXT,
        ...attachmentsRes.value.AUDIO,
      ]
    }

    if (imgsUUIDS) {
      latestSection.thumbnailUrls = attachmentsRes.value.IMAGE.map((item) => {
        return getFullImageUrl(item.thumbnailUrl!)
      })
    }

    if (params?.thumbnailUrls) {
      latestSection.thumbnailUrls = params.thumbnailUrls
    }

    if (params?.attachments) {
      latestSection.attachments = params.attachments
    }

    sections.value.push(latestSection)
  }

  if (selectedMode.value && [MODE_TYPE.STABLE].includes(selectedMode.value?.type || 0)) {
    try {
      const res = await sendSD({ prompt: content })
      const { imageURL } = res
      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: '',
        receiving: true,
        img: imageURL,
        imgText: '',
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      })
      if (!params?.relationId) {
        getNewMessageId()
      }
      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)
    }
    catch {
    }
  }
  else if ([MODE_TYPE.FLUXPRO].includes(selectedMode.value?.type || 0)) {
    try {
      const { thumbnailUrls } = await sendFlux({ prompt: content })
      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: '',
        receiving: true,
        img: getFullImageUrl(thumbnailUrls[0]),
        imgText: '',
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      })
      if (!params?.relationId) {
        getNewMessageId()
      }
      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)
    }
    catch { }
  }
  else if (selectedMode.value?.type === MODE_TYPE.FLUX_KONTEXT_PRO) {
    try {
      const imageUuid = attachmentsRes.value.IMAGE.map(item => item.uuid).join(',')
      if (!imageUuid) {
        ElNotification.error({
          message: t('chatView.fluxKontextProNoImageError').value,
        })
        loading.value = false
        sectionSkeletonVisible.value = false
        throw new Error('No image uploaded for Flux Kontext Pro')
      }

      const requestBody: any = {
        prompt: content,
        modeId: mode.value,
        dialogId: messageId.value,
        uuid: imageUuid,
      }
      removeAllFiles()
      // 发送初始请求，返回一个任务ID或其他用于轮询的标识符
      const initialFluxKontextResponse: any = await services.post(
        `${import.meta.env.VITE_APP_GPT_URL}/flux/edit`,
        requestBody,
        {
          headers: {
            noninductive: true,
          },
        },
      )

      // 从初始响应中获取用于轮询的ID
      const recordIdForPolling = initialFluxKontextResponse

      if (!recordIdForPolling) {
        loading.value = false
        sectionSkeletonVisible.value = false
        return
      }

      // 先添加一个不含图片的消息体
      sections.value.push({
        id: recordIdForPolling,
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: t('chatView.loading').value, // 初始文本
        receiving: true,
        img: '', // 初始无图片
        imgText: t('chatView.loading').value,
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
        originalImageUuidForKontextPro: imageUuid,
        originalImageThumbnailForKontextPro: getFullImageUrl(attachmentsRes.value.IMAGE[0]?.thumbnailUrl || ''),
      })

      // 开始轮询获取图片结果
      try {
        const fluxResult = await getFluxRecords(recordIdForPolling, messageId.value)
        const { thumbnailUrls: finalThumbnailUrls, originalUrls: finalOriginalUrls } = fluxResult

        const sectionIndex = sections.value.findIndex((s: any) => s.id === recordIdForPolling && s.nickName === 'Juchats')
        if (sectionIndex > -1) {
          sections.value[sectionIndex].img = finalThumbnailUrls?.[0]
            ? getFullImageUrl(finalThumbnailUrls[0])
            : (
                finalOriginalUrls?.[0]
                  ? getFullImageUrl(finalOriginalUrls[0])
                  : ''
              )
          sections.value[sectionIndex].content = ''
          sections.value[sectionIndex].imgText = ''
        }
        if (!params?.relationId) {
          await getNewMessageId()
        }
      }
      catch (pollingError) {
        console.error('Flux Kontext Pro polling error:', pollingError)
        const sectionIndex = sections.value.findIndex((s: any) => s.id === recordIdForPolling && s.nickName === 'Juchats')
        if (sectionIndex > -1) {
          sections.value[sectionIndex].content = t('chatView.imageFailedToLoad').value || '图片加载失败，请稍后重试。'
          sections.value[sectionIndex].imgText = t('chatView.imageFailedToLoad').value || '图片加载失败，请稍后重试。'
        }
      }
      finally {
        loading.value = false
        sectionSkeletonVisible.value = false
        sendBox.value.resize()
        sendBox.value.setFocus()
        getInfo()
        scrollSction()
        await generateTitle(params?.relationId)
      }
    }
    catch (err) {
      console.error('Flux Kontext Pro initial request error:', err)
      loading.value = false
      sectionSkeletonVisible.value = false
      // 可以在这里添加更具体的错误处理，例如通知用户，或者移除之前添加的占位消息
      const errorSectionIndex = sections.value.findIndex((s: any) => s.question === content && s.nickName === 'Juchats' && !s.img)
      if (errorSectionIndex > -1 && sections.value[errorSectionIndex].originalImageUuidForKontextPro) { // 确保是Kontext Pro的占位消息
        sections.value.splice(errorSectionIndex, 1) // 如果初始请求就失败，移除占位
      }
    }
  }
  else if ([20].includes(selectedMode.value?.type || 0)) {
    try {
      const res = await sendIdeogram({ prompt: content })
      const { thumbnailUrl, receivedPrompt } = res

      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: receivedPrompt,
        receiving: true,
        img: getFullImageUrl(thumbnailUrl || ''),
        imgText: receivedPrompt,
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      })
      if (!params?.relationId) {
        getNewMessageId()
      }
      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)
    }
    catch {
    }
  }
  else if ([36].includes(selectedMode.value?.type || 0)) {
    try {
      // 判断是否有图片上传
      const hasImages = attachmentsRes.value.IMAGE.length > 0
      const res = await sendGPTImage({ prompt: content })
      const { originalUrl } = res

      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: '',
        receiving: true,
        img: getFullImageUrl(originalUrl || ''),
        imgText: '',
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
        // 如果是图生图，添加原始图片信息
        ...(hasImages && { thumbnailUrls: attachmentsRes.value.IMAGE.map(item => getFullImageUrl(item.thumbnailUrl!)) }),
      })
      if (!params?.relationId) {
        getNewMessageId()
      }
      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)

      // 清理上传的图片
      if (hasImages) {
        removeAllFiles()
      }
    }
    catch (error) {
      console.error('GPT图像生成错误:', error)
      loading.value = false
      sectionSkeletonVisible.value = false
    }
  }
  else if ([29].includes(selectedMode.value?.type || 0)) {
    try {
      const requestId = generateUUID()
      const url = `${import.meta.env.VITE_APP_GPT_URL}/gpt/completions`
      const sendData: any = {
        prompt: content,
        requestId,
        modeId: mode.value,
        contextId: contextId.value,
        dialogId: messageId.value,
        fileUuid, // 文件uuid
      }

      if (showAttachment.value) {
        if (attachmentsRes.value.IMAGE.length > 0) {
          sendData.uuid = imgsUUIDS
        }
      }
      if (params?.uuid) {
        sendData.uuid = params.uuid
      }
      removeAllFiles()
      const res = await services.post<any, string>(
        url,
        sendData,
        {
          headers: {
            noninductive: true,
          },
        },
      )
      const array = JSON.parse(res)
      const answerText = array.map((element: { text: string }) => element.text)
      const inlineData = array.find((element: { inlineData: { data: string } }) => element.inlineData)?.inlineData

      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: answerText?.join(''),
        receiving: true,
        img: inlineData?.data ? getFullImageUrl(inlineData?.data) : '',
        imgText: '',
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      })
      if (!params?.relationId) {
        getNewMessageId()
      }

      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)
    }
    catch {
      loading.value = false
      sectionSkeletonVisible.value = false
    }
  }
  else if (!isGenerateImage.value) {
    const resendModelId = params?.resendModelId
    if (resendModelId) {
      setActiveResendModelId(resendModelId)
    }
    const sendData: any = {
      relationId: params?.relationId,
      prompt: content,
      requestId,
      modeId: resendModelId || mode.value,
      contextId: contextId.value,
      dialogId: messageId.value,
      languageTypeId: languageId.value,
      fileUuid, // 文件uuid
      type: !mode.value ? ttsId.value : undefined,
    }

    sendData.prompt = await processPrompt(sendData.prompt, isCelHiveLink.value)

    // 如果有retryChatId，添加到sendData中
    if (params?.retryChatId) {
      sendData.retryChatId = params.retryChatId
    }

    if (showAttachment.value) {
      if (attachmentsRes.value.IMAGE.length > 0) {
        sendData.uuid = imgsUUIDS
      }

      if (
        latestSection
        && latestSection.attachments
        && latestSection.attachments.length > 0
      ) {
        if ([31].includes(selectedMode.value?.type ?? -1)) {
          // 如果模型为GPT4.1 无法识别markdown文件
          // 故将markdown文件内容提取至 prompt 拼接
          const processMarkdownFile = async (element: { fileId: string, fileName: string, [key: string]: any }) => {
            try {
              const url = `${import.meta.env.VITE_APP_V1_API}/v1/app/file/getLink`
              const { fileLink } = await services.post<any, { fileLink: string }>(
                url,
                { fileId: element.fileId },
                { headers: { noninductive: true } },
              )
              const res = await fetch(fileLink)
              const text = await res.text()
              sendData.prompt = `${sendData.prompt}\n\n${text}`
              return undefined
            }
            catch {
              return element // 处理失败保留原文件
            }
          }

          // 处理所有附件
          const processedAttachments = await Promise.all(
            latestSection.attachments.map(async (element: any) => {
              if (element.fileName.includes('.md') || element.fileName.includes('.markdown')) {
                return processMarkdownFile(element)
              }
              return element
            }),
          )
          latestSection.attachments = processedAttachments.filter(Boolean)
        }
        sendData.attachments = latestSection.attachments
      }
    }
    if (params?.uuid) {
      sendData.uuid = params.uuid
    }

    // showTool.value只能判断当前模型是否支持tools字段
    // if 支持tools && (不是换模型发送 || 换的模型支持tools)
    // 则添加tools字段
    if (showTool.value && (!resendModelId || isModelSupportTools(resendModelId))) {
      sendData.tools = tools.value
        // DEEPSEEK允许联网搜索
        // .filter((tool: { id: string }) => {
        //   if (selectedMode.value?.type === MODE_TYPE.DEEPSEEKR1) {
        //     return tool.id === 'BROWSING'
        //   }
        //   else {
        //     return true
        //   }
        // })
        .filter((tool: { isActive: boolean }) => tool.isActive)
        .map((tool: { name: string, id: string }) => {
          return {
            name: tool.name,
            id: tool.id,
          }
        })
      if (selectedMode.value?.type === MODE_TYPE.SONNET) {
        sendData.deepThinking = deepThinking.value
      }
    }
    let initContent = ''
    ctrl.value = new AbortController()
    removeAllFiles()
    // 收起Artifacts
    chatContent?.value?.closeArtifactsCode()
    let startTimestamp: number = 0
    const localeLang = localStorage.getItem('localeLang') || ''
    let deepSeekRes = 'EMPTY'
    try {
      if (resend) {
        mixpanel.resendMessage()
      }
      else {
        mixpanel.sendPromptMessage(modelName.value, sendData.requestId)
      }

      await fetchEventSource(
        `/gw/chatgpt/gpt/completions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Jtoken': localStorage.token,
            'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
          },
          body: JSON.stringify(sendData),
          signal: ctrl.value.signal,
          openWhenHidden: true,
          // keepalive: ![31].includes(selectedMode.value?.type), // GPT4.1临时测试
          credentials: 'omit',
          onopen: async (res) => {
            if (res.headers.get('content-type')?.includes('text/event-stream')) {
              ThinkingMarkdownProcessor.reset()
              return // everything's good
            }
            const { code, msg } = await res.json()
            if (![200].includes(code)) {
              initContent = ''
              sectionSkeletonVisible.value = false
              if (code === 402) {
                notify.error({
                  dangerouslyUseHTMLString: true,
                  message: `${t('tipMessage.upgradeTip').value}<span class="notification__upgrade">${t('tipMessage.upgrade').value}</span>`,
                  onClick: () => { upgrade() },
                })
              }
              else {
                notify.error({
                  title: msg || '异常',
                  message: '',
                })
              }
              getInfo()
              loading.value = false
            }
          },
          onmessage: async (event: any) => {
            if ([event.data].includes('[DONE]')) {
              if (!params?.relationId) {
                getNewMessageId()
                getMessages()
                getUserInfoV2()
              }

              // 如果是第一次对话 对话长度小于等于两条 并且不是重新提问 生成标题
              initContent = ''
              loading.value = false
              sendBox.value.resize()
              sendBox.value.setFocus()
              getInfo()
              await generateTitle(params?.relationId)
              return
            }

            const { data } = JSON.parse(event.data)

            if (data) {
              sectionSkeletonVisible.value = false
            }
            else {
              return
            }

            const { requestId, originUrl, timeSeconds } = data

            if ([26].includes(selectedMode.value!.type)) {
              switch (deepSeekRes) {
                case 'EMPTY':
                  if (Object.prototype.hasOwnProperty.call(data, 'reasoning')) {
                    deepSeekRes = 'THINKING'
                    data.content = `\n<think>\n${data.reasoning}`
                  }
                  break
                case 'THINKING':
                  if (!Object.prototype.hasOwnProperty.call(data, 'reasoning')) {
                    deepSeekRes = 'THINKEND'
                    data.content = `\n</think>\n\n\n${data.content}`
                  }
                  else {
                    data.content = data.reasoning
                  }
                  break
              }
            }
            let content = data.content || ''

            if (thinkingTimeFunctional.value) {
              ThinkingMarkdownProcessor.process(content, (value) => {
                if (!hiddenThinkingTimeFunctional.value) { return }
                if (value) {
                  updateChatContentToolsLoading(true, t('claude4thinking').value)
                }
                else {
                  updateChatContentToolsLoading(false, '')
                }
              })
            }

            // 处理后端返回Tools调用时的信息，显示对应的loading
            if (data.JUCHATS_ACTION_MSG) {
              const { type, toolName, content: toolContent } = data.JUCHATS_ACTION_MSG
              ACTION_HANDLERS[type as ActionType]?.(toolName)

              if (type === 'CHOOSE_TOOLS') {
                startTimestamp = Date.now()
              }

              if (type === 'TOOL_CALL_START') {
                mixpanel.toolCallStart(toolName)

                // 当工具名为ARTIFACTS时，重置artifactsHtml内容，准备接收新的HTML代码
                if (toolName === 'ARTIFACTS') {
                  chatContent.value?.handleArtifactsToolCall('', false)
                  // 重置临时HTML变量
                  latestArtifactsHtml.value = ''

                  const end = Date.now()
                  mixpanel.artifactResponse({ requestId: sendData.requestId, start: startTimestamp, end, duration: end - startTimestamp })
                }
              }

              if (type === 'TOOL_CALL_END' && toolName === 'ARTIFACTS') {
                delete sections.value[sections.value.length - 1].artifact
                sections.value[sections.value.length - 1].toolsType = SectionType.Artifact
                // 当ARTIFACTS工具调用结束时，传递isEnd=true参数，表示切换到预览模式
                chatContent.value?.handleArtifactsToolCall(chatContent.value?.artifactsData || '', true)
              }

              if (type === 'TOOL_CALL_CONTENT' && toolStatus('mermaid').mermaid) {
                content = toolContent
                // Find and click the last render-mermaid element when tool content is received
                // Use MutationObserver to ensure element is rendered
                const observer = new MutationObserver((mutations, obs) => {
                  const renderMermaidElements = document.querySelectorAll('.render-mermaid')
                  if (renderMermaidElements.length > 0) {
                    const lastElement = renderMermaidElements[renderMermaidElements.length - 1] as HTMLElement
                    lastElement.click()
                    obs.disconnect() // Stop observing once clicked
                  }
                })

                // Start observing the document with the configured parameters
                observer.observe(document.body, {
                  childList: true,
                  subtree: true,
                })

                // Cleanup observer after 5 seconds to prevent memory leaks
                setTimeout(() => {
                  observer.disconnect()
                }, 5000)
              }
            }

            // 处理artifacts字段，更新ArtifactsCodeHtml组件的内容
            if (data.artifacts && data.artifacts.html) {
              sections.value[sections.value.length - 1].artifact = data.artifacts
              chatContent.value?.handleArtifactsToolCall(data.artifacts.html, false)
              // 保存最新的HTML内容到临时变量
              latestArtifactsHtml.value = data.artifacts.html
            }

            const existingSection = sections.value.find((element: any) => {
              return element.requestId === requestId && element.receiving
            })
            const object: any = {
              unionid: generateUUID(),
              avatar: avatar_gpt,
              question: sendData.prompt,
              content: '',
              requestId,
              receiving: true,
              isNew: true,
              relationId: params?.relationId,
              file: originUrl
                ? [{ filePath: originUrl, content, seconds: timeSeconds }]
                : [],
              date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
              nickName: 'Juchats',
            }

            // 如果是重新对话，获取重新对话的父级对话
            if (params?.relationId) {
              const resendSection = sections.value.find((element: any) => {
                return (
                  params?.relationId
                  && element.id === params.relationId
                  && element.receiving
                )
              })

              // 如果存在重新对话的父级对话
              if (resendSection) {
                const updateBeforeModelName = resendSection.modelShowName
                if (resendModelId) {
                  resendSection.modelShowName = getModelNameById(resendModelId)
                }

                // 如果存在重新对话的父级对话的子对话
                const resendChildrenSection = resendSection.children.find(
                  (element: any) => {
                    return element.requestId === requestId && element.receiving
                  },
                )

                // 如果存在重新对话的父级对话的子对话，则将内容添加到重新对话的父级对话的子对话中
                if (resendChildrenSection) {
                  resendChildrenSection.modelShowName = getModelNameById(resendModelId)

                  if (data.commentary) {
                    resendSection.content = JSON.stringify(data)
                    resendChildrenSection.content = JSON.stringify(data)
                  }
                  else {
                    resendSection.content += content
                    resendChildrenSection.content += content
                  }
                }
                // 如果不存在重新对话的父级对话的子对话，则将重新对话的父级对话的内容和子对话的内容合并
                else {
                  Object.keys(object).forEach((key) => {
                    resendSection[key] = object[key]
                  })
                  resendSection.content = data.content
                  resendSection.page = resendSection.children.length + 1
                  resendSection.children = resendSection.children
                    ? [...resendSection.children, object]
                    : [object]

                  if (!resendSection.children[0].modelShowName) {
                    resendSection.children[0].modelShowName = updateBeforeModelName
                  }
                }
              }
            }
            else if (!existingSection) {
              initContent = data.commentary ? JSON.stringify(data) : content
              sections.value.push({ ...object, content: initContent, page: 1, children: [object] })
            }
            else {
              if (!existingSection.isFirst) {
                existingSection.content = initContent
                existingSection.isFirst = true
              }
              if (data.commentary) {
                existingSection.content = JSON.stringify(data)
                existingSection.children[0].content = JSON.stringify(data)
              }
              else {
                existingSection.content += content
                existingSection.children[0].content += content
              }
              if (originUrl) {
                existingSection.file.push({
                  filePath: originUrl,
                  content: data.commentary ? JSON.stringify(data) : content,
                  seconds: timeSeconds,
                })
              }
            }
            // 不是重新提问 执行滚动到底部操作
            if (!params?.relationId) {
              scrollSction()
            }
          },
          onclose: () => {
            loading.value = false
            sectionSkeletonVisible.value = false
            initContent = ''
            llmStreaming.value = false
            setActiveResendModelId(null)
          },
          onerror: (error) => {
            throw error
          },
        },
      )
    }
    catch {
      setActiveResendModelId(null)
      loading.value = false
      sectionSkeletonVisible.value = false

      const lastSection = sections.value[sections.value.length - 1]
      if (lastSection && lastSection.nickName !== 'Juchats') {
        sections.value.push({
          content: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
          imgText: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
          avatar: avatar_gpt,
          date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
          file: [],
          nickName: 'Juchats',
          retry: true,
          question: sendData.prompt,
          receiving: true,
        })
      }
    }
  }
  else {
    try {
      const res = await sendE3({ prompt: content })
      const { thumbnailUrls, revisedPrompt } = res
      sections.value.push({
        question: content,
        unionid: generateUUID(),
        avatar: avatar_gpt,
        content: revisedPrompt,
        receiving: true,
        img: getFullImageUrl(thumbnailUrls),
        imgText: revisedPrompt,
        file: [],
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      })
      if (!params?.relationId) {
        getNewMessageId()
      }
      loading.value = false
      sectionSkeletonVisible.value = false
      sendBox.value.resize()
      sendBox.value.setFocus()
      getInfo()
      scrollSction()
      await generateTitle(params?.relationId)
    }
    catch {
    }
  }
}
chatStore.registerSendMessageFn((params) => {
  input.value = params.prompt
  if (params.attachments) {
    attachmentsRes.value.TEXT = params.attachments as any
  }
  // 只有当 sendDirectly 为 true 或未指定时才发送消息
  if (params.sendDirectly !== false) {
    onEnter({})
  }
})

const generateTitle = async (relationId: number | undefined) => {
  if (relationId) {
    return
  }

  if (sections.value.length <= 2) {
    try {
      const title: string = await services.post(
        `gpt/generateDialogTitle`,
        {
          id: messageId.value,
          language: language.value === 'zhCn' ? '' : 'english',
        },
        {
          baseURL: '/gw/chatgpt',
        },
      )

      let currentText = ''
      for (const char of title) {
        currentText += char
        generalDialogs.value.forEach((item: any) => {
          if (item.id === messageId.value) {
            item.name = currentText
          }
        })
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
    catch {
    }
  }
}

const sendQuestion = (string: string) => {
  input.value = string
  onEnter({})
}

const refreshGroup = async ({ id, status }: any) => {
  const groupId = groups.value
    .flatMap((element: any) => element.array)
    .find((element: any) => element && element.id === id)
    ?.groupId
  await switchMessage({ groupId, reload: true, status })
}

const isFavorited = (id: any) => {
  return groups.value
    .find((element: any) => element.starFlag)
    .array
    .find((element: any) => element.id === id)
}

watch(
  () => favoriteMessageTrigger.value.count,
  () => {
    favoriteMessage(favoriteMessageTrigger.value.id)
  },
)
const favoriteMessage = async (id: number | undefined) => {
  if (!id) {
    return
  }
  const messageFn: any = isPc.value ? ElNotification.success : createMessage
  const cancelStarTxt = t('chatView.cancelStarText')
  const saveStarTxt = t('chatView.saveStarText')
  // const upperLimitTxt = t('chatView.upperLimitText')
  if (isFavorited(id)) {
    mixpanel.favoriteChat(2)
    await services.post('/gpt/moveDialog', { dialogId: id, type: 2 })
    messageFn({
      [isPc.value ? 'title' : 'message']: cancelStarTxt,
    })
  }
  else {
    // 现在所有套餐都可以星标，取消判断
    // if (
    //   userInfoV2.value.levelLimitVo
    //   && userInfoV2.value?.levelLimitVo?.starGroupDialogUsed
    //   < userInfoV2.value?.levelLimitVo?.starGroupDialogLimit
    // ) {
    mixpanel.favoriteChat(1)
    await services.post('/gpt/moveDialog', { dialogId: id, type: 1 })
    messageFn({
      [isPc.value ? 'title' : 'message']: saveStarTxt,
    })

    if (isPc.value) {
      sideBar.value.select(1)
    }
    // }
    // else {
    //   messageFn({
    //     [isPc.value ? 'title' : 'message']: upperLimitTxt,
    //   })
    // }
  }
  groupId.value = null
  await getMessages()
  await refreshGroup({ id: messageId.value, status: 'OPEN' })
  await getStarGroup({ status: 'OPEN' })
  getInfo()
}

const shake = () => {
  shakeVisible.value = true
  setTimeout(() => {
    shakeVisible.value = false
  }, 1000)
}

const newMessage = (resetMode = true) => {
  mixpanel.createDialog()

  if (loading.value) {
    loading.value = false
  }
  if (ctrl.value) {
    ctrl.value.abort()
  }
  router.push({
    name: 'Chat',
  })
  shake()
  sceneVisible.value = false
  status.value = 'READY'
  messageId.value = 0
  sections.value = []
  contextId.value = ''
  sendBox.value.setFocus()
  setActiveResendModelId(null)
  if (resetMode) {
    resetModel()
  }
}
watch(
  () => deleteMessageTrigger.value.count,
  () => {
    deleteMessage({ id: deleteMessageTrigger.value.id })
  },
)

const deleteMessage = async ({ id }: any) => {
  if (loading.value) {
    loading.value = false
  }
  await services.post('/gpt/delDialog', { id })
  await getMessages()
  await refreshGroup({ id })
  getInfo()
  newMessage(false)
}

const deleteGroup = async ({ id }: any) => {
  if (loading.value) {
    loading.value = false
  }
  await services.post('/gpt/delGroup', { id })
  groups.value = groups.value.filter((element: any) => element.id !== id)
  getInfo()
}

const deleteChatsGroup = async (ids: number[]) => {
  if (loading.value) {
    loading.value = false
  }
  mixpanel.deleteChats()
  await services.post('/gpt/delDialogs', { ids })
  await getMessages()
  await refreshGroup({ id: messageId.value })
  getInfo()
  if (ids.includes(messageId.value)) {
    newMessage(false)
  }
}

const createGroup = async ({ name, id }: any) => {
  groups.value.splice(1, 0, {
    createTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    dialogCount: null,
    defaultFlag: 0,
    starFlag: 0,
    id,
    name,
  })
  getInfo()
}

watch(
  () => moveGroupTrigger.value.count,
  () => {
    moveGroup({ groupId: moveGroupTrigger.value.id })
  },
)
const moveGroup = async ({ groupId: _groupId, dialogId }: any) => {
  const targetDialogId = dialogId || messageId.value
  mixpanel.moveGroup(_groupId)
  await services.post('/gpt/moveDialog', {
    dialogId: targetDialogId,
    groupId: _groupId,
  })
  const messageFn: any = isPc.value ? ElNotification.success : createMessage
  const moveGroupTxt = t('chatView.moveGroupText')
  messageFn({
    [isPc.value ? 'title' : 'message']: moveGroupTxt,
  })
  sendControlBarSelect(0)
  groupId.value = _groupId
  await getMessages()
  await refreshGroup({ id: targetDialogId })
  await switchMessage({ groupId: _groupId, reload: true })
}

const onScene = (visible: boolean) => {
  sceneVisible.value = visible
}

const sceneChange = () => {
  input.value = ''
  sendBox.value.resize()
}

const isBatchLoading = ref(false) // 用于标识 batchLoadSections 函数是否正在执行

// 按参数batchSize的值往sections.value中一波一波的添加数据
const batchLoadSections = (array: any[], batchSize = 9) => {
  isBatchLoading.value = true
  let currentIndex = array.length - batchSize
  let isFirstBatch = true // 是否是第一波数据

  const loadNextBatch = () => {
    if (currentIndex < 0) {
      if (currentIndex + batchSize > 0) {
        const remainingBatch = array.slice(0, currentIndex + batchSize)
        sections.value = [...remainingBatch, ...sections.value]

        // 最后一批数据加载完成后 触发scrollBottom
        nextTick(() => {
          if (chatContent.value) {
            chatContent.value.scrollBottom()
          }
        })
      }
      isBatchLoading.value = false
      return
    }

    const batch = array.slice(currentIndex, currentIndex + batchSize)
    sections.value = [...batch, ...sections.value]
    currentIndex -= batchSize

    nextTick(() => {
      if (isFirstBatch && chatContent.value) {
        // 如果是第一波数据 触发scrollBottom
        chatContent.value.scrollBottom()
        isFirstBatch = false
      }
      setTimeout(() => {
        loadNextBatch()
      }, 100)
    })
  }
  loadNextBatch()
  appStore.triggerCodeCollapse()
}
const { isRealPc } = useDevice()
const selectMessage = async ({
  id,
  ttsLanguageTypeId,
  ttsType,
  modeId,
  contextId: _contextId,
  groupId: _groupId,
}: any) => {
  if (_groupId) {
    sideBar.value.select(1)
    await switchMessage({ id, groupId: _groupId, status: 'OPEN', reload: true })
  }

  if (loading.value) {
    loading.value = false
  }
  if (ctrl.value) {
    ctrl.value.abort()
  }
  router.push({
    name: 'Chat',
    params: {
      id: encode(id),
    },
  })
  sideBar.value.switchMenuBar({ modeId, ttsType })
  await fetchTools()
  sectionSkeletonVisible.value = false
  updateChatContentToolsLoading(false, '')
  messageId.value = id
  skeletonVisible.value = true
  const { records }: any = await services.post(
    '/gpt/chatRecords',
    {
      id,
      limit: 1000,
      page: 1,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  skeletonVisible.value = false
  input.value = ''
  uuid.value = ''
  voice.value = []
  sceneVisible.value = false
  sendBox.value.resize()
  selected.value = modeTransSelected(modeId, models.value) as unknown as number
  ttsId.value = ttsType
  mode.value = modeId

  contextId.value = _contextId
  groupId.value = _groupId
  languageId.value = ttsLanguageTypeId
  languageType.value = types.value
    .flatMap((element: any, index: number) =>
      element.detailVos.map((element: any) => ({ ...element, index })),
    )
    .find((element: any) => element.id === ttsLanguageTypeId)
    ?.index
  sendControlBarSelect(0)
  clearFormatedCache()

  // 初始化对话列表数组
  const array: any = []

  if (!records.length) {
    // 如果没有对话 去取MessageDB中的用户对话信息 因为报错异常对话不保存
    const requestId = generateUUID()
    const { id, content, file, uuid, thumbnailUrls, attachments } = await messageDB.getMessageById(messageId.value)
    if (id) {
      array.push(...[{
        requestId,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content,
        receiving: false,
        file,
        uuid,
        thumbnailUrls,
        attachments,
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      }, {
        requestId,
        avatar: avatar_gpt,
        content: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
        imgText: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
        receiving: true,
        retry: true,
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }])
      // 这里的按钮上面有个 data-IOcIq6iIsK 的「属性」，表示这个地方需要当做html解析显示在页面上，不加这个「属性」将会以文本显示
    }
  }

  // 获取正序的对话列表
  const reverseRecords = [...records].reverse()
  reverseRecords.forEach((element: any) => {
    const requestId = generateUUID()
    // 对话列表中属于子级对话（带有relationId）则跳过 在 发起重新提问的父级对话 添加children数组
    if (element.relationId) {
      return false
    }
    // 找到当前id所有包含relationId的子级对话 汇总为children数组
    const children = records
      .filter((child: any) => element.id === child.relationId)
      .map((element: any) => ({
        ...element,
        avatar: avatar_gpt,
        content: element.answer,
        requestId,
        receiving: true,
        file: element.responseFiles
          ? element.responseFiles.map((element: any) => ({
              ...element,
              content: element.chats,
            }))
          : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }))
    // 添加提问的对话
    // 额外处理GEMINI2.0历史记录
    if ([29].includes(selectedMode.value?.type || 0)) {
      const questions = JSON.parse(element.question)
      const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
      const questionsFiles = questions.filter((element: { inlineData: { uuid: string, data: string } }) => element?.inlineData)
      const thumbnailUrls = questionsFiles.map((element: { inlineData: { data: string } }) => element?.inlineData?.data)
      array.push({
        ...element,
        question: questionsText,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content: questionsText,
        img: '',
        attachments: [],
        requestId,
        receiving: false,
        thumbnailUrls,
        file: [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    else {
      array.push({
        ...element,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content: element.question,
        img: element.thumbnailUrls
          ? getFullImageUrl(element.thumbnailUrls[0])
          : '',
        attachments: JSON.parse(element.attachments),
        requestId,
        receiving: false,
        file: element.requestFile ? [element.requestFile] : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    // 对回答的对话数据做格式处理
    // 额外处理GEMINI2.0历史记录
    if ([29].includes(selectedMode.value?.type || 0)) {
      const questions = JSON.parse(element.question)
      const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
      const answer = JSON.parse(element.answer)
      const answerText = answer.map((element: { text: string }) => element.text)
      const inlineData = answer.find((element: { inlineData: { data: string } }) => element.inlineData)?.inlineData
      array.push({
        ...element,
        question: questionsText,
        avatar: avatar_gpt,
        content: answerText?.join(''),
        requestId,
        imgText: '',
        img: inlineData?.data ? getFullImageUrl(inlineData?.data) : '',
        receiving: true,
        file: [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    else {
      const object = {
        ...element,
        avatar: avatar_gpt,
        content: element.answer,
        requestId,
        imgText: element.answer.split(IMAGE_SPLIT)[1] || '',
        img: (element?.thumbnailUrls && Boolean(element?.thumbnailUrls[0]))
          ? getFullImageUrl(element.thumbnailUrls[0])
          : (element?.originalUrls && Boolean(element?.originalUrls[0]))
              ? getFullImageUrl(element.originalUrls[0])
              : '',
        receiving: true,
        file: element.responseFiles
          ? element.responseFiles.map((element: any) => ({
              ...element,
              content: element.chats,
            }))
          : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }

      // 添加回答的对话
      array.push({
        ...object,
        page: 1,
        // children中包含父级对话与子级重新提问对话 记录当前对话所有的重新提问
        children: [object, ...children],
      })
    }
  })
  sections.value = []
  array.forEach((i: any) => {
    i.unionid = generateUUID()
  })

  // 分批加载对话列表, 防止有的用户怼着一个对话用，加载特别卡
  batchLoadSections(array)
  // 如果模型为Flux或Flux Kontext Pro, 特殊处理 重新获取生成图片队列
  if (selectedMode.value && [MODE_TYPE.FLUXPRO, MODE_TYPE.FLUX_KONTEXT_PRO].includes(selectedMode.value?.type || 0)) {
    const dialogId = messageId.value
    sections.value.forEach(async (element: any, index: number) => {
      // FLUX模型 && 没有图片 && 属于回复内容
      if (!element?.thumbnailUrls?.length && element.receiving) {
        element.imgText = t('chatView.loading')
        const { answer, originalUrls, thumbnailUrls } = await getFluxRecords(
          element.id,
          dialogId,
        )
        if (dialogId !== messageId.value) {
          return
        }
        const object = {
          ...element,
          imgText: '',
          content: answer,
          answer,
          originalUrls,
          thumbnailUrls,
          img: thumbnailUrls ? getFullImageUrl(thumbnailUrls[0]) : '',
        }
        sections.value[index] = object
      }
    })
  }
  status.value = 'START'
  // 真的pc才设置焦点，移动端一切对话就弹个键盘体验不好
  if (isRealPc.value) {
    sendBox.value.setFocus()
  }
  nextTick(() => {
    initMermaidDiagrams()
  })
  scrollSction()
}

// async function sendO1({
//   prompt,
//   requestId,
// }: {
//   prompt: string
//   requestId: string
// }) {
//   const url = `${import.meta.env.VITE_APP_GPT_URL}/gpt/completions`
//   const params: any = {
//     prompt,
//     requestId,
//     modeId: mode.value,
//     dialogId: messageId.value,
//   }
//   if (showTool.value) {
//     params.tools = tools.value
//       .filter((tool: { isActive: boolean }) => tool.isActive)
//       .map((tool: { name: string, id: string }) => {
//         return {
//           name: tool.name,
//           id: tool.id,
//         }
//       })
//   }
//   return await services.post<GenerateImageModel, GenerateImageModel>(
//     url,
//     params,
//     {
//       headers: {
//         noninductive: true,
//       },
//     },
//   )
// }

async function sendE3({ prompt }: { prompt: string }) {
  const url = `${import.meta.env.VITE_APP_GPT_URL}/v1/images/generations`
  return await services.post<GenerateImageModel, GenerateImageModel>(
    url,
    {
      n: 1,
      prompt,
      size: size.value === '1:1' ? '1024x1024' : size.value === '7:4' ? '1792x1024' : '1024x1792',
      modeId: mode.value,
      dialogId: messageId.value,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
}

const editMessage = ({ messageId, messageName, type }: any) => {
  if (loading.value) {
    return
  }
  if (type === 'MESSAGE') {
    generalDialogs.value.map(async (element: any) => {
      if (messageId === element.id) {
        if (element.editing) {
          if (!messageName) {
            ElNotification.error({
              title: t('chatView.dialogPlaceholder').value,
            })
            return
          }
          await services.post('/gpt/updDialogName', {
            id: messageId,
            name: messageName,
          })
          element.name = messageName
          if (sendBox.value) {
            sendBox.value.setFocus()
          }
        }
        element.editing = !element.editing
      }
    })
  }
  else if (type === 'GROUP') {
    groups.value
      .flatMap((element: any) => element.array)
      .map(async (element: any) => {
        if (element && element.id === messageId) {
          if (element.editing) {
            if (!messageName) {
              ElNotification.error({
                title: t('chatView.dialogPlaceholder').value,
              })
              return
            }
            await services.post('/gpt/updDialogName', {
              id: messageId,
              name: messageName,
            })
            element.name = messageName
            if (sendBox.value) {
              sendBox.value.setFocus()
            }
          }
          element.editing = !element.editing
        }
      })
  }
}

const blurMessage = ({ messageId, type }: any) => {
  if (type === 'MESSAGE') {
    generalDialogs.value.map(async (element: any) => {
      if (messageId === element.id) {
        element.editing = !element.editing
      }
    })
  }
  else if (type === 'GROUP') {
    groups.value
      .flatMap((element: any) => element.array)
      .map(async (element: any) => {
        if (element && element.id === messageId) {
          element.editing = !element.editing
        }
      })
  }
}

const onBrekChat = () => {
  updateChatContentToolsLoading(false, '')

  loading.value = false
  llmStreaming.value = false
  if (ctrl.value) {
    ctrl.value.abort()
  }
  sectionSkeletonVisible.value = false
  ThinkingMarkdownProcessor.stop()
}

const onResend = (item: any, resendModelId?: number, retryChatId?: number) => {
  loading.value = false
  input.value = item.question
  onEnter({ relationId: item.id, uuid: item.uuid, resendModelId, retryChatId }, true)
}

// 发生异常后重试，非重新提问 无relationId
const onRetry = async (item: any) => {
  // 手动重试，避免重复显示原消息
  const { content } = await messageDB.getMessageById(messageId.value)
  sections.value.pop()
  sections.value.forEach((item: any, index: number) => {
    if (item.content === content) {
      sections.value.splice(index, 1)
    }
  })

  loading.value = false
  input.value = item.content
  onEnter({ uuid: item.uuid, thumbnailUrls: item.thumbnailUrls, attachments: item.attachments })
}

const storedMode = useStorage('selected-model-mode', DefaultModelId)

const init = async () => {
  await getGroups() // 获取分组
  await getMessages() // 获取常规对话

  await getStarGroup({})
  await modelDataInit()
  models.value = await services.get('/gpt/modes', {
    headers: {
      noninductive: true,
    },
  })
  types.value = await services.get('/tts/types')
  mode.value = storedMode.value || 20
  if (route.params.id) {
    const target = dialogs.value.find(
      (element: any) => String(element.id) === decode(String(route.params.id)),
    )
    target && target.id && sideBar.value.selectMessage(target)
  }
  if (route.query.scene === 'member') {
    upgrade()
  }
}

// 获取v2版本模型数据
async function modelDataInit() {
  const data: any = await services.get('/gpt/v2/models', {
    headers: {
      noninductive: true,
    },
  })
  modelData.value = data
}

// Initialize all mermaid diagrams by clicking render buttons
const initMermaidDiagrams = () => {
  const { mermaid } = toolStatus('mermaid')
  // Use setTimeout to ensure DOM is ready
  if (mermaid) {
    const renderMermaidElements = document.querySelectorAll('.render-mermaid')
    renderMermaidElements.forEach((element) => {
      (element as HTMLElement).click()
    })
  }
}

onMounted(() => {
  sendBox.value.setFocus()
  init()

  messageDB.init()
  headerBarStore.setHeaderBar(headerBar.value)
})
watchEffect(() => {
  chatViewData.value.messageId = messageId.value
})
watchEffect(() => {
  chatViewData.value.groups = groups.value
})

watchEffect(() => {
  chatViewData.value.sections = sections.value
  currentChatAttachment.value = sections.value
    .filter((i: any) => !i.receiving)
    .map((i: any) => i.attachments)
    .filter(Boolean)
    .flat(Infinity)
})
watchEffect(() => {
  chatViewData.value.groupId = groupId.value
})
watchEffect(() => {
  chatViewData.value.chatContent = chatContent.value
})
watchEffect(() => {
  chatViewData.value.status = status.value
})
watchEffect(() => {
  chatViewData.value.messageName = generalDialogs.value.find(
    (element: any) => element.id === messageId.value,
  )?.name ?? ''
})

// 节流后的newMessage函数
const throttledNewMessage = throttle(() => {
  newMessage(false)
}, 1500)
// 更新Artifact的HTML内容
const updateArtifactHtml = (artifactId: number, newHtml: string) => {
  if (!artifactId || !newHtml) {
    return
  }

  // 遍历sections找到receiving为true的项
  sections.value.forEach((item: any) => {
    if (item.receiving && item.artifactsList) {
      // 在artifactsList中找到对应id的项
      const artifactIndex = item.artifactsList.findIndex(
        (artifact: any) => artifact.id === artifactId,
      )
      if (artifactIndex !== -1) {
        try {
          // 解析artifacts字符串为对象
          const artifactsObj = JSON.parse(item.artifactsList[artifactIndex].artifacts)
          // 更新html内容
          artifactsObj.html = newHtml
          // 将对象转回字符串并更新
          item.artifactsList[artifactIndex].artifacts = JSON.stringify(artifactsObj)

          // 更新currentArtifacts，如果当前显示的就是这个artifact
          if (currentArtifacts.value?.id === artifactId) {
            currentArtifacts.value = {
              ...currentArtifacts.value,
              html: newHtml,
            }
          }
        }
        catch (error) {
          console.error('解析artifacts JSON失败:', error)
        }
      }
    }
  })
}

provideChatView({ createLeptonDialog, onAssistantMessage, onReceiveDone, updateArtifactHtml })
const sendBoxFocus = ref(false)

const onChatViewClick = (e: MouseEvent) => {
  const codeWrapper = (e.target as HTMLElement).closest('.code-wrapper')
  codeWrapper && codeWrapper.classList.toggle('open')
}

// 对话窗口id变化/对话状态变化时，关闭当前组件并重新初始化
watch(
  [
    () => chatViewData.value.messageId,
    () => chatViewData.value.status,
  ],
  () => {
    chatContent.value?.closeArtifactsCode()
    isResizablePanelVisible.value = false
  },
)

onUnmounted(() => {
  ThinkingMarkdownProcessor.destory()
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/chat-view";
</style>

<style lang="scss">
.hidden-thinking-time-functional {
  .juchats-thinking {
    display: none !important;
  }
}
</style>
