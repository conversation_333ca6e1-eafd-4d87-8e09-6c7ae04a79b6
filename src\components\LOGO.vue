<template>
  <h1 class="overflow-hidden text-[0px]">
    <PERSON><PERSON><PERSON>
    <svg class="h-34px" :class="svgClass" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 174 34.5">
      <path
        d="M8.1 34c-1.3 0-2.5-.1-3.7-.3s-2.2-.5-3.2-.8l1.4-6.5c.7.3 1.5.6 2.4.7.9.2 1.9.3 2.9.3 1.4 0 2.6-.2 3.4-.5s1.4-.9 1.9-1.6c.4-.7.8-1.7 1-2.8l4.2-19.9h7.7L21.6 24c-.7 3.2-2.1 5.7-4.2 7.4S12.2 34 8.1 34zm24.4 0c-2.5 0-4.4-.8-5.6-2.4-1.3-1.6-1.6-3.8-1-6.6l3-14.3h7.7l-2.9 13.9c-.3 1.3-.2 2.2.2 2.8s1.3.9 2.7.9c.9 0 1.8-.2 2.8-.6s1.7-.9 2.4-1.4L45 10.7h7.7L48 33.5h-7.2l.1-1.9c-1.1.7-2.3 1.3-3.7 1.7s-3 .7-4.7.7z"
        fill="#f65134"
      />
      <path
        :fill="calcChatStrColor"
        d="M64.7 34c-2.4 0-4.5-.4-6.3-1.3s-3-2.2-3.8-4-.9-4-.3-6.6c.8-4 2.6-7 5.2-8.9 2.6-2 6.1-2.9 10.4-2.9 1.7 0 3.1.1 4.3.4 1.2.2 2.3.6 3.4 1.2l-1.2 5.5c-1.6-.9-3.6-1.3-6-1.3-2.2 0-4 .4-5.4 1.3s-2.3 2.5-2.8 4.8c-.4 2.1-.3 3.7.5 4.7s2.4 1.5 5.1 1.5c2.3 0 4.6-.5 6.7-1.4l-1.2 5.7c-1.3.5-2.6.9-4 1.1-1.5.1-3 .2-4.6.2zm12.2-.5l6.9-32.4h7.7l-2.4 11.3c1-.6 2.2-1.2 3.5-1.6 1.4-.4 2.8-.6 4.2-.6 2.8 0 4.8.7 6.1 2.2 1.2 1.4 1.5 3.6.9 6.6l-3 14.4h-7.7L96 19.6c.3-1.3.2-2.2-.1-2.8-.4-.6-1.4-.8-2.9-.8-.8 0-1.7.2-2.7.6-.9.4-1.8.9-2.5 1.4l-3.3 15.5h-7.6zm36.9.5c-3.1 0-5.4-.9-6.9-2.7-1.4-1.8-1.8-4.6-1-8.2.5-2.5 1.4-4.7 2.6-6.6s2.8-3.4 4.7-4.5 4.1-1.6 6.6-1.6c1.3 0 2.4.2 3.2.5s1.6.7 2.3 1.3l.2-1.3h7.7l-4.8 22.8h-6.7v-1.9c-2.2 1.4-4.8 2.2-7.9 2.2zm3.7-5.6c1.7 0 3.3-.6 4.7-1.7l1.9-9.1c-.6-1.1-1.8-1.6-3.4-1.6-1.8 0-3.3.6-4.4 1.7s-2 2.9-2.5 5.4c-.4 2-.3 3.4.2 4.2.6.7 1.8 1.1 3.5 1.1zm25.2 5.6c-2.6 0-4.4-.7-5.6-2.2-1.1-1.4-1.4-3.4-.9-6l2-9.3h-3.1l1.2-5.9h3.1l1-4.8 8.2-2.1-1.4 6.8h5.5l-1.6 5.9h-5.2l-1.8 8.7c-.2 1.1-.2 1.8.1 2.3.3.4 1.1.6 2.4.6 1 0 2-.2 3.1-.5l-1.1 5.2c-1.7.9-3.7 1.3-5.9 1.3zm17 0c-1.8 0-3.5-.1-5.1-.4s-2.8-.6-3.7-1l1.2-5.9c1 .5 2.2.8 3.7 1.1 1.4.3 2.7.4 3.9.4 1.4 0 2.4-.1 3-.2s1-.5 1.2-1.1-.2-1.1-1.2-1.4-2.3-.8-3.9-1.4c-2-.7-3.4-1.6-4.2-2.6s-1.1-2.4-.6-4.3c.4-2.2 1.6-4 3.4-5.2s4.7-1.8 8.6-1.8c1.4 0 2.8.1 4.2.3s2.5.5 3.3.8l-1.2 5.8c-.8-.4-1.8-.7-2.9-.9s-2.1-.3-3-.3c-1.2 0-2.2.1-3 .2s-1.3.5-1.4 1c-.1.6.2 1 1.1 1.3l3.6 1.2c1.7.5 2.9 1.1 3.8 1.8s1.4 1.4 1.7 2.2c.2.9.2 2-.1 3.3-.5 2.3-1.7 4-3.6 5.2s-5 1.9-8.8 1.9z"
      />
    </svg>
  </h1>
</template>

<script setup lang="ts">
const props = defineProps({
  fillColor: {
    type: String,
    default: '',
  },
  svgClass: {
    type: String,
    default: '',
  },
})
const { isDark } = useThemeChange()
const calcChatStrColor = computed(() => {
  return props.fillColor || (isDark.value ? '#fff' : '#000')
})
</script>

<style lang="scss" scoped></style>
