<template>
  <template v-if="isPc">
    <SideBar
      ref="sideBar" :groups="groups" :messages="generalDialogs" :loading="loading" :message-id="messageId"
      :get-flag="getFlag" :models="models" @new="throttledNewMessage" @edit="editMessage" @blur="blurMessage"
      @delete-group="deleteGroup" @delete="deleteMessage" @delete-chats="deleteChatsGroup" @select="selectMessage"
      @switch="switchMessage" @grouping="createGroup" @reset-title="resetTitle" @move-group="moveGroup"
    ></SideBar>
  </template>
  <template v-else>
    <Teleport to="body">
      <SideBar
        ref="sideBar" :groups="groups" :messages="generalDialogs" :loading="loading" :message-id="messageId"
        :get-flag="getFlag" :models="models" @new="newMessage(false)" @edit="editMessage" @blur="blurMessage"
        @delete-group="deleteGroup" @delete="deleteMessage" @delete-chats="deleteChatsGroup" @select="selectMessage"
        @switch="switchMessage" @grouping="createGroup"
      ></SideBar>
      <div
        v-if="expandSidebar"
        class="fixed inset-0 z-15 h-full w-full bg-[var(--mobile-sidebar-shadow-bg)] backdrop-blur-[3px]" @click="() => {
          expandSidebar = false;
        }
        "
      ></div>
    </Teleport>
  </template>
</template>

<script setup lang="ts">
import { toRefs, useStorage } from '@vueuse/core'
import { decode, encode } from 'base-64'
import { ElNotification } from 'element-plus'
import { throttle } from 'lodash'
import moment from 'moment'
import avatar_gpt_dark from '@/assets/images/ai-avatar-dark.svg'
import avatar_gpt_light from '@/assets/images/ai-avatar-light.svg'
import avatar_default from '@/assets/images/user-default-avatar.svg'
import {
  generateUUID,
  getFlag,
  modeTransSelected,
  upgrade,
} from '@/common'
import services from '@/common/axios'
import { DefaultModelId } from '@/common/constant'
import { clearFormatedCache } from '@/common/marked'
import messageDB from '@/common/message'
import { getFullImageUrl, notify, ThinkingMarkdownProcessor } from '@/common/tools'
import { mixpanel } from '@/config/mixpanel'
import { IMAGE_SPLIT, MODE_TYPE } from '@/enum'
import { useDevice } from '@/hooks/useDevice'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { createMessage } from '@/hooks/useMobildMessage/useMobileMessage'
import { useAppStore } from '@/stores/app'
import { useChatStore } from '@/stores/chat'
import { useHeaderBarStore } from '@/stores/headerBar'
import { useLangStore } from '@/stores/i18n'
import { useTools } from '@/stores/tools'
import { useUserStore } from '@/stores/user'

const t = useGlobalI18n()
const { isDark } = useThemeChange()
const avatar_gpt = computed(() =>
  isDark.value ? avatar_gpt_light : avatar_gpt_dark,
)

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const headerBarStore = useHeaderBarStore()
const { fetchTools, toolStatus } = useTools()

const { userInfoV2 } = storeToRefs(userStore)
const {
  expandSidebar,
  isPc,
  restartTrigger,
  newMessageTrigger,
  upgradePackageTrigger,
  isResizablePanelVisible,
}
  = storeToRefs(useAppStore())
const { setActiveResendModelId } = useAppStore()
const appStore = useAppStore()

// 监听升级套餐触发器
watch(
  () => upgradePackageTrigger.value.count,
  () => {
    if (sideBar.value && sideBar.value.upgrade) {
      sideBar.value.upgrade()
    }
  },
)

watch(
  () => restartTrigger.value,
  () => {
    onBrekChat()
  },
)

watch(
  () => newMessageTrigger.value,
  () => {
    // 如果当前已经是一个新的对话，则不执行「新建对话」
    if (chatViewData.value.status === 'READY') {
      return
    }
    newMessage(false)
  },
)

const loading = ref(false)
const llmStreaming = ref(false)

const groupId: any = ref(null)
const ctrl = ref()
const {
  chatViewData,
  selectedMode,
  deleteMessageTrigger,
  favoriteMessageTrigger,
  moveGroupTrigger,
  modelSelectInfo,
  modelData,
  currentChatAttachment,
} = storeToRefs(useAppStore())
// 这里的toRefs是vueuse提供的而非vue提供，旨在解构ref的modelSelectInfo
const {
  selected,
  mode,
  ttsId,
  languageId,
  languageType,
  types,
  models,
} = toRefs(modelSelectInfo)
const { sendControlBarSelect, resetModel } = useAppStore()
const chatStore = useChatStore()
const { generalDialogs, dialogs } = toRefs(chatStore)
const status = ref('READY')
const uuid = ref('')
const voice = ref([])
const input = ref('')
const sections: any = ref([])
const messageId = ref(0)

const contextId: any = ref(null)
const groups: any = ref([])
const sideBar = ref()
const headerBar = ref()
const sendBox = ref()
const sceneVisible = ref(false)
const shakeVisible = ref(false)
const chatContent = ref()
const enableScroll = ref(true)
const skeletonVisible = ref(false)
const sectionSkeletonVisible = ref(false)

const switchMessage = async ({ groupId, reload, status }: any) => {
  groups.value.map(
    async (element: { id: number, array: object[], visible: boolean }) => {
      if (groupId === element.id) {
        if (!element.array || reload) {
          const array: any = await services.post(
            '/gpt/dialogs',
            {
              id: groupId,
            },
            {
              headers: {
                noninductive: true,
              },
            },
          )
          element.array = array.reverse().map((element: any) => ({
            ...element,
            editing: false,
          }))
          chatStore.appendDialogs(array)
        }
        if (['OPEN'].includes(status)) {
          element.visible = true
        }
        else if (['CLOSE'].includes(status)) {
          element.visible = false
        }
        if (!reload && !status) {
          element.visible = !element.visible
        }
      }
    },
  )
}

const getStarGroup = async ({ status }: any) => {
  const groupId = groups.value.find((element: any) => element.starFlag).id
  await switchMessage({ groupId, reload: true, status })
}

const getGroups = async () => {
  const groupsData: any = await services.get('/gpt/groups', {
    headers: {
      noninductive: true,
    },
  })
  const array = groupsData.map((element: any) => {
    const item = {
      ...element,
      defaultFlag: 0,
      groupType: 'startGroup',
      visible: false,
    }

    if (item.name === '星标对话') {
      item.name = t('sideBar.starDialog')
    }
    else if (item.name === '默认分组') {
      item.name = t('sideBar.defaultGroup')
    }

    return item
  })
  array.push({
    createTime: '',
    defaultFlag: 1,
    groupType: 'scenceGroup',
    dialogCount: null,
    id: 0,
    name: t('sideBar.groupDialog'),
    starFlag: 0,
    array: [],
    visible: true,
  })
  groups.value = array
}

const getMessages = async () => {
  await chatStore.loadDialogs()
}

const scrollSction = () => {
  if (!enableScroll.value) {
    return
  }
  const interval = setInterval(() => {
    if (chatContent.value) {
      chatContent.value.scrollBottom()
      clearInterval(interval)
    }
  })
}

const getInfo = () => {
  if (headerBar.value?.getInfo) {
    headerBar.value.getInfo()
  }
}

async function getFluxRecords(id: any, dialogId: any) {
  if (dialogId !== messageId.value) {
    throw new Error('IS_NOT_CURRENT_DIALOG')
  }
  let res: any = await services.post(
    '/gpt/chatRecord',
    {
      id,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  if (
    res.createTime
    && moment.duration(moment().diff(moment(res.createTime))).asMinutes() > 10
  ) {
    throw new Error('Time out')
  }
  const resultArr = res?.thumbnailUrls
  if (!resultArr?.length || resultArr[0].startsWith('https')) {
    await new Promise(resolve => setTimeout(resolve, 5000))
    res = await getFluxRecords(id, dialogId)
  }
  return res
}

const langStore = useLangStore()
const { language } = storeToRefs(langStore)

const onReceiveDone = async (id?: string, force = false) => {
  try {
    // 修改message标题
    if (sections.value.length <= 2 || force) {
      const title: string = await services.post(
        `gpt/generateDialogTitle`,
        {
          id: id || messageId.value,
          language: language.value === 'zhCn' ? '' : 'english',
        },
        {
          baseURL: '/gw/chatgpt',
        },
      )
      let currentText = ''
      for (const char of title) {
        currentText += char
        generalDialogs.value.forEach((item: any) => {
          if (item.id === messageId.value) {
            item.name = currentText
          }
        })
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
  }
  catch {
  }
}

// 重置标题
function resetTitle(id: string) {
  if (!id) {
    return
  }
  notify.success({
    title: t('sideBar.resetTitleEmitSuccess').value,
  })
  onReceiveDone(id, true)
}

// 用于更新聊天内容后的工具调用动画
function updateChatContentToolsLoading(showAnimation: boolean, toolName: string) {
  if (chatContent.value) {
    chatContent.value.showSharkAnimation = showAnimation
    chatContent.value.currentToolName = toolName
  }
}

const refreshGroup = async ({ id, status }: any) => {
  const groupId = groups.value
    .flatMap((element: any) => element.array)
    .find((element: any) => element && element.id === id)
    ?.groupId
  await switchMessage({ groupId, reload: true, status })
}

const isFavorited = (id: any) => {
  return groups.value
    .find((element: any) => element.starFlag)
    .array
    .find((element: any) => element.id === id)
}

watch(
  () => favoriteMessageTrigger.value.count,
  () => {
    favoriteMessage(favoriteMessageTrigger.value.id)
  },
)
const favoriteMessage = async (id: number | undefined) => {
  if (!id) {
    return
  }
  const messageFn: any = isPc.value ? ElNotification.success : createMessage
  const cancelStarTxt = t('chatView.cancelStarText')
  const saveStarTxt = t('chatView.saveStarText')
  // const upperLimitTxt = t('chatView.upperLimitText')
  if (isFavorited(id)) {
    mixpanel.favoriteChat(2)
    await services.post('/gpt/moveDialog', { dialogId: id, type: 2 })
    messageFn({
      [isPc.value ? 'title' : 'message']: cancelStarTxt,
    })
  }
  else {
    // 现在所有套餐都可以星标，取消判断
    // if (
    //   userInfoV2.value.levelLimitVo
    //   && userInfoV2.value?.levelLimitVo?.starGroupDialogUsed
    //   < userInfoV2.value?.levelLimitVo?.starGroupDialogLimit
    // ) {
    mixpanel.favoriteChat(1)
    await services.post('/gpt/moveDialog', { dialogId: id, type: 1 })
    messageFn({
      [isPc.value ? 'title' : 'message']: saveStarTxt,
    })

    if (isPc.value) {
      sideBar.value.select(1)
    }
    // }
    // else {
    //   messageFn({
    //     [isPc.value ? 'title' : 'message']: upperLimitTxt,
    //   })
    // }
  }
  groupId.value = null
  await getMessages()
  await refreshGroup({ id: messageId.value, status: 'OPEN' })
  await getStarGroup({ status: 'OPEN' })
  getInfo()
}

const shake = () => {
  shakeVisible.value = true
  setTimeout(() => {
    shakeVisible.value = false
  }, 1000)
}

const newMessage = (resetMode = true) => {
  mixpanel.createDialog()

  if (loading.value) {
    loading.value = false
  }
  if (ctrl.value) {
    ctrl.value.abort()
  }
  router.push({
    name: 'Chat',
  })
  shake()
  sceneVisible.value = false
  status.value = 'READY'
  messageId.value = 0
  sections.value = []
  contextId.value = ''
  sendBox.value.setFocus()
  setActiveResendModelId(null)
  if (resetMode) {
    resetModel()
  }
}
watch(
  () => deleteMessageTrigger.value.count,
  () => {
    deleteMessage({ id: deleteMessageTrigger.value.id })
  },
)

const deleteMessage = async ({ id }: any) => {
  if (loading.value) {
    loading.value = false
  }
  await services.post('/gpt/delDialog', { id })
  await getMessages()
  await refreshGroup({ id })
  getInfo()
  newMessage(false)
}

const deleteGroup = async ({ id }: any) => {
  if (loading.value) {
    loading.value = false
  }
  await services.post('/gpt/delGroup', { id })
  groups.value = groups.value.filter((element: any) => element.id !== id)
  getInfo()
}

const deleteChatsGroup = async (ids: number[]) => {
  if (loading.value) {
    loading.value = false
  }
  mixpanel.deleteChats()
  await services.post('/gpt/delDialogs', { ids })
  await getMessages()
  await refreshGroup({ id: messageId.value })
  getInfo()
  if (ids.includes(messageId.value)) {
    newMessage(false)
  }
}

const createGroup = async ({ name, id }: any) => {
  groups.value.splice(1, 0, {
    createTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    dialogCount: null,
    defaultFlag: 0,
    starFlag: 0,
    id,
    name,
  })
  getInfo()
}

watch(
  () => moveGroupTrigger.value.count,
  () => {
    moveGroup({ groupId: moveGroupTrigger.value.id })
  },
)
const moveGroup = async ({ groupId: _groupId, dialogId }: any) => {
  const targetDialogId = dialogId || messageId.value
  mixpanel.moveGroup(_groupId)
  await services.post('/gpt/moveDialog', {
    dialogId: targetDialogId,
    groupId: _groupId,
  })
  const messageFn: any = isPc.value ? ElNotification.success : createMessage
  const moveGroupTxt = t('chatView.moveGroupText')
  messageFn({
    [isPc.value ? 'title' : 'message']: moveGroupTxt,
  })
  sendControlBarSelect(0)
  groupId.value = _groupId
  await getMessages()
  await refreshGroup({ id: targetDialogId })
  await switchMessage({ groupId: _groupId, reload: true })
}

const isBatchLoading = ref(false) // 用于标识 batchLoadSections 函数是否正在执行

// 按参数batchSize的值往sections.value中一波一波的添加数据
const batchLoadSections = (array: any[], batchSize = 9) => {
  isBatchLoading.value = true
  let currentIndex = array.length - batchSize
  let isFirstBatch = true // 是否是第一波数据

  const loadNextBatch = () => {
    if (currentIndex < 0) {
      if (currentIndex + batchSize > 0) {
        const remainingBatch = array.slice(0, currentIndex + batchSize)
        sections.value = [...remainingBatch, ...sections.value]

        // 最后一批数据加载完成后 触发scrollBottom
        nextTick(() => {
          if (chatContent.value) {
            chatContent.value.scrollBottom()
          }
        })
      }
      isBatchLoading.value = false
      return
    }

    const batch = array.slice(currentIndex, currentIndex + batchSize)
    sections.value = [...batch, ...sections.value]
    currentIndex -= batchSize

    nextTick(() => {
      if (isFirstBatch && chatContent.value) {
        // 如果是第一波数据 触发scrollBottom
        chatContent.value.scrollBottom()
        isFirstBatch = false
      }
      setTimeout(() => {
        loadNextBatch()
      }, 100)
    })
  }
  loadNextBatch()
  appStore.triggerCodeCollapse()
}
const { isRealPc } = useDevice()
const selectMessage = async ({
  id,
  ttsLanguageTypeId,
  ttsType,
  modeId,
  contextId: _contextId,
  groupId: _groupId,
}: any) => {
  if (_groupId) {
    sideBar.value.select(1)
    await switchMessage({ id, groupId: _groupId, status: 'OPEN', reload: true })
  }

  if (loading.value) {
    loading.value = false
  }
  if (ctrl.value) {
    ctrl.value.abort()
  }
  router.push({
    name: 'Chat',
    params: {
      id: encode(id),
    },
  })
  sideBar.value.switchMenuBar({ modeId, ttsType })
  await fetchTools()
  sectionSkeletonVisible.value = false
  updateChatContentToolsLoading(false, '')
  messageId.value = id
  skeletonVisible.value = true
  const { records }: any = await services.post(
    '/gpt/chatRecords',
    {
      id,
      limit: 1000,
      page: 1,
    },
    {
      headers: {
        noninductive: true,
      },
    },
  )
  skeletonVisible.value = false
  input.value = ''
  uuid.value = ''
  voice.value = []
  sceneVisible.value = false
  sendBox.value.resize()
  selected.value = modeTransSelected(modeId, models.value)
  ttsId.value = ttsType
  mode.value = modeId

  contextId.value = _contextId
  groupId.value = _groupId
  languageId.value = ttsLanguageTypeId
  languageType.value = types.value
    .flatMap((element: any, index: number) =>
      element.detailVos.map((element: any) => ({ ...element, index })),
    )
    .find((element: any) => element.id === ttsLanguageTypeId)
    ?.index
  sendControlBarSelect(0)
  clearFormatedCache()

  // 初始化对话列表数组
  const array: any = []

  if (!records.length) {
    // 如果没有对话 去取MessageDB中的用户对话信息 因为报错异常对话不保存
    const requestId = generateUUID()
    const { id, content, file, uuid, thumbnailUrls, attachments } = await messageDB.getMessageById(messageId.value)
    if (id) {
      array.push(...[{
        requestId,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content,
        receiving: false,
        file,
        uuid,
        thumbnailUrls,
        attachments,
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      }, {
        requestId,
        avatar: avatar_gpt,
        content: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
        imgText: `${t('chatContent.errorText').value}，<span data-IOcIq6iIsK class="text-destructive-200 cursor-pointer retry-button">${t('chatContent.errorButton').value}</span>`,
        receiving: true,
        retry: true,
        date: moment(new Date()).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }])
      // 这里的按钮上面有个 data-IOcIq6iIsK 的「属性」，表示这个地方需要当做html解析显示在页面上，不加这个「属性」将会以文本显示
    }
  }

  // 获取正序的对话列表
  const reverseRecords = [...records].reverse()
  reverseRecords.forEach((element: any) => {
    const requestId = generateUUID()
    // 对话列表中属于子级对话（带有relationId）则跳过 在 发起重新提问的父级对话 添加children数组
    if (element.relationId) {
      return false
    }
    // 找到当前id所有包含relationId的子级对话 汇总为children数组
    const children = records
      .filter((child: any) => element.id === child.relationId)
      .map((element: any) => ({
        ...element,
        avatar: avatar_gpt,
        content: element.answer,
        requestId,
        receiving: true,
        file: element.responseFiles
          ? element.responseFiles.map((element: any) => ({
              ...element,
              content: element.chats,
            }))
          : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }))
    // 添加提问的对话
    // 额外处理GEMINI2.0历史记录
    if ([29].includes(selectedMode.value?.type || 0)) {
      const questions = JSON.parse(element.question)
      const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
      const questionsFiles = questions.filter((element: { inlineData: { uuid: string, data: string } }) => element?.inlineData)
      const thumbnailUrls = questionsFiles.map((element: { inlineData: { data: string } }) => element?.inlineData?.data)
      array.push({
        ...element,
        question: questionsText,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content: questionsText,
        img: '',
        attachments: [],
        requestId,
        receiving: false,
        thumbnailUrls,
        file: [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    else {
      array.push({
        ...element,
        avatar: userInfoV2.value.thumbnailUrl || avatar_default,
        content: element.question,
        img: element.thumbnailUrls
          ? getFullImageUrl(element.thumbnailUrls[0])
          : '',
        attachments: JSON.parse(element.attachments),
        requestId,
        receiving: false,
        file: element.requestFile ? [element.requestFile] : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    // 对回答的对话数据做格式处理
    // 额外处理GEMINI2.0历史记录
    if ([29].includes(selectedMode.value?.type || 0)) {
      const questions = JSON.parse(element.question)
      const questionsText = questions.filter((element: { text: string }) => element.text)[0]?.text || ''
      const answer = JSON.parse(element.answer)
      const answerText = answer.map((element: { text: string }) => element.text)
      const inlineData = answer.find((element: { inlineData: { data: string } }) => element.inlineData)?.inlineData
      array.push({
        ...element,
        question: questionsText,
        avatar: avatar_gpt,
        content: answerText?.join(''),
        requestId,
        imgText: '',
        img: inlineData?.data ? getFullImageUrl(inlineData?.data) : '',
        receiving: true,
        file: [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: userInfoV2.value.name,
      })
    }
    else {
      const object = {
        ...element,
        avatar: avatar_gpt,
        content: element.answer,
        requestId,
        imgText: element.answer.split(IMAGE_SPLIT)[1] || '',
        img: (element?.thumbnailUrls && Boolean(element?.thumbnailUrls[0]))
          ? getFullImageUrl(element.thumbnailUrls[0])
          : (element?.originalUrls && Boolean(element?.originalUrls[0]))
              ? getFullImageUrl(element.originalUrls[0])
              : '',
        receiving: true,
        file: element.responseFiles
          ? element.responseFiles.map((element: any) => ({
              ...element,
              content: element.chats,
            }))
          : [],
        date: moment(element.createTime).format('YYYY.MM.DD HH:mm'),
        nickName: 'Juchats',
      }

      // 添加回答的对话
      array.push({
        ...object,
        page: 1,
        // children中包含父级对话与子级重新提问对话 记录当前对话所有的重新提问
        children: [object, ...children],
      })
    }
  })
  sections.value = []
  array.forEach((i: any) => {
    i.unionid = generateUUID()
  })

  // 分批加载对话列表, 防止有的用户怼着一个对话用，加载特别卡
  batchLoadSections(array)
  // 如果模型为Flux或Flux Kontext Pro, 特殊处理 重新获取生成图片队列
  if (selectedMode.value && [MODE_TYPE.FLUXPRO, MODE_TYPE.FLUX_KONTEXT_PRO].includes(selectedMode.value?.type || 0)) {
    const dialogId = messageId.value
    sections.value.forEach(async (element: any, index: number) => {
      // FLUX模型 && 没有图片 && 属于回复内容
      if (!element?.thumbnailUrls?.length && element.receiving) {
        element.imgText = t('chatView.loading')
        const { answer, originalUrls, thumbnailUrls } = await getFluxRecords(
          element.id,
          dialogId,
        )
        if (dialogId !== messageId.value) {
          return
        }
        const object = {
          ...element,
          imgText: '',
          content: answer,
          answer,
          originalUrls,
          thumbnailUrls,
          img: thumbnailUrls ? getFullImageUrl(thumbnailUrls[0]) : '',
        }
        sections.value[index] = object
      }
    })
  }
  status.value = 'START'
  // 真的pc才设置焦点，移动端一切对话就弹个键盘体验不好
  if (isRealPc.value) {
    sendBox.value.setFocus()
  }
  nextTick(() => {
    initMermaidDiagrams()
  })
  scrollSction()
}

const editMessage = ({ messageId, messageName, type }: any) => {
  if (loading.value) {
    return
  }
  if (type === 'MESSAGE') {
    generalDialogs.value.map(async (element: any) => {
      if (messageId === element.id) {
        if (element.editing) {
          if (!messageName) {
            ElNotification.error({
              title: t('chatView.dialogPlaceholder').value,
            })
            return
          }
          await services.post('/gpt/updDialogName', {
            id: messageId,
            name: messageName,
          })
          element.name = messageName
          if (sendBox.value) {
            sendBox.value.setFocus()
          }
        }
        element.editing = !element.editing
      }
    })
  }
  else if (type === 'GROUP') {
    groups.value
      .flatMap((element: any) => element.array)
      .map(async (element: any) => {
        if (element && element.id === messageId) {
          if (element.editing) {
            if (!messageName) {
              ElNotification.error({
                title: t('chatView.dialogPlaceholder').value,
              })
              return
            }
            await services.post('/gpt/updDialogName', {
              id: messageId,
              name: messageName,
            })
            element.name = messageName
            if (sendBox.value) {
              sendBox.value.setFocus()
            }
          }
          element.editing = !element.editing
        }
      })
  }
}

const blurMessage = ({ messageId, type }: any) => {
  if (type === 'MESSAGE') {
    generalDialogs.value.map(async (element: any) => {
      if (messageId === element.id) {
        element.editing = !element.editing
      }
    })
  }
  else if (type === 'GROUP') {
    groups.value
      .flatMap((element: any) => element.array)
      .map(async (element: any) => {
        if (element && element.id === messageId) {
          element.editing = !element.editing
        }
      })
  }
}

const onBrekChat = () => {
  updateChatContentToolsLoading(false, '')

  loading.value = false
  llmStreaming.value = false
  if (ctrl.value) {
    ctrl.value.abort()
  }
  sectionSkeletonVisible.value = false
  ThinkingMarkdownProcessor.stop()
}

const storedMode = useStorage('selected-model-mode', DefaultModelId)

const init = async () => {
  await getGroups() // 获取分组
  await getMessages() // 获取常规对话

  await getStarGroup({})
  await modelDataInit()
  models.value = await services.get('/gpt/modes', {
    headers: {
      noninductive: true,
    },
  })
  types.value = await services.get('/tts/types')
  mode.value = storedMode.value || 36
  if (route.params.id) {
    const target = dialogs.value.find(
      (element: any) => String(element.id) === decode(String(route.params.id)),
    )
    target && target.id && sideBar.value.selectMessage(target)
  }
  if (route.query.scene === 'member') {
    upgrade()
  }
}

// 获取v2版本模型数据
async function modelDataInit() {
  const data: any = await services.get('/gpt/v2/models', {
    headers: {
      noninductive: true,
    },
  })
  modelData.value = data
}

// Initialize all mermaid diagrams by clicking render buttons
const initMermaidDiagrams = () => {
  const { mermaid } = toolStatus('mermaid')
  // Use setTimeout to ensure DOM is ready
  if (mermaid) {
    const renderMermaidElements = document.querySelectorAll('.render-mermaid')
    renderMermaidElements.forEach((element) => {
      (element as HTMLElement).click()
    })
  }
}

onMounted(() => {
  sendBox.value.setFocus()
  init()

  messageDB.init()
  headerBarStore.setHeaderBar(headerBar.value)
})
watchEffect(() => {
  chatViewData.value.messageId = messageId.value
})
watchEffect(() => {
  chatViewData.value.groups = groups.value
})

watchEffect(() => {
  chatViewData.value.sections = sections.value
  currentChatAttachment.value = sections.value
    .filter((i: any) => !i.receiving)
    .map((i: any) => i.attachments)
    .filter(Boolean)
    .flat(Infinity)
})
watchEffect(() => {
  chatViewData.value.groupId = groupId.value
})
watchEffect(() => {
  chatViewData.value.chatContent = chatContent.value
})
watchEffect(() => {
  chatViewData.value.status = status.value
})
watchEffect(() => {
  chatViewData.value.messageName = generalDialogs.value.find(
    (element: any) => element.id === messageId.value,
  )?.name ?? ''
})

// 节流后的newMessage函数
const throttledNewMessage = throttle(() => {
  newMessage(false)
}, 1500)

// 对话窗口id变化/对话状态变化时，关闭当前组件并重新初始化
watch(
  [
    () => chatViewData.value.messageId,
    () => chatViewData.value.status,
  ],
  () => {
    chatContent.value?.closeArtifactsCode()
    isResizablePanelVisible.value = false
  },
)

onUnmounted(() => {
  ThinkingMarkdownProcessor.destory()
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/chat-view";
</style>

<style lang="scss">
.hidden-thinking-time-functional {
  .juchats-thinking {
    display: none !important;
  }
}
</style>
