<template>
  <div class="plan_progress_wrapper flex-y-c space-x-8px">
    <div class="translate-x-5px text-12px text-#000 font-normal opacity-0 transition-cusbezier-200 dark:text-#fff">
      {{ sum >= 9999 ? '∞' : `${used}/${sum}` }}
    </div>
    <div
      class="plan_progress h-10px overflow-hidden rounded-6px bg-#F9F9F9 dark:bg-#363636"
      :style="{ width: `${progresswidth}px` }"
    >
      <div
        class="plan_progress_bar h-full shadow-[0_0_50px_0_#0000000d] transition-cusbezier-300"
        :style="{ width: `${progress * 48}px` }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  progress: number // 0-1之间的小数
  sum: number
  used: number
}

const props = defineProps<Props>()

const progresswidth = ref(48)
const barwidth = computed(() => {
  return `${props.progress * progresswidth.value}px`
})
</script>

<style lang="scss" scoped>
.plan_progress_bar {
  width: v-bind(barwidth);
  background-image: linear-gradient(103deg, #FFBB9E 0%, #FF4C00 100%);
}

.plan_progress_wrapper {
  &:hover {
    :has(+ .plan_progress) {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
</style>
