<template>
  <button
    text="13px #727272 hover:#000 dark:(#cdcdcd hover:#fff)"
    bg="#fff dark:#272727"
    border="~ #E5E5E5 dark:none"
    shadow="[0_0_10px_0_#0000000a] hover:[0_0_20px_0_#00000014]"
    class="h-42px flex-y-c rounded-20px px-18px transition-cusbezier-300"
    :class="[
      hasAnimation && 'animate__animated animate__zoomIn',
    ]"
    @click="onHandleClick"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  hasAnimation?: boolean
}>(), {
  hasAnimation: true,
})
const emit = defineEmits<{
  (e: 'click'): void
}>()

function onHandleClick() {
  emit('click')
}
</script>
