<template>
  <div class="w-full">
    <div
      v-if="!opened && <PERSON><PERSON><PERSON>(note.cover)" class="[box-shadow:0px_5px_20px_#00000012] mx-auto box-border w-[calc(100%-40px)] w-full flex flex-col cursor-pointer rounded-[5px] bg-[#ffffff] px-5px pb-6px pt-1px transition-all duration-100 dark:bg-#272727"
      :style="{ opacity: isVisible ? 0 : 1 }"
      @click="openNote"
    >
      <div class="mb-10px h-15px text-12px font-light font-Lexend">
        {{ note.version }} {{ $t('updateNote.title') }}
      </div>
      <div class="aspect-200/80 w-full overflow-hidden rounded-5px">
        <img :src="note.cover" class="h-full w-full object-cover" alt="">
      </div>
    </div>
    <Teleport to="body">
      <ElDialog
        v-model="isVisible" :destroy-on-close="true" :show-close="false"
        modal-class="el-overlay [&_.el-dialog]:shadow-[0_0_7.5rem_0] [&_.el-dialog]:shadow-[#00000014] [&_.el-dialog]:rounded-2.5 [&_.el-dialog]:p-8.5 [&_.el-dialog]:pt-5 [&_.el-dialog]:lt-md:p-4 [&_.el-dialog]:my-0 [&_.el-dialog]:bg-model-select-bg [&_.el-dialog]:text-primary-300 !z-20030 bg-[rgba(var(--white-rgb),0.5)]! backdrop-blur-2.5rem! sticky [&_.el-dialog]:top-[50%]! [&_.el-dialog]:-translate-y-[50%]!"
        class="no-scrollbar max-h-[90vh] max-w-640px overflow-auto md:h-auto md:max-h-auto md:w-auto w-[90vw]! dark:bg-#1e1e1e! md:px-20px! md:py-18px!"
      >
        <template #header="{ titleId }">
          <div :id="titleId" class="pb-8px">
            <div class="flex items-center justify-between text-14px font-Lexend">
              <div class="dark:text-#F1F1F1">{{ moment(note.publishedAt).format('MMM DD') }}</div>
              <div class="text-14px font-bold dark:text-#F1F1F1">{{ $t('updateNote.title') }}</div>
              <i class="i-ri-close-line cursor-pointer text-20px text-#A8AFBF" @click="closeDialog"></i>
            </div>
          </div>
        </template>
        <template #default>
          <img v-if="!note.videoUrl" :src="note.cover" class="mb-24px w-full" alt="" />
          <video v-if="note.videoUrl" :src="note.videoUrl" class="mb-24px w-full rounded-5px" autoplay loop></video>
          <ul class="m-0 list-none p-0">
            <li>
              <div
                class="mb-14px text-24px text-24px text-#000000 text-#000000 font-medium font-medium leading-30px font-Lexend dark:text-#F1F1F1"
              >
                {{ note.version }} {{ $t('updateNote.title') }}
              </div>
              <ul class="grid grid-cols-1 mb-14px list-none gap-14px p-0">
                <li
                  v-for="content in note.items" :key="content.content"
                  class="flex flex-nowrap items-start font-Lexend dark:text-#F1F1F1"
                >
                  <span class="note" :class="{ highlight: content.highlight }">
                    ○ {{
                      content.content[locale] }}</span>
                </li>
              </ul>
              <div class="indent-15px text-12px text-#A8AFBF font-light font-Lexend">
                {{
                  moment(note.publishedAt).format('YYYY-MM-DD HH:mm') }}
              </div>
            </li>
          </ul>
        </template>
      </ElDialog>
    </Teleport>
  </div>
</template>

<script setup>
import { ElDialog } from 'element-plus'
import moment from 'moment'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()
const isVisible = ref(false)
const note = ref({
  version: '',
  publishedAt: '',
  items: [],
  cover: '',
  videoUrl: '',
})
const opened = ref(true)

const closeDialog = () => {
  isVisible.value = false
}

const openNote = () => {
  isVisible.value = true
  opened.value = true
  localStorage.setItem(`updateNoteOpened-${note.value.version}`, 'true')
}

const getUpdateNote = () => {
  fetch('https://strapi.herm.studio/api/juchats-update-notes?populate=*&sort[0]=publishAt:desc&pagination[page]=1&pagination[pageSize]=1').then(res => res.json()).then((res) => {
    if (res.data && res.data.length > 0) {
      const latestNote = res.data[0]
      if (latestNote.cover && latestNote.cover.length > 0) {
        const url = new URL(latestNote.cover[0].url, 'https://strapi.herm.studio')
        note.value = {
          version: latestNote.version,
          publishedAt: latestNote.publishAt,
          items: latestNote.items,
          cover: url.href,
          videoUrl: latestNote.videoUrl,
        }

        if (note.value.videoUrl) {
          // 如果有视频，把原封面置空，把入口当做一个 loading 状态
          note.value.cover = ''

          const video = document.createElement('video')
          video.src = note.value.videoUrl
          video.crossOrigin = 'anonymous'
          video.style.display = 'none'
          document.body.appendChild(video)

          video.addEventListener('loadedmetadata', () => {
            video.currentTime = 0
          })

          video.addEventListener('seeked', () => {
            try {
              const canvas = document.createElement('canvas')
              canvas.width = video.videoWidth
              canvas.height = video.videoHeight
              const ctx = canvas.getContext('2d')
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
              const dataURL = canvas.toDataURL('image/jpeg')
              note.value.cover = dataURL
            }
            catch (error) {
              console.warn('无法生成视频缩略图:', error)
            }
            finally {
              document.body.removeChild(video)
            }
          })

          video.addEventListener('error', (e) => {
            console.error('视频加载失败:', e)
            document.body.removeChild(video)
          })
          video.load()
        }

        const openedKey = `updateNoteOpened-${latestNote.version}`
        const cached = localStorage.getItem(openedKey)
        if (!cached || cached === 'false') {
          opened.value = false
        }
      }
    }
  })
}

onMounted(() => {
  getUpdateNote()
})
</script>

<style scoped>
.note {
  @apply text-#000000 leading-24px text-12px md:text-14px dark:text-white;

  &.highlight {
    @apply text-#D97757 font-bold;
  }
}
</style>
