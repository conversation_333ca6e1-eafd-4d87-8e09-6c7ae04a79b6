<template>
  <div
    p="x-0 y-40px"
    class="absolute z-10 w-880px overflow-auto rounded-[16px_16px_40px_40px] bg-[#fff] shadow-[0_5px_20px_0_#00000012]"
  >
    <Transition name="fade" mode="out-in">
      <div v-if="!contextId" class="scene-button__background"></div>
    </Transition>
    <div class="scene-button__tabs flex justify-start">
      <div
        class="mr-6"
        :class="{ 'font-500 text-#000': !selected }"
        @click="select(0)"
      >
        {{ $t("sceneButton.rolePlaying") }}
      </div>
      <div
        class="absolute h-5px w-20px bg-[#000] transition-cusbezier-300 -bottom-5px"
        :style="`left:${selected * 80}px`"
      ></div>
    </div>

    <el-scrollbar height="400px" always>
      <div class="scene-button__groups">
        <div
          v-for="{ id, title, context } in contexts"
          :key="id"
          :class="{ 'scene-button__groups--active': contextId === id }"
          @click="change({ context, id })"
        >
          {{ title }}
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import services from "@/common/axios";

defineProps({
  contextId: {
    type: Number,
    default: null,
  },
  contextTitle: {
    type: String,
    default: null,
  },
});
const emits = defineEmits([
  "change",
  "update:contextId",
  "update:contextTitle",
]);
const contexts: any = ref([]);
const selected: any = ref(0);
const select = (param: number) => {
  selected.value = param;
};

onMounted(async () => {
  const { records }: any = await services.post("/gpt/contexts", {
    page: 1,
    limit: 10000,
  });
  contexts.value = records;
});

const change: any = ({ id }: any) => {
  emits("update:contextId", id);
  emits(
    "update:contextTitle",
    contexts.value.find((element: any) => element.id === id)?.title
  );
  emits("change");
};
</script>

<style lang="scss" scoped>
.scene-button__background {
  @apply absolute top-0 left-0 size-full pointer-events-none;

  background: linear-gradient(to bottom, transparent, transparent, white);
}

.scene-button__tabs {
  @apply mx-40px mt-0 mb-40px lh-20px text-14px font-400 text-[#bcbcbc] cursor-pointer relative;

  & > div {
    transition: all 0.3s var(--ani-bezier);
  }
}

.scene-button__groups {
  @apply mx-40px my-0 pb-110px grid gap-40px  grid-cols-[repeat(3,240px)];

  div {
    @apply w-240px h-50px lh-50px bg-[#fefefe] border-1px border-dashed border-[#e1e5ea] rounded-5px text-14px text-center cursor-pointer transition-cusbezier-300;

    &:hover {
      border: 1px solid #000;
    }
  }
}

.scene-button__groups--active {
  border: 1px solid #000 !important;
}
</style>
