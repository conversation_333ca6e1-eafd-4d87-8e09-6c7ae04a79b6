<template>
  <div
    class="fixed z-10 h-80px w-full flex items-center justify-center bg-primary-600 shadow-[0_0_20px_0_#00000012] lt-md:(h-50px px-10px)"
  >
    <LOGO class="shrink-0" svg-class="lt-md:h-22px"></LOGO>
    <div
      class="mt-11px px-20px px-8px pt-5px text-#d1d1d1 lt-md:(mt-1px pt-5px)"
    >
      |
    </div>
    <div
      class="relative mt-12px flex items-center text-24px font-800 font-RobotoSlab lt-md:mt-3px"
    >
      <span
        class="max-w-200px flex-1 of-hidden text-ellipsis whitespace-nowrap break-all"
      >
        {{ formatMobile(userInfo.userName) }}
      </span>
      <div class="shirnk-0 flex text-18px text-destructive-300 font-Damion">
        <div class="mt-5px break-all pl-10px leading-32px">
          {{
            !userInfo.packageName
              ? $t("shareFooter.guest")
              : userInfo.packageName?.replace("年", "")
          }}
        </div>
        <PremiumPro
          v-if="['Premium年'].includes(userInfo.packageName)"
          class="mt-10px"
        />
      </div>
    </div>
  </div>
  <div class="h-80px lt-md:h-50px"></div>
</template>

<script setup lang="ts">
import { formatMobile } from '@/common'

defineProps({
  userInfo: {
    type: Object,
    default: () => ({}),
  },
})
</script>
