import Message from './MobileMessage.vue'

interface MessageOptions {
  message: string
  duration?: number
  transition?: string
  teleport?: string
  zIndex?: number
  top?: number
}

const defaultOptions: Required<Omit<MessageOptions, 'message'>> = {
  duration: 2000,
  transition: 'message',
  teleport: 'body',
  zIndex: 2001,
  top: 281,
}

export function createMessage(options: MessageOptions) {
  const mergedOptions = { ...defaultOptions, ...options }

  const app = createApp({
    render() {
      return h(Message, { ...mergedOptions })
    },
  })

  const target = document.querySelector(mergedOptions.teleport)
  if (!target) {
    throw new Error(`无法找到传送目标：${mergedOptions.teleport}`)
  }
  const container = document.createElement('div')
  target.appendChild(container)

  app.mount(container)

  setTimeout(() => {
    app.unmount()
    target.removeChild(container)
  }, mergedOptions.duration + 400)
}
