<script setup>
import { useAppStore } from '@/stores/app'
import { ATTACHMENTS_NUMBER_LIMIT } from '@/common/constant'

const appStore = useAppStore()
const { expandSidebar } = storeToRefs(appStore)

const fileIconList = [
  { fileName: 'test.xls' },
  { fileName: 'test.doc' },
  { fileName: 'test.pdf' },
  { fileName: '' },
]
</script>

<template>
  <div
    :class="{
      'left-260px': expandSidebar,
      'left-0': !expandSidebar,
    }"
    class="absolute bottom-0px right-0px top-0px z-30 box-border flex flex-c bg-#ffffff80 px-7.3rem py-4.69rem shadow-[0_0.13rem_1.88rem_0_#0000000d] backdrop-blur-0.63rem !dark:bg-#0003"
  >
    <div
      class="h-15rem w-37.5rem flex-c flex-col animate-name-bounceIn animate-duration-0.2s border-0.06rem border-#155EEF border-rd-0.88rem border-dashed bg-#F2F6FE dark:(border-[var(--p8)] bg-[var(--p2)])"
    >
      <div class="flex space-x-1.25rem">
        <SendAttachmentFileIcon
          v-for="(item, index) in fileIconList"
          :key="index"
          class="file-icon"
          :file-name="item.fileName"
          :style="{ 'animation-delay': `${index * 45}ms` }"
        />
      </div>
      <div class="mt-0.8rem flex flex-y-c">
        <p
          class="mr-0.4rem animate-name-fadeInUp animate-duration-0.3s text-justify text-0.88rem text-[var(--s4)] font-600 tracking-0"
        >
          {{
            $t("dragAttachmentPlaceHolder.placeholder", {
              ATTACHMENTS_NUMBER_LIMIT,
            })
          }}
          &lt;30MB
        </p>
        <div class="rotate-15">
          <i
            class="i-ju-file-attachment-upload animate-name-heartBeat animate-duration-1s animate-iteration-infinite text-[1.4rem] c-[var(--drag-attachment-placeholder-icon)]"
          ></i>
        </div>

        <slot name="progress" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.file-icon {
  @apply animate-name-bounceIn animate-duration-0.3s animate-forwards op-0;
}
</style>
