<template>
  <div
    class="header absolute left-0 top-0 w-full px-40px py-30px lt-md:(px-8px py-8px)"
  >
    <div class="flex items-center justify-between">
      <LOGO svg-class="lt-md:h-17px"></LOGO>
      <!-- {{ $t("baseHeader.title") }} -->
      <!-- <h2 class="I_hidden">Juchats ｜ One Window, Infinite Worlds! ｜LLMs｜RAG｜Agent｜AI Human</h2> -->
      <div class="flex items-center leading-none">
        <div class="link mr-10px flex-y-c lt-md:(mr-0 text-sm)">
          <a
            href="https://www.x.com/hermstudio"
            target="_blank"
            class="social-link flex items-center"
          >
            <span class="icon-social icon-x"></span>
            <span class="text-13px lt-md:text-sm">Twitter</span>
          </a>
          <a
            href="https://www.discord.gg/hermstudio"
            target="_blank"
            class="social-link flex items-center"
          >
            <span class="icon-social icon-discord"></span>
            <span class="text-13px lt-md:text-sm">Discord</span>
          </a>
          <span class="mx-15px my-0">|</span>
          <a v-if="isGift" href="/chat" class="auth-link link-visual">{{
            $t("baseHeader.home")
          }}</a>
          <a
            v-if="!isGift"
            href="javascript:;"
            class="auth-link link-visual"
            @click="logIn"
          >{{ $t("baseHeader.login") }}</a>
        </div>
        <div class="light-switch flex-c" @click="handleThemeChange('switch')">
          <span
            class="i-gravity-ui-sun dark:i-gravity-ui-moon text-17px"
          ></span>
        </div>
        <!-- <button class="border" @click="handleThemeChange('light')">浅色</button>
        <button class="border" @click="handleThemeChange('dark')">深色</button>
        <button class="border" @click="handleThemeChange('auto')">跟随系统</button> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ThemeMode } from '@/hooks/useThemeChange'
import { useRoute } from 'vue-router'

const emits = defineEmits(['clickLogin'])
const { setTheme } = useThemeChange()
const handleThemeChange = (theme: ThemeMode) => {
  setTheme(theme)
}
const route = useRoute()
const isGift = computed(() => {
  return route.name === 'Gift'
})

function logIn() {
  emits('clickLogin')
}
</script>

<style lang="scss" scoped>
.header {
  a {
    color: var(--black);
  }

  .light-switch {
    @apply cursor-pointer transition-cusbezier-200 lt-md:hidden;

    &:hover {
      filter: invert(1);
    }
  }

  .link,
  .light-switch {
    @apply bg-[rgba(255,255,255,.6)]  rounded-10px p-15px dark:bg-[rgba(0,0,0,0.4)] lt-md:(py-10px);
  }

  .auth-link {
    transition: 0.3s;

    &:hover {
      text-shadow: var(--black) 1px 1px 5px;
    }
  }

  .social-link {
    @apply op-80 transition-cusbezier-300;

    &:first-child {
      margin-right: 15px;
    }

    &:hover {
      opacity: 1;
    }
  }

  .icon-social {
    font-size: 14px;
    margin-right: 5px;
  }

  .icon-discord {
    @apply text-18px lt-md:text-16px;
  }
}
</style>
