.mermaid-toggle-button {
  width: 140px;
  height: 32px;
  background-color: var(--s);
  border: 1px solid var(--artifacts-code-tabs-border);
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: var(--sidebar-option-text);
  position: relative;
  overflow: hidden;
  
  .segment {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
  
  .active-indicator {
    position: absolute;
    width: calc(50% - 6px);
    height: calc(100% - 6px);
    background-color: #FFF;
    border-radius: 4px;
    transition: transform 0.3s var(--ani-bezier);
    z-index: 0;
    top: 3px;
    left: 3px;
  }
}

.rendered .mermaid-toggle-button .active-indicator {
  transform: translateX(calc(100% + 6px));
}

/* 深色模式下的文字颜色 */
.render-mermaid:not(.rendered) {
  .mermaid-toggle-button {
    .segment:nth-child(2) {
      @apply dark:I_text-[#000];
    }
  }
}

.rendered {
  .mermaid-toggle-button {
    .segment:nth-child(3) {
      @apply dark:I_text-[#000];
    }
  }
}

// 代码块折叠时隐藏按钮
.code-block-collapsed {
  .mermaid-toggle-button {
    display: none;
  }
}

.code-height-max {
  max-height: 400px;
  overflow: hidden !important;

  .code_shadow {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 26px;
    background: linear-gradient(to top, rgb(var(--white-rgb), 0.8), #fff0);
    z-index: 1;
    pointer-events: none;
  }
}


