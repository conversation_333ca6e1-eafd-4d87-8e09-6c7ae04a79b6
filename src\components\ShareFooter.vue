<template>
  <div class="share-footer">
    <div class="share-footer__container">
      <div class="share-footer__content flex items-center justify-between">
        <div class="flex items-center">
          <AvatarFrame
            :src="userInfo.avatar"
            name="share-footer__avatar"
          ></AvatarFrame>
          <div class="share-footer__name ml-[5px]">
            <span>{{ userInfo.userName }}</span>
            <div class="share-footer__name__flag">
              {{ userInfo.packageName || $t("shareFooter.guest") }}
            </div>
          </div>
          <div class="share-footer__data flex items-center pl-16">
            <el-divider class="pr-5" direction="vertical" />
            <div class="pr-5">
              {{ $t("shareFooter.prompt") }}：{{ userInfo.prompts }}
            </div>
            <el-divider class="pr-5" direction="vertical" />
            <div class="pr-5">
              {{ $t("shareFooter.views") }}：{{ userInfo.views }}
            </div>
            <!-- <el-divider class="pr-5" direction="vertical" />
            <div class="pr-5">引用次数：72</div> -->
            <el-divider class="pr-5" direction="vertical" />
            <div class="pr-5">
              {{ $t("shareFooter.cumulativeGain") }}：{{
                userInfo.commission || 0
              }}
            </div>
            <el-divider class="pr-5" direction="vertical" />
            <div class="pr-5">{{ $t("shareFooter.unlimitedLove") }}</div>
          </div>
          <div class="share-footer__mobile__data flex items-center pl-[50px]">
            <el-divider direction="vertical" />
            <div class="pl-[5px]">{{ $t("shareFooter.unlimitedLove") }}</div>
          </div>
        </div>
        <div>
          <img class="share-footer__code" :src="qrcode" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { generateQRCode } from "@/common";

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({}),
  },
  shareCode: {
    type: String,
    default: "",
  },
});
const qrcode = ref();
onMounted(async () => {
  qrcode.value = await generateQRCode(
    `${location.origin}?shareCode=${props.shareCode}`
  );
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/share-footer";
</style>
