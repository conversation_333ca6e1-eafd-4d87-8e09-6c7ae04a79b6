<template>
  <div class="relative h-200px">
    <el-button class="absolute left-0 top-0 z-50" @click="replay">show</el-button>
    <div v-if="show">
      <div class="relative h-200px">
        <div class="animate__animated animate__fadeOut absolute animate-delay-1300">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 255 158" width="300">
            <path d="M185.1 4.2a16.24 16.24 0 0 1 3.7 3.7c2.1 2.9 2.8 6.5 3 12.1h.1c-24.7 0-45.9 14.9-55.1 36.2.6.6 1.3 1.2 1.9 1.7 4.2 3 10 3.2 21.4 3.2h3.5c11.3 0 17.2-.2 21.4-3.2a16.24 16.24 0 0 0 3.7-3.7c3-4.1 3.2-9.8 3.2-20.8v-1.7-.7-.6-1.7l-.1-8.7h.1c33.1 0 60 26.9 60 60 0 13.8-4.7 26.5-12.5 36.7.6.9 1.1 1.8 1.7 2.8 7.6 12 11.4 18 10.7 23-.5 3.4-2.2 6.5-4.8 8.7-3.6 3-10.1 3.2-22.8 3.2h-4.7c-12.6 0-19.2-.2-22.8-3.2-2.6-2.2-4.3-5.3-4.8-8.7-.1-.8-.1-1.6 0-2.4h-.1c19.3 0 36.5-9.1 47.5-23.3-5.6-8.8-8.8-13.4-12.8-14.9a13.8 13.8 0 0 0-9.4 0c-4.4 1.6-7.8 6.9-14.5 17.6-6.4 10.1-10.1 16-10.7 20.6h-.1c-33.1 0-60-26.9-60-60 0-8.5 1.8-16.5 4.9-23.8a16.32 16.32 0 0 1-1.7-2c-3-4.1-3.2-9.8-3.2-20.8v-4.6c0-11 .2-16.7 3.2-20.8a16.24 16.24 0 0 1 3.7-3.7c4.2-3 10-3.2 21.4-3.2h3.5c11.5-.2 17.4 0 21.5 3zM121 104v36H1v-36h120z" fill="none" stroke="#d8d8d8" stroke-linejoin="round" stroke-width="1.3">
              <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="1.5s" />
            </path>
          </svg>
        </div>

        <div class="animate__animated animate__fadeIn absolute animate-delay-1200" @click="replay">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 255 158" width="300"><path d="M137.1 57c-3.1 7.3-4.6 15-4.6 23 0 32.7 26.5 59.3 59.2 59.5.8-4.6 4.4-10.3 10.7-20.3 6.8-10.8 10.2-16.2 14.8-17.8a14.67 14.67 0 0 1 9.7 0c4 1.5 7 5.5 12.6 14.5 7.8-10.4 12-22.7 12-35.8 0-32.7-26.5-59.3-59.1-59.5.1 2.3.1 4.8.1 8.2v2.9 1.7c0 10.9-.2 16.8-3.3 21.1a17.12 17.12 0 0 1-3.8 3.8c-4.3 3.1-10.4 3.3-21.7 3.3h-3.5c-11.3 0-17.4-.2-21.7-3.3-.4-.4-.9-.9-1.4-1.3z" fill="#f43513" /><path d="M121 140.5H1a.47.47 0 0 1-.5-.5v-36a.47.47 0 0 1 .5-.5h120a.47.47 0 0 1 .5.5v36a.47.47 0 0 1-.5.5z" /><path d="M1.5 104.5h119v35H1.5z" /></svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const show = ref(false)
// 重新播放
function replay() {
  show.value = false
  setTimeout(() => {
    show.value = true
  }, 100)
}
</script>

<style lang="scss" scoped>
</style>
