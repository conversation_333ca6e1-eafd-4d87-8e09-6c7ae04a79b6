import { onMounted } from 'vue'
import axios from 'axios'
import { useI18n } from 'vue-i18n'

const request = axios.create({
  baseURL: 'https://strapi.herm.studio',
  headers: {
    Authorization: `Bearer 13b32a240fc493cf8fd4a3ba5f99554ebf635e817324c920156a6b4a30100575f8529b798d73b69ab6541c2c9b1e8b2718e12c31dad314c873c2ab6652f26bc14f6aca8dd3520cdf564717ed7de2c0e5a70a573c0fc5f23bc21f814feefacbbcbb1c8a2352d94268676d8f4a324cc985c5e6bf39333395cc6e87f4631ed44a7b`,
  },
})

export const useStrapi = () => {
  const config = ref([])

  const { locale } = useI18n()

  onMounted(() => {
    let domain = location.hostname.trim()
    if (!domain) {
      domain = 'www.juchats.com'
    }

    request.get('/api/juchats-announcements').then((res) => {
      const target = res.data.data.filter((item: any) => domain.includes(item.domain))
      config.value = target
    })
  })

  const announcement = computed(() => {
    const target = config.value.find((item: any) => item.language === locale.value)
    if (!target) return ''
    return target.content
  })

  return {
    announcement
  }
}

