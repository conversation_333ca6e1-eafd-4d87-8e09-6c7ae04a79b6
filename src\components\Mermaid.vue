<template>
  <div
    :class="{ 'fixed left-0 top-0 size-full inset-0 z-[100] bg-white dark:bg-#1c1f24': isFullscreen, 'relative': !isFullscreen }"
  >
    <div
      class="top-0 mb-2 flex gap-2 bg-inherit -ml-3px -mt-6px" :class="{
        'lt-md:p-0': !isFullscreen,
        'sticky': !isClient,
        'mt-6 ml-2': isFullscreen && isClient,
        'w-full': isFullscreen,
        'z-[101] p-2': isFullscreen,
        'z-[10]': !isFullscreen,
        'lt-md:(fixed left-0 right-0 gap-3 p-3)': isFullscreen,
      }"
    >
      <button class="border rounded px-2 py-1 text-sm dark:border-color-#575E69 dark:text-#D8D8D8" @click="zoomIn">
        <i class="i-ri-zoom-in-line" />
      </button>
      <button class="border rounded px-2 py-1 text-sm dark:border-color-#575E69 dark:text-#D8D8D8" @click="zoomOut">
        <i class="i-ri-zoom-out-line" />
      </button>
      <button class="border rounded px-2 py-1 text-sm dark:border-color-#575E69 dark:text-#D8D8D8" @click="resetZoom">
        <i class="i-ri-refresh-line" />
      </button>
      <el-popover trigger="click" popper-class="mermaid-popover" :show-arrow="false" placement="bottom-start" class="min-w-auto" :width="20">
        <template #reference>
          <button class="border rounded px-2 py-1 text-sm dark:border-color-#575E69 dark:text-#D8D8D8">
            <i class="i-ri-download-line" />
          </button>
        </template>
        <div class="flex flex-col gap-2 text-12px font-Lexend">
          <button
            class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
            @click="() => downloadPNG({ backgroundColor: '#ffffff' })"
          >
            PNG
          </button>
          <button
            class="block w-full flex cursor-pointer items-center justify-between rounded-md px-4 py-2 text-left hover:bg-[var(--user-info-bg)] hover:text-black"
            @click="downloadSVG"
          >
            SVG
          </button>
        </div>
      </el-popover>
      <button
        class="border rounded px-2 py-1 text-sm dark:border-color-#575E69 dark:text-#D8D8D8"
        @click="toggleFullscreen"
      >
        <i :class="isFullscreen ? 'i-ri-fullscreen-exit-line' : 'i-ri-fullscreen-line'" />
      </button>
    </div>
    <div
      ref="containerRef" class="mermaid-container relative overflow-hidden" :class="[
        isFullscreen ? 'h-[calc(100vh-48px)] lt-md:mt-12' : 'h-[200px] md:h-[400px]',
      ]" @mousedown="startDrag" @mousemove="onDrag" @mouseup="stopDrag" @mouseleave="stopDrag" @wheel="handleWheel"
      @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd"
    >
      <div
        :id="containerId" ref="mermaidContainer"
        class="mermaid absolute h-full w-full flex items-center justify-center"
        :style="{ transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)` }"
      >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useViewerControls } from '@/hooks/useViewerControls'
import { useAppStore } from '@/stores/app'

const props = defineProps<{
  code: string
}>()

function decodeFromUnicode(str: string) {
  return str.replace(/u[0-9a-fA-F]{4}/g, (match) => {
    return String.fromCharCode(Number.parseInt(match.slice(1), 16))
  })
}

const { isClient } = storeToRefs(useAppStore())

const isDark = () => {
  const theme = document.documentElement.getAttribute('class')
  const dark = Boolean(theme && theme.includes('dark'))
  return dark
}

const containerId = `mermaid-container-${Date.now()}`

const mermaidContainer = ref<HTMLElement | null>(null)
const containerRef = ref<HTMLElement | null>(null)
const currentTheme = ref(isDark())

const {
  scale,
  position,
  isFullscreen,
  zoomIn,
  zoomOut,
  resetZoom,
  startDrag,
  onDrag,
  stopDrag,
  handleWheel,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd,
  toggleFullscreen,
  downloadPNG,
  downloadSVG,
} = useViewerControls({
  containerRef,
  contentRef: mermaidContainer,
}, 'Mermaid')

const initMermaid = async (code?: string) => {
  try {
    const newContainerId = `mermaid-${Date.now()}`
    const dark = isDark()

    // eslint-disable-next-line ts/ban-ts-comment
    // @ts-expect-error
    mermaid.initialize({
      theme: isDark() ? 'dark' : 'base',
      htmlLabels: true,
      startOnLoad: false,
      sequence: {
        useMaxWidth: true,
      },
      flowchart: {
        useMaxWidth: true,
      },
      themeVariables: {
        edgeLabelBackground: dark ? '#1C1F24' : '#ffffff',
        nodeBorder: dark ? '#575E69' : 'transparent',
      },
    })

    if (mermaidContainer.value) {
      // eslint-disable-next-line ts/ban-ts-comment
      // @ts-expect-error
      const { svg } = await mermaid.render(newContainerId, decodeFromUnicode(code || props.code))
      mermaidContainer.value.innerHTML = svg
    }
  }
  catch (error) {
    if (mermaidContainer.value) {
      mermaidContainer.value.innerHTML = `
        <div class="p-4 text-red-500 dark:text-red-400 rounded">
          <div class="font-bold mb-2">Mermaid Syntax Error</div>
          <pre class="text-sm whitespace-pre-wrap">${(error as Error).message}</pre>
        </div>
      `
    }
  }
}

const observer = new MutationObserver((mutationsList) => {
  for (const mutation of mutationsList) {
    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
      const newTheme = isDark()
      // 只在主题真正改变时才重新渲染
      if (newTheme !== currentTheme.value) {
        currentTheme.value = newTheme
        nextTick(() => {
          if (mermaidContainer.value) {
            initMermaid()
          }
        })
      }
    }
  }
})

observer.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ['class'],
})

onMounted(() => {
  setTimeout(() => {
    initMermaid()
  })
  if (containerRef.value) {
    containerRef.value.addEventListener('touchstart', handleTouchStart)
    containerRef.value.addEventListener('touchmove', handleTouchMove)
    containerRef.value.addEventListener('touchend', handleTouchEnd)
  }
})

onUnmounted(() => {
  observer.disconnect()
  if (containerRef.value) {
    containerRef.value.removeEventListener('touchstart', handleTouchStart)
    containerRef.value.removeEventListener('touchmove', handleTouchMove)
    containerRef.value.removeEventListener('touchend', handleTouchEnd)
  }
})
</script>

<style>
.mermaid svg {
  width: 100%;
  height: 100%;
  max-width: 100%;
  transition: transform 0.1s ease;
}

.mermaid {
  transform-origin: center center;
  cursor: grab;
  touch-action: none;
}

.mermaid:active {
  cursor: grabbing;
}

[id^="mermaid-"] p {
  line-height: 1.4 !important;
}

/*
[id^="mermaid-"] .edgeLabel p {
  @apply !bg-white !dark:bg-#282c34;
} */

.mermaid-container {
  touch-action: none;

  /* 禁用浏览器默认的触摸行为 */
}

.mermaid-popover {
  @apply !shadow-none !border !border-solid !border-[rgb(229,231,235)] rounded px-2 py-1 text-sm !dark:border-color-#575E69 dark:text-#D8D8D8;
}
</style>
