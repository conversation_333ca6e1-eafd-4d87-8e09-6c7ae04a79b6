// 公共样式
@mixin screen($width) {
  @media (max-width: $width) {
    @content;
  }
}

@mixin narrow {
  @include screen(767.9px) {
    @content;
  }
}

@mixin narrow-mini {
  @include screen(380px) {
    @content;
  }
}

:root {
  --ani-bezier: cubic-bezier(0.22, 0.58, 0.12, 0.98); // 动画曲线
  --font-family: "Inter", "PingFang SC", "HarmonyOS Sans SC", "Segoe UI", system-ui, rototo, helvetica, arial, sans-serif;
  
  // 优先使用Inter匹配英文
  // 然后使用苹果自带的PingFang SC，苹果用这个字体，Windows上不存在这个字（windows低分辨率品目用这个字体很虚，用户乐意装也没啥办法）
  // 然后使用鸿蒙字体匹配中文（Windows用户大部分会匹配到这个）
  // 以下是兜底，匹配Windows下的Segoe UI（详见：https://infinnie.github.io/blog/2017/systemui.html）
  // system-ui用作兜底Windows和Mac的主要正常显示
  // Rototo是安卓默认字体
}
