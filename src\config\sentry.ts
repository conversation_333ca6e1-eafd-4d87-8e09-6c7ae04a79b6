import type { App } from 'vue'
import router from '@/router'
import * as Sentry from '@sentry/vue'

export const initSentry = (app: App) => {
  import.meta.env.DEV || Sentry.init({
    app,
    dsn: import.meta.env.VITE_SENTRY_DSN,
    environment: import.meta.env.MODE,
    integrations: [
      Sentry.browserTracingIntegration({ router }),
      Sentry.replayIntegration(),
    ],
    ignoreErrors: [
      // 账号被挤
      // /unauthorized/i,
      // SSE 请求终止
      /Request aborted/i,
      /**
       * 动态导入模块失败，通常发生在静态资源重新部署后
       */
      /dynamically imported module/i,
      /Importing a module script failed/i,
      // SSE 读取流失败
      /ReadableStreamDefaultReader constructor/i,
      // 第三方资源加载失败
      /Failed to load the Google 3P Authorization JavaScript Library/i,
      // 当用户使用的资源为旧资源时产生，不会导致使用问题
      /Unable to preload CSS/i,
    ],
    denyUrls: ['www.google-analytics.com'],
    // Tracing
    tracesSampleRate: 1.0, //  Capture 100% of the transactions
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
    // Session Replay
    replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  })
}
