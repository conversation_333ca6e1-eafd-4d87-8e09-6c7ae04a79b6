<template>
  <HomeBase @toggle-protocol="onToggleProtocol" @click-login="logIn">
    <!-- 登录BOX START -->
    <Transition
      enter-active-class="animate__animated animate__fadeInUp"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div
        v-if="STEP.LOGIN === step"
        v-show="!isProtolShow"
        class="size-full"
      >
        <LoginWays />
      </div>
    </Transition>
    <!-- 登录BOX END -->
    <!-- <div
      v-show="!isProtolShow"
      v-if="STEP.SUCCESS === step"
      class="login-center absolute min-h-320px w-full flex flex-col items-center"
    >
      <EGGSuccess :gift-info="giftInfo"></EGGSuccess>
    </div> -->
  </HomeBase>
</template>

<script setup lang="ts">
// import services from '@/common/axios'

import avatar from '@/assets/images/avatar_default.png'
import services from '@/common/axios'
import { closeNotification, showNotification } from '@/common/index'

import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useRoute, useRouter } from 'vue-router'
import EGGSuccess from './components/EGGSuccess.vue'

import HomeBase from './components/HomeBase.vue'

const rndKey = ref(Math.random())
const router = useRouter()
const route = useRoute()
const inviteStatus = ref('')
const isShortcut = ref(false)
const giftInfo = ref({})

// const barCodeBox = ref()
const { isPc } = storeToRefs(useAppStore())

// eslint-disable-next-line no-restricted-syntax
const enum LOGIN_TYPE {
  SOCIAL,
  EMAIL,
}

// eslint-disable-next-line no-restricted-syntax
const enum STEP {
  LOGIN,
  INVITATION,
  SUCCESS,
}

const step = ref(STEP.LOGIN)
const loginType = ref(LOGIN_TYPE.SOCIAL)

// const emailCode = ref('')
// const loginKey = ref(0)
const isProtolShow = ref(false)
// const initInvitationCode = ref(false)

const invitationCode = ref('')

init()

async function init() {
  if (route.query.code) {
    invitationCode.value = JSON.parse(JSON.stringify(route.query.code)).substring(0, 8)
  }
  if (route.query.isLogin) {
    isShortcut.value = true
  }
  else if (route.query.isGithub) {
    inviteStatus.value = 'GITHUB'
    isShortcut.value = true
  }
}

// let initInvitationCodeTimer: number

if (localStorage.getItem('token')) {
  router.replace({
    name: 'Chat',
  })
}

function onToggleProtocol(isShow: boolean) {
  isProtolShow.value = isShow
}

function logIn() {
  rndKey.value = Math.random()
  loginType.value = LOGIN_TYPE.SOCIAL
}
</script>

<style lang="scss" scoped>
</style>
