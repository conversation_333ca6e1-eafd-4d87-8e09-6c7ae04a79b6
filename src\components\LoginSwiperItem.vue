<template>
  <div class="h-full" :class="{ 'w-362px': !dialogue }">
    <div class="led-font animate__animated animate__fadeInRight mb-9px text-48px lt-lg:(pt-46px text-center text-32px)">
      <i v-if="title?.toLowerCase() === 'client'" class="i-twemoji-sparkling-heart" />
      {{ title }}
    </div>
    <LoginTipWithLogo class="animate__animated animate__fadeInRight animate-delay-100 lt-lg:(I_hidden)" :tip="tip" />

    <template v-if="dialogue">
      <section class="animate__animated animate__fadeInUp grid grid-cols-[48px_1fr] mt-65px animate-delay-800 gap-15px lt-lg:(mt-40px px-33px)">
        <AvatarFrame
          :src="cydiaImg"
          name="size-48px  "
        >
        </AvatarFrame>

        <div>
          <div class="text-14px font-RobotoSlab space-x-4px">
            <span class="font-bold">Cydiar</span>
            <span class="text-#999">Sonnet 3.5</span>
          </div>

          <div class="mt-6px text-13px leading-loose lt-lg:(leading-normal)">
            <slot name="user"></slot>
          </div>
        </div>
      </section>
      <section class="animate__animated animate__fadeInUp grid grid-cols-[48px_1fr] mt-60px animate-delay-2000 gap-15px lt-lg:(grid-cols-1 mt-20px px-33px)">
        <AvatarFrame
          v-if="!ltLg"
          :src="BotAvatar"
          name="size-48px"
        >
        </AvatarFrame>

        <div>
          <div v-if="!ltLg" class="text-14px font-RobotoSlab space-x-4px">
            <span class="font-bold">Juchats</span>
          </div>
          <div class="animate__animated animate__fadeInUp mt-6px animate-delay-2100 text-13px leading-loose lt-lg:(leading-normal)">
            <slot name="bot"></slot>
          </div>
        </div>
      </section>
    </template>

    <template v-else>
      <slot></slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import BotAvatar from '@/assets/images/ai-avatar-dark.svg'
import cydiaImg from '@/assets/images/login-cydiar-avatar.webp'
import { useAppStore } from '@/stores/app'

const { dialogue = true } = defineProps<Props>()
const { breakpoints } = useAppStore()
const ltLg = breakpoints.smaller('lg') // 小于LG尺寸

interface Props {
  title: string
  tip: string
  dialogue?: boolean
}
</script>

<style lang="scss" scoped>
</style>
