import type { Meta, StoryObj } from '@storybook/vue3'
import Switch from './Switch.vue'

const meta: Meta<typeof Switch> = {
  title: 'component/Switch',
  component: Switch,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  render: () => ({
    components: { Switch },
    setup() {
      const checked = ref(false)
      return { checked }
    },
    template: `
      <Switch v-model="checked" />
    `,
  })
}
