<!-- AudioRecorder.vue -->
<template>
  <div class="audio-recorder">
    <div class="recorder-container">
      <!-- 未录制状态：显示录制按钮 -->
      <button v-if="!isRecording" class="record-btn" @click.stop="toggleRecording" :disabled="loading">
        <svg v-if="!loading" width="8px" height="11px" viewBox="0 0 8 11" version="1.1"
          xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <title>形状</title>
          <g id="Juchats-Dev-Voice" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Website-Index-Model-ZEN-Default-White-Default" transform="translate(-1357, -1093)"
              class="fill-[#ffffff] dark:fill-black" fill-rule="nonzero">
              <g id="Chat-Input" transform="translate(790, 1072)">
                <g id="编组" transform="translate(567.1818, 21)">
                  <path
                    d="M0,2.72727273 L1.09090909,2.72727273 L1.09090909,8.18181818 L0,8.18181818 L0,2.72727273 Z M2.18181818,0 L3.27272727,0 L3.27272727,9.81818182 L2.18181818,9.81818182 L2.18181818,0 Z M4.36363636,1.09090909 L5.45454545,1.09090909 L5.45454545,10.9090909 L4.36363636,10.9090909 L4.36363636,1.09090909 Z M6.54545455,2.72727273 L7.63636364,2.72727273 L7.63636364,8.18181818 L6.54545455,8.18181818 L6.54545455,2.72727273 Z"
                    id="形状"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <i v-else class="icon icon-loading"></i>
      </button>

      <!-- 录制状态：显示取消和确认按钮 -->
      <template v-else>
        <!-- 取消按钮 -->
        <button class="action-btn cancel-btn" @click="cancelRecording">
          <i class="icon icon-close"></i>
        </button>

        <!-- 点线动画 -->
        <div class="recording-line-container">
          <div class="recording-line" :style="lineStyle">
            <div class="dot" v-for="(dot, index) in dots" :key="index" :class="{ 'active': index > previewDotCount }">
            </div>
          </div>
        </div>

        <!-- 计时器 -->
        <span class="time">{{ formatTime(recordTime) }}</span>

        <!-- 确认按钮 -->
        <button class="action-btn confirm-btn" @click="toggleRecording">
          <i class="icon icon-check"></i>
        </button>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AudioRecorder',
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['start', 'stop', 'recording-complete', 'error'],
  data() {
    return {
      recorder: null,
      isRecording: false,
      recordTime: 0,
      timer: null,
      mediaStream: null,
      audioChunks: [],
      dots: Array(1000).fill(false),
      translateX: 0,
      dotAnimationTimer: null,
      containerWidth: 180,
      dotWidth: 10,
      isFullyActive: false,
      previewDotCount: 16,
      audioContext: null,
      analyser: null,
      volumeCheckTimer: null,
      isSoundDetected: false,
      volumeThreshold: -45
    }
  },
  computed: {
    lineStyle() {
      return {
        transform: `translateX(${this.translateX}px)`
      }
    }
  },
  methods: {
    // 初始化录音机
    async initRecorder() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.mediaStream = stream;


        // 初始化音频分析
        this.audioContext = new AudioContext();
        const source = this.audioContext.createMediaStreamSource(stream);
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048;
        source.connect(this.analyser);

        // 创建 MediaRecorder 实例
        this.recorder = new MediaRecorder(stream, {
          mimeType: 'audio/webm'
        });

        this.recorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data);
          }
        };

        return true;
      } catch (err) {
        console.error('麦克风初始化失败:', err);
        this.$emit('error', this.$t('audioRecorder.permissionsRejected'));
        this.isRecording = false;
        return false;
      }
    },

    // 切换录音状态
    async toggleRecording() {
      if (!this.isRecording) {
        await this.startRecording();
      } else {
        await this.stopRecording();
      }
    },

    // 开始录音
    async startRecording() {
      if (!this.recorder) {
        const result = await this.initRecorder();
        if (!result) return;
      }

      this.audioChunks = [];
      this.isSoundDetected = false;
      this.recorder.start();
      this.isRecording = true;
      this.startTimer();
      this.startDotAnimation();

      // 开始音量检测
      this.volumeCheckTimer = setInterval(() => {
        this.checkVolume();
      }, 100);

      this.$emit('start');
    },

    // 停止录音
    async stopRecording() {
      return new Promise((resolve) => {
        this.recorder.onstop = () => {
          clearInterval(this.volumeCheckTimer);

          if (!this.isSoundDetected) {
            // 如果没有检测到足够的声音，不发送录音文件
            this.isRecording = false;
            this.stopTimer();
            this.stopDotAnimation();

            if (this.mediaStream) {
              this.mediaStream.getTracks().forEach(track => track.stop());
              this.mediaStream = null;
            }

            this.$emit('error', this.$t('audioRecorder.noSoundDetected'));
            resolve();
            return;
          }

          // 正常处理录音
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
          const audioFile = new File([audioBlob], `record_${Date.now()}.webm`, {
            type: 'audio/webm'
          });

          this.isRecording = false;
          this.stopTimer();
          this.stopDotAnimation();

          if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
          }

          this.$emit('recording-complete', audioFile);
          resolve();
        };

        this.recorder.stop();
        this.recorder = null;
      });
    },

    // 开始计时器
    startTimer() {
      this.recordTime = 0;
      this.timer = setInterval(() => {
        this.recordTime++;
        if (this.recordTime > 12) {
          this.previewDotCount = -1;
        }
      }, 1000);
    },

    // 停止计时器
    stopTimer() {
      clearInterval(this.timer);
      this.previewDotCount = 16;
    },

    // 格式化时间显示
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    shouldBeActive(index) {
      const dotPosition = index * this.dotWidth + this.translateX;
      return dotPosition < 0 || dotPosition > this.containerWidth;
    },

    startDotAnimation() {
      this.dots = Array(1000).fill(false);
      this.translateX = 0;

      let lastTimestamp = 0;

      const animate = (timestamp) => {
        if (!this.isRecording) return;

        if (timestamp - lastTimestamp > 30) {
          this.translateX -= 0.5;

          const totalWidth = this.dots.length * this.dotWidth;
          if (Math.abs(this.translateX) >= totalWidth) {
            this.translateX = 0;
          }

          lastTimestamp = timestamp;
        }

        this.dotAnimationTimer = requestAnimationFrame(animate);
      };

      this.dotAnimationTimer = requestAnimationFrame(animate);
    },

    stopDotAnimation() {
      if (this.dotAnimationTimer) {
        cancelAnimationFrame(this.dotAnimationTimer);
      }
      this.dots = Array(1000).fill(false);
      this.translateX = 0;
    },

    // 添加检测音量的方法
    checkVolume() {
      const dataArray = new Float32Array(this.analyser.frequencyBinCount);
      this.analyser.getFloatTimeDomainData(dataArray);

      // 计算音量的均方根值(RMS)
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i] * dataArray[i];
      }
      const rms = Math.sqrt(sum / dataArray.length);
      const db = 20 * Math.log10(rms);

      // 如果音量超过阈值，标记为检测到声音
      if (db > this.volumeThreshold) {
        this.isSoundDetected = true;
      }
    },

    // 取消录音
    async cancelRecording() {
      if (this.volumeCheckTimer) {
        clearInterval(this.volumeCheckTimer);
      }

      if (this.recorder && this.recorder.state === 'recording') {
        this.recorder.stop();
      }

      this.isRecording = false;
      this.stopTimer();
      this.stopDotAnimation();
      this.audioChunks = []; // 清空录音数据

      // 关闭麦克风
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => track.stop());
        this.mediaStream = null;
      }

      this.$emit('stop');
      this.recorder = null;
    }
  },
  beforeDestroy() {
    this.stopTimer();
    this.stopDotAnimation();
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
    }
    if (this.recorder && this.recorder.state === 'recording') {
      this.recorder.stop();
    }
    if (this.dotAnimationTimer) {
      cancelAnimationFrame(this.dotAnimationTimer);
    }
    if (this.volumeCheckTimer) {
      clearInterval(this.volumeCheckTimer);
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}
</script>

<style scoped>
.audio-recorder {
  border-radius: 8px;
  width: fit-content;

  @apply p-[12px_0_12px_0] md:p-[12px_0_12px_0];
}

.recorder-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.record-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  border-radius: 50%;
  background: var(--send-button-disabled-bg);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  @apply bg-[#000] dark:bg-white text-[#fff] dark:text-[#000];
}

.record-btn.recording {
  background: #f44336;
}

.record-btn:hover {
  opacity: 0.9;
}

.record-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-mic {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z'/%3E%3C/svg%3E");
}

.icon-stop {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
}

.recording-line-container {
  overflow: hidden;
  position: relative;

  @apply w-[180px] md:w-[194px];
}

.recording-line {
  height: 20px;
  display: flex;
  align-items: center;
  position: relative;
  white-space: nowrap;
}

.dot {
  width: 4px;
  height: 4px;
  border-radius: 0;
  background-color: #333;
  margin: 0 3px;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  opacity: 0.3;
}

.dot.active {
  opacity: 1;
}

.time {
  font-size: 12px;
  font-weight: 500;
  min-width: 24px;
  font-family: monospace;

  @aplly text-[#333] dark:text-[#fff];
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.icon-loading {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 4V2C6.48 2 2 6.48 2 12h2c0-4.41 3.59-8 8-8zm0 16c-4.41 0-8-3.59-8-8H2c0 5.52 4.48 10 10 10v-2zm0-14V4c4.41 0 8 3.59 8 8h2c0-5.52-4.48-10-10-10z'/%3E%3C/svg%3E");
  animation: spin 1s linear infinite;
}

.action-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.cancel-btn {
  background: #ff4444;
}

.confirm-btn {
  background: #4CAF50;
}

.action-btn:hover {
  opacity: 0.9;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E");
}

.icon-close::before {
  display: none;
}

.icon-check {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
}
</style>
