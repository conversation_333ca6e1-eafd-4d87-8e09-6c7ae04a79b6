<script setup>
defineProps({
  percentage: {
    type: Number,
    default: 0,
  },
  strokeWidth: {
    type: Number,
    default: 3,
  },
  color: {
    type: String,
    default: 'white',
  },
  size: {
    type: Number,
    default: 26,
  },
})
</script>

<template>
  <div
    class="absolute left-0 top-0 h-[100%] w-[100%] bg-#00000066"
  >
    <el-progress
      stroke-linecap="round"
      :stroke-width="strokeWidth"
      :color="color"
      class="absolute left-[50%] top-[50%] translate--50%"
      :width="size"
      type="circle"
      :show-text="false"
      :percentage="percentage"
    >
    </el-progress>
    <span class="absolute left-[50%] top-[50%] translate--50% text-0.55rem text-white font-600 tracking-0 font-['Inter']">{{ percentage }}</span>
  </div>
</template>
