import type { SSEResponse } from '@/types'
import { generateUUID } from '@/common'
import services from '@/common/axios'
import { convertAudioToBase64, getBtyes, notify } from '@/common/tools'
import { MODE_TYPE } from '@/enum'
import { AudioPlayer } from '@/hooks/useSpeechRecognition/streamaudioplay'
import { useAppStore } from '@/stores/app'
import { toRefs } from '@vueuse/core'
import { decode } from 'base-64'
import { getRecordSupportMineType } from './getRecordSupportMineType'

interface UseSpeechRecognitionOptions {
  /** 当有新的音频数据块可用时触发 */
  onBlobAvailable?: (blob: Blob) => void
  /** 当停止录音时触发 */
  onStop?: () => void
  /** 当检测到静音时触发 */
  onSilence?: (blob: Blob) => void
  options?: {
    /** 静音检测阈值 */
    silenceThreshold?: number
    /** 静音检测时间 */
    silenceTime?: number
    /** 是否自动检测静音 */
    autoCheckSilence?: boolean
  }
}

// 使用MediaRecorder，但是无法实现paused/resume后正确获取音频。暂时保留逻辑
export const useMediaRecorder = ({
  onBlobAvailable,
  onStop,
  onSilence,
  options = {
    silenceThreshold: 5,
    silenceTime: 1000,
    autoCheckSilence: false,
  },
}: UseSpeechRecognitionOptions = {},
) => {
  const status = ref('inactive')
  const timerInterval = ref<NodeJS.Timeout | null>(null)
  const mediaRecorder = ref<MediaRecorder | null>(null)
  const time = ref(0)
  const hasSilence = ref(false)
  const audioContext = ref<AudioContext | null>(null)
  const analyser = ref<AnalyserNode | null>(null)
  const silenceTimer = ref<NodeJS.Timeout | null>(null)
  const audioChunks = ref<BlobPart[]>([])

  const _startClock = () => {
    timerInterval.value = setInterval(() => {
      time.value++
    }, 1000)
  }

  const _stopClock = () => {
    if (timerInterval.value) {
      clearInterval(timerInterval.value)
    }
    timerInterval.value = null
  }

  const checkSilence = (callback?: () => void) => {
    if (!analyser.value || status.value === 'inactive') {
      return
    }

    const bufferLength = analyser.value.fftSize
    const dataArray = new Uint8Array(bufferLength)
    analyser.value.getByteTimeDomainData(dataArray)

    let sum = 0
    for (let i = 0; i < bufferLength; i++) {
      sum += Math.abs(dataArray[i] - 128)
    }
    const average = sum / bufferLength
    console.log('Average volume:', average) // 添加这行来输出平均音量

    // 检测到静音
    if (options.silenceThreshold && average < options.silenceThreshold) {
      // console.log('Silence detected: ', silenceTimer.value) // 添加这行来确认检测到静音
      // if (!silenceTimer.value) {
      //   console.log('开始计时') // 添加这行来确认静音计时器未设置
      //   silenceTimer.value = setTimeout(() => {
      //     console.log('Silence timer triggered') // 添加这行来确认静音计时器触发
      //     if (mediaRecorder.value && audioChunks.value.length > 0) {
      //       const currentBlob = new Blob(audioChunks.value, { type: mediaRecorder.value.mimeType })
      //       onSilence?.(currentBlob)
      //       audioChunks.value = []
      //       count.value++
      //     }
      //   }, options.silenceTime)
      // }
      console.log('检测到静音')
      if (hasSilence.value) {
        callback?.()
        console.log('说完话了，发送音频！')
        hasSilence.value = false
      }
    }
    else {
      // if (silenceTimer.value) {
      //   clearTimeout(silenceTimer.value)
      //   silenceTimer.value = null
      //   console.log('Silence timer cleared') // 添加这行来确认静音计时器被清除
      // }
      console.log('有声音')

      if (silenceTimer.value) {
        clearTimeout(silenceTimer.value)
        silenceTimer.value = null
        console.log('又说话了，清除静音计时器')
      }
      else {
        silenceTimer.value = setTimeout(() => {
          // if (mediaRecorder.value && audioChunks.value.length > 0) {
          //   const currentBlob = new Blob(audioChunks.value, { type: mediaRecorder.value.mimeType })
          //   onSilence?.(currentBlob)
          //   audioChunks.value = []
          //   count.value++
          // }
          console.log('【说话说完了，准备发送！】')
          hasSilence.value = true
        }, options.silenceTime)
      }
    }
    requestAnimationFrame(() => checkSilence(callback))
  }

  const start = () => {
    audioChunks.value = []
    if (timerInterval.value) {
      return
    }
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then((stream) => {
        status.value = 'recording'
        const recorder = new MediaRecorder(stream, { mimeType: getRecordSupportMineType() })
        mediaRecorder.value = recorder

        audioContext.value = new AudioContext()
        analyser.value = audioContext.value.createAnalyser()
        const source = audioContext.value.createMediaStreamSource(stream)
        source.connect(analyser.value)

        recorder.ondataavailable = (event) => {
          console.log('ondataavailable', event)
          audioChunks.value.push(event.data)
          onBlobAvailable?.(new Blob([event.data], { type: recorder.mimeType }))
        }
        recorder.onpause = () => {
          status.value = 'paused'
        }
        recorder.onresume = (event: Event) => {
          console.log('onresume', event)
          status.value = 'recording'
        }

        recorder.start(100)
        _startClock()
        if (options.autoCheckSilence) {
          checkSilence()
        }
      })
      .catch((error) => {
        console.error('Error useSpeechRecognition:', error)
      })
  }

  const stop = () => {
    return new Promise<void>((resolve) => {
      if (mediaRecorder.value) {
        const onStopHandler = () => {
          status.value = 'inactive'
          _stopClock()
          onStop?.()

          // 停止所有音轨
          mediaRecorder.value!.stream.getTracks().forEach(track => track.stop())

          // 移除事件监听器
          mediaRecorder.value!.removeEventListener('stop', onStopHandler)

          if (silenceTimer.value) {
            clearTimeout(silenceTimer.value)
          }

          resolve()
        }

        // 添加一次性事件监听器
        mediaRecorder.value.addEventListener('stop', onStopHandler, { once: true })

        // 调用 stop 方法
        mediaRecorder.value.stop()
      }
      else {
        resolve()
      }
    })
  }

  const pause = () => {
    return new Promise<void>((resolve) => {
      if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
        mediaRecorder.value.pause()
        // 如果不是自动监测静音，则手动发送音频
        if (!options.autoCheckSilence) {
          const currentBlob = new Blob(audioChunks.value, { type: mediaRecorder.value.mimeType })
          onSilence?.(currentBlob)
          audioChunks.value = []
        }
        resolve()
      }
    })
  }

  const resume = () => {
    return new Promise<void>((resolve) => {
      if (mediaRecorder.value && mediaRecorder.value.state === 'paused') {
        mediaRecorder.value.resume()
        resolve()
      }
    })
  }

  onUnmounted(async () => {
    console.log('onUnmounted')
    await stop()
  })

  return {
    start,
    stop,
    pause,
    resume,
    status,
    time,
  }
}

export const useTTS = ({
  onReceiveAssistantMessage,
  onSilenceAction,
  onReceiveDone,
}: {
  onReceiveAssistantMessage: (object: SSEResponse, userMessage: string) => void
  onSilenceAction: (id: number) => void
  onReceiveDone: () => void
}) => {
  const userMessage = ref('')
  const audioQueue = ref<Uint8Array[]>([])
  const abortController = ref<AbortController | null>(null)
  const audioContext = ref<AudioContext | null>(null)
  const isPlaying = ref(false)
  const { modelSelectInfo } = storeToRefs(useAppStore())
  const { mode } = toRefs(modelSelectInfo)
  const route = useRoute()
  const dialogId = ref(route.params.id ? decode(String(route.params.id)) : '')
  const audioPlayerRef = ref<AudioPlayer | null>(null)

  const { start, stop, pause, resume, status } = useAudioWorkletRecorder({
    onSilence: async (blob) => {
      if (blob) {
        try {
          if (!dialogId.value) {
            const new_id = await setDialogId()
            if (new_id) {
              dialogId.value = new_id
            }
          }
          onSilenceAction(Number(dialogId.value))
          const base64Audio = await convertAudioToBase64(blob)
          await sendAudioAndGetResponse(base64Audio)
        }
        catch (error: any) {
          notify.error({
            title: error?.message || '创建对话失败',
            message: '',
          })
        }
      }
    },
  })

  async function playNextAudioChunk() {
    if (!audioQueue.value.length) {
      isPlaying.value = false
      return
    }
    isPlaying.value = true
    const chunk = audioQueue.value.shift()
    if (!chunk) {
      return
    }

    if (!audioContext.value) {
      audioContext.value = new (window.AudioContext || (window as any)?.webkitAudioContext)()
    }
    try {
      const audioBuffer = await audioContext.value.decodeAudioData(chunk.buffer)
      const source = audioContext.value.createBufferSource()
      source.buffer = audioBuffer
      // 创建增益节点用于淡入淡出
      const gainNode = audioContext.value.createGain()
      source.connect(gainNode)
      gainNode.connect(audioContext.value.destination)

      // 淡入效果
      gainNode.gain.setValueAtTime(0, audioContext.value.currentTime)
      gainNode.gain.linearRampToValueAtTime(1, audioContext.value.currentTime + 0.05)

      // 淡出效果
      gainNode.gain.setValueAtTime(1, audioContext.value.currentTime + audioBuffer.duration - 0.05)
      gainNode.gain.linearRampToValueAtTime(0, audioContext.value.currentTime + audioBuffer.duration)

      source.start()
      source.onended = playNextAudioChunk
    }
    catch (cleanError: any) {
      console.error('无法播放音频:', cleanError.message)
      notify.error({
        title: cleanError?.message || '无法播放音频',
        message: '',
      })
      playNextAudioChunk()
    }
  }
  function stopPlayAudio() {
    if (audioContext.value) {
      audioContext.value.close()
      audioContext.value = null
    }
    audioQueue.value = []
    isPlaying.value = false
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }
    stop()
  }
  async function setDialogId(): Promise<string> {
    const data: string = await services.post('/gpt/createDialog', {
      dialogType: 1,
      name: '新对话',
      type: MODE_TYPE.LEPTON_AUDIO,
      ttsLanguageTypeId: 0,
      ttsType: 0,
      modeId: mode.value,
      contextId: '',
    })
    return data
  }

  function handleStreamResponse(response: any) {
    if (response && response.data && response.code === 200) {
      const { asrResult = '', audio } = response.data as SSEResponse
      userMessage.value = asrResult || userMessage.value
      onReceiveAssistantMessage(response.data, userMessage.value)
      if (audio) {
        const audioData = getBtyes(audio)
        audioQueue.value.push(audioData)
        if (!isPlaying.value) {
          playNextAudioChunk()
        }
      }
    }
  }

  const getAudio = (audio: any) => {
    const byteCharacters = atob(audio)
    const len = byteCharacters.length
    const btyes = new Uint8Array(len)
    for (let i = 0; i < len; i++) {
      btyes[i] = byteCharacters.charCodeAt(i)
    }
    return btyes
  }

  // 确保这个函数被使用
  async function sendAudioAndGetResponse(base64Audio: string) {
    // 每次发送请求前，先取消之前的请求（如果存在）
    if (abortController.value) {
      abortController.value.abort()
    }

    // 创建新的 AbortController
    abortController.value = new AbortController()
    const requestBody = {
      type: 2, // 1: 文本 2: 音频
      prompt: base64Audio,
      requestId: generateUUID(),
      modeId: mode.value,
      dialogId: dialogId.value,
      ttsPresetId: 'jessica',
    }
    const localeLang = localStorage.getItem('localeLang') || ''
    try {
      const url = `${import.meta.env.VITE_APP_GPT_URL}/gpt/completions`
      // const url = `${import.meta.env.VITE_APP_V1_API}/v1/app/tts`
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Jtoken': localStorage.token,
          'x-api-key': 'sdasdsdasgs6bc10lwpy06ta9drv',
          'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
        },
        body: JSON.stringify(requestBody),
        signal: abortController.value.signal,
      })

      const decoder = new TextDecoder('utf-8')
      let isFirstChunk = true
      let accumulatedContent = ''
      let originBuffer = ''

      const transformStream = new TransformStream({
        transform(chunk, controller) {
          originBuffer += decoder.decode(chunk, { stream: true })
          let boundary = originBuffer.indexOf('\n\n')
          while (boundary !== -1) {
            const data = originBuffer.slice(0, boundary)
            originBuffer = originBuffer.slice(boundary + 2)
            boundary = originBuffer.indexOf('\n\n')
            try {
              if (data.includes('[DONE]')) {
                onReceiveDone()
                return
              }
              const object = JSON.parse(data.slice(5)) as { data: { audio: string, content?: string, asrResult?: string } }
              if (object.data.asrResult && isFirstChunk) {
                isFirstChunk = false
              }
              if (object.data.content) {
                accumulatedContent += object.data.content
              }
              if (object.data.audio) {
                controller.enqueue(getAudio(object.data.audio))
              }
            }
            catch (error) {
              console.error(error)
            }
          }
        },
      })

      console.log(accumulatedContent, '累积消息')
      console.log(response.body, 'response.body')

      if (!response.body) { return };

      const outputStream = response.body.pipeThrough(transformStream)
      const outputReader = outputStream.getReader()

      audioPlayerRef.value = new AudioPlayer(outputReader)
      audioPlayerRef.value?.on('canplay', () => {
        console.log('canplay')
        audioPlayerRef.value?.play()
      })

      // if (!response.body || response.status !== 200) {
      //   const httpTextString = await response.text()
      //   const httpText = JSON.parse(httpTextString)
      //   notify.error({
      //     title: httpText.msg,
      //     message: '',
      //   })
      //   throw httpText
      // }

      // const reader = response.body?.getReader()
      // const decoder = new TextDecoder()
      // let buffer = ''

      // while (true) {
      //   const { done, value } = await reader!.read()
      //   if (done) { break }

      //   buffer += decoder.decode(value, { stream: true })
      //   const lines = buffer.split('\n\n')
      //   buffer = lines.pop() || ''

      //   for (const line of lines) {
      //     if (line.startsWith('data:')) {
      //       const data = line.slice(5)
      //       if (data === '[DONE]') {
      //         onReceiveDone()
      //         break
      //       }
      //       try {
      //         const parsed = JSON.parse(data)
      //         handleStreamResponse(parsed)
      //       }
      //       catch (e) {
      //         console.error('解析响应数据时出错:', e)
      //       }
      //     }
      //   }
      // }
    }
    catch (error) {
      console.error('发送请求或处理响应时出错:', error)
    }
  }

  return {
    start,
    stop,
    pause,
    resume,
    status,
    stopPlayAudio,
    userMessage,
  }
}
