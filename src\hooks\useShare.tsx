import { copy } from '@/common'
import services from '@/common/axios'
import i18n from '@/i18n'
import { defineComponent } from 'vue'
import { mixpanel } from '../config/mixpanel'

export function useShareChat() {
  const getUrl = async (messageId: number) => {
    const { shareUrl }: { shareUrl: string } = await services.post('/gpt/getShareUrl', {
      id: messageId,
      lang: i18n.global.locale.value,
    })

    mixpanel.createShareChat(shareUrl)

    return shareUrl
  }
  const shareContent = async (messageId: number, name: string) => {
    const url = await getUrl(messageId)
    return defineComponent({
      setup() {
        const hasCopied = ref(false)
        return () =>
          (
            <p class="pb-6 pt-3 text-primary-300 lt-md:(pb-3 pt-0)">
              <p class="mb-7 lt-md:mb-5">
                <span class="font-bold">
                  「
                  {name.length > 20 ? `${name.slice(0, 20)}...` : name}
                  」
                </span>
                { i18n.global.t('sideBar.shareDialogSubtitle') }
              </p>
              <div class="h-13.5 flex items-center justify-between rounded-7.71 bg-[var(--share-dialog-link-bg)] px-7">
                <span class="of-hidden text-ellipsis whitespace-nowrap text-primary-400">{url}</span>
                <div class="ml-2 cursor-pointer text-18px text-[var(--share-dialog-text)]">
                  {
                    !hasCopied.value
                      ? <i class="i-ju-url" onClick={() => { copy(url); hasCopied.value = true; mixpanel.copyShareChat(url) }} />
                      : <i v-else class="i-ri-check-line" />
                  }
                </div>
              </div>
            </p>
          )
      },
    })
  }

  return {
    shareContent,
  }
}
