/* eslint-disable ts/no-use-before-define */
import { createVNode, getCurrentInstance, nextTick, ref, render } from 'vue'
import type { App, ComponentInternalInstance, SetupContext } from 'vue'
import type { HookModalProps } from './types'

let _app: App

export const useModal = () => {
  const _modalInstance: Ref<ComponentInternalInstance | null> = ref(null)
  const modalRef = ref()
  const appContext = _app?._context || getCurrentInstance()?.appContext
  // 当前模态框是否处于App.vue上下文中
  const isAppChild = ref(false)

  const getModalInstance = async () => {
    await nextTick()
    if (isAppChild.value && modalRef.value) {
      return modalRef.value
    }

    if (_modalInstance.value) {
      return _modalInstance.value
    }
    const container = document.createElement('div')
    const vnode = createVNode(MyModal)
    vnode.appContext = appContext
    render(vnode, container)
    _modalInstance.value = vnode.component!
    _modalInstance.value.props.closeModal = hide
    return _modalInstance.value
  }

  const setProps = async (_props: any) => {
    const instance = await getModalInstance()
    if (Object.is(instance, modalRef.value)) {
      (instance as any)?.setProps?.(_props)
    }
    else {
      (instance as any)?.exposed?.setProps?.(_props)
    }
  }

  const hide = () => {
    setProps({ visible: false })
  }

  const show = async (props: HookModalProps) => {
    setProps({
      ...props,
      visible: true,
    })

    await nextTick()
  }

  const ModalRender = (props: HookModalProps, { attrs, slots }: SetupContext) => {
    isAppChild.value = true
    return <MyModal ref={modalRef} {...{ ...attrs, ...props }} v-slots={slots} />
  }

  onBeforeRouteLeave(() => {
    hide()
  })

  ModalRender.show = show
  ModalRender.hide = hide
  ModalRender.setProps = setProps
  ModalRender._modalInstance = _modalInstance

  // ;[show, hide].forEach(fn => ModalRender[fn.name] = fn)

  return [ModalRender, modalRef] as const
}

export type ModalInstance = ReturnType<typeof useModal>
