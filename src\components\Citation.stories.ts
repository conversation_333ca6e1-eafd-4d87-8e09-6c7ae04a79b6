import type { Meta, StoryObj } from '@storybook/vue3'
import Citation from './Citation.vue'

const meta: Meta<typeof Citation> = {
  title: 'component/Citation',
  component: Citation,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Primary: Story = {

}
