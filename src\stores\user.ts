import type { UserInfo } from '@/model/common'
import { defineStore } from 'pinia'
import avatar from '@/assets/images/user-default-avatar.svg'
import services from '@/common/axios'
import { mixpanel } from '@/config/mixpanel'

interface State {
  userInfo: UserInfo
  userInfoV2: UserInfo
}

export const useUserStore = defineStore('user', {
  state: (): State => ({
    userInfo: {},
    userInfoV2: {},
  }),
  actions: {
    async getUserInfo() {
      const res: UserInfo = await services.get('/user/info', {
        headers: {
          noninductive: true,
        },
      })
      this.userInfo = {
        ...res,
        thumbnailUrl: res.thumbnailUrl
          && import.meta.env.VITE_APP_IMAGE_URL + res.thumbnailUrl,
        upUserThumbnailUrl: res.upUserThumbnailUrl
          ? import.meta.env.VITE_APP_IMAGE_URL + res.upUserThumbnailUrl
          : avatar,
      }

      const email = this.userInfo.email || this.userInfo.googleEmail
      if (email) {
        mixpanel.initUser({ email, name: this.userInfo.name!, avatar: this.userInfo.thumbnailUrl! })
      }
      else {
        console.warn('userInfo.email is empty')
      }
    },

    async getUserInfoV2() {
      const res: UserInfo = await services.get('/user/v2/info', {
        headers: {
          noninductive: true,
        },
      })
      this.userInfoV2 = {
        ...res,
        thumbnailUrl: res.thumbnailUrl
          && import.meta.env.VITE_APP_IMAGE_URL + res.thumbnailUrl,
        upUserThumbnailUrl: res.upUserThumbnailUrl
          ? import.meta.env.VITE_APP_IMAGE_URL + res.upUserThumbnailUrl
          : avatar,
      }

      const email = this.userInfoV2.email || this.userInfoV2.googleEmail
      sessionStorage.email = email
    },
  },
})
