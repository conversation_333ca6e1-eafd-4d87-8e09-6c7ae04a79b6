<template>
  <el-dialog v-model="visible" width="60%" :modal="false" :before-close="onClose">
    <img :src="canvas" class="w-full" />
  </el-dialog>
</template>

<script setup lang="ts">
defineProps({
  canvas: {
    type: String,
    default: '',
  },
})
const visible = defineModel<boolean>('visible', { default: false })
const onClose = () => {
  visible.value = !visible.value
}
</script>

<style lang="scss" scoped>
</style>
