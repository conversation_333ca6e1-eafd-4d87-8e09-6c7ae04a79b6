<template>
  <div v-if="Boolean(references)" class="rounded-10px shadow-xl mb-10px p-4 text-14px flex flex-col gap-2">
    <div>引用内容：{{ references.content }}</div>
    <div>出处：{{ references.referer }}</div>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{ references: string }>()

const references = computed<{ content: string; referer: string }>(() => {
  const regex = /```references([\s\S]*?)```/;
  const matched = props.references.match(regex)
  if (matched) {
    try {
      return JSON.parse(matched[1])
    } catch (error) {
      console.error(error)
      return null
    }
  } else {
    return null
  }
});
</script>
