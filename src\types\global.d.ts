interface Window {
  downloadImage: (encodedUrl: string) => Promise<void>
  downloadTable: (button: HTMLElement) => void
  previewImage: (encodedUrl: string, encodedText: string) => void
}

declare module 'textarea-caret' {
  export default function getCaretCoordinates(
    element: HTMLTextAreaElement,
    position: number,
    options?: {
      debug?: boolean
      includeMargin?: boolean
      client?: boolean
    }
  ): { top: number; left: number; }
}

declare module 'xlsx-js-style';

declare interface ChatDialog {
  id: number;
  shareCode: string
  createTime: string
  groupId: null | number
  modeId: number
  name: string
  ttsLanguageTypeId: null | number
  ttsType: number
  type: number
  updateTime: string
  contextId?: number
}
