<template>
  <div
    class="m-auto w-460px border-rd-7px bg-[#fff] p-70px shadow-[0_50px_30px_0_#00000008]"
  >
    <div class="text-[14px] text-[#37425C]">
      {{ $t("emailBox.emailAddress") }}
    </div>
    <InputBox
      v-model="params.email"
      class="mt-[8px]"
      :placeholder="$t('emailBox.promptEnterEmail')"
      @keydown.enter="submit"
    ></InputBox>
    <div class="pt-[25px] text-[14px] text-[#37425C]">
      {{ $t("emailBox.emailVerificationCode") }}
    </div>
    <InputBox
      v-model="params.code"
      class="mt-[8px]"
      :maxlength="6"
      :placeholder="$t('emailBox.promptEnterVerificationCode')"
      @keydown.enter="submit"
    >
      <template #suffix>
        <div v-debounce="() => getCode()">
          {{
            seconds
              ? $t("loginBox.sendAgain", { seconds })
              : $t("loginBox.getCode")
          }}
        </div>
      </template>
    </InputBox>
    <div v-if="inviteVisible" class="pt-[25px] text-[14px] text-[#37425C]">
      {{ $t("loginBox.inviteCode") }}
    </div>
    <InputBox
      v-if="inviteVisible"
      v-model="params.inviteCode"
      class="mt-[8px]"
      :disabled="disabled"
      :placeholder="$t('emailBox.promptEnterInvitationCode')"
      :maxlength="8"
      @keydown.enter="submit"
    ></InputBox>
    <div
      class="login-box__agreement flex-y-c pt-5"
      :class="{ 'login-box__agreement--active': actived }"
    >
      <div class="checkbox">
        <input v-model="checked" type="checkbox" />
        <Transition name="fade" mode="out-in">
          <img
            v-if="checked"
            src="//hermosssvip.herm.tools/images/checked.png"
            class="checkbox--activited"
            @click="checked = false"
          />
        </Transition>
      </div>
      <div class="pl-2 text-sm">
        <span>{{ $t("emailBox.agreeToTerms") }}</span>
        <span>&nbsp;</span>
        <span
          class="cursor-pointer font-[600]"
          @click="toStatic('Terms of use.html')"
          >{{ $t("baseFooter.terms") }}</span
        >
        <span> & </span>
        <span
          class="cursor-pointer font-[600]"
          @click="toStatic('Privacy policy.html')"
          >{{ $t("loginBox.policy") }}</span
        >
      </div>
    </div>
    <div class="pt-10">
      <ActionButton :loading="loading" @click="submit">{{
        $t("loginBox.loginNow")
      }}</ActionButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { ElNotification } from "element-plus";
import InputBox from "@/components/InputBox.vue";
import ActionButton from "@/components/ActionButton.vue";
import services from "@/common/axios";
import router from "@/router";
import { useGlobalI18n } from "@/hooks/useGlobalI18n";

const t = useGlobalI18n();
const route = useRoute();
const regular = /^[\w.%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const checked = ref(false);
const actived = ref(false);
const loading = ref(false);
const seconds = ref(0);
const disabled = ref(false);
const inviteVisible = ref(false);
const params: any = ref({
  email: "",
  code: "",
});
const submit = async () => {
  if (loading.value) {
    return;
  }
  if (!params.value.email) {
    ElNotification.error({ title: t("emailBox.fillEmailAddress") });
    return;
  }
  if (!regular.test(params.value.email)) {
    ElNotification.error({ title: t("headerBar.enterCorrectEmailAccount") });
    return;
  }
  if (!params.value.code) {
    ElNotification.error({ title: t("emailBox.fillVerificationCode") });
    return;
  }
  if (params.value.code.length !== 6) {
    ElNotification.error({ title: t("loginBox.codeFormatTip") });
    return;
  }
  if (!checked.value) {
    actived.value = true;
    setTimeout(() => {
      actived.value = false;
    }, 500);
    ElNotification.error({
      title: t("loginBox.agreeTip"),
    });
    return;
  }
  loading.value = true;
  try {
    const { token, exchange }: any = await services.post(
      "/user/email/regLogin",
      { ...params.value },
      {
        headers: {
          noninductive: true,
        },
      }
    );
    localStorage.setItem("token", token);
    router.push({
      name: exchange ? "Gift" : "Chat",
    });
  } catch ({ code }: any) {
    if ([402].includes(code)) {
      if (inviteVisible.value) {
        ElNotification.error({
          title: t("emailBox.promptEnterInvitationCode"),
        });
      } else {
        inviteVisible.value = true;
      }
    } else {
      loading.value = false;
    }
  } finally {
    loading.value = false;
  }
};
const toStatic = (string: any) => {
  window.open(`${location.origin}/frame?static=${string}`, "_blank");
};
const getCode = async () => {
  if (!params.value.email) {
    ElNotification.error({ title: t("emailBox.promptEnterEmail") });
    return;
  }
  if (!regular.test(params.value.email)) {
    ElNotification.error({ title: t("headerBar.enterCorrectEmailAccount") });
    return;
  }
  if (seconds.value) {
    return;
  }

  await services.post(
    "/user/email/sendCaptcha",
    {
      email: params.value.email,
      type: 1,
    },
    {
      headers: {
        noninductive: true,
      },
    }
  );
  ElNotification.success({ title: t("loginWays.sendEmailMessage") });
  seconds.value = 120;
  const interval = setInterval(() => {
    seconds.value--;
    if (seconds.value <= 0) {
      clearInterval(interval);
    }
  }, 1000);
};
onMounted(() => {
  if (route.query.code) {
    disabled.value = true;
    params.value.inviteCode = JSON.parse(
      JSON.stringify(route.query.code)
    ).substring(0, 8);
  } else {
    disabled.value = false;
  }
});
</script>

<style lang="scss" scoped>
.login-box__agreement--active {
  animation: shake 0.3s;
}
</style>
