import { useThemeStore } from '@/stores/theme'
import { useDark } from '@vueuse/core'

export type ThemeMode = keyof typeof THEME_MAP
// 主题类型映射表
export const THEME_MAP = {
  auto: 0,
  dark: 1,
  light: 2,
} as const

export function useThemeChange() {
  const themeStore = useThemeStore()
  const isDark = useDark({
    valueDark: 'dark hljs-dark',
    valueLight: 'light hljs-light',
  })
  const { store: colorModeStore, system: uiColorMode } = useColorMode()

  const setTheme = (theme: ThemeMode) => {
    // 获取当前实际的主题值（light/dark）
    const currentActualTheme
      = colorModeStore.value === 'auto'
        ? uiColorMode.value
        : colorModeStore.value

    // 计算新主题的实际值
    const newActualTheme = theme === 'auto' ? uiColorMode.value : theme

    // 如果实际主题发生变化，需要播放动画
    if (newActualTheme !== currentActualTheme) {
      themeStore.setShowThemeTransition(true)
      setTimeout(() => {
        colorModeStore.value = theme
      }, 100)
    }
    else {
      // 即使实际主题没变，也要更新存储值，让菜单正确高亮
      colorModeStore.value = theme
    }
  }

  watch(colorModeStore, async (newVal, oldVal) => {
    if (newVal !== oldVal) {
      setTimeout(() => {
        themeStore.setShowThemeTransition(false)
      }, 2000)
    }
  })

  return {
    isDark,
    setTheme,
  }
}
