import type { Component, ExtractPropTypes, PropType } from 'vue'

export const ModalPropsDefine = {
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
} as const

export type ModalProps = ExtractPropTypes<typeof ModalPropsDefine>
// 普通模态框.
export interface HookModalProps extends Partial<ModalProps> {
  getContainer?: Element
  content: string | (() => Component)
  headerContent?: string | (() => Component)
  closeModal?: Function
  openModal?: Function
  modalClass?: string
  style?: Record<string, string>
}
