<script setup lang="ts">
</script>

<template>
  <div class="arrow-animation flex flex-col">
    <i class="i-ri-arrow-down-s-line"></i>
    <i class="i-ri-arrow-down-s-line"></i>
    <i class="i-ri-arrow-down-s-line"></i>
  </div>
</template>

<style lang="scss" scoped>
.arrow-animation {
  i {
    @apply text-12px text-[#fff] lh-7px font-bold;

    &:nth-child(1) {
      animation: color-cycle 3s infinite;
    }

    &:nth-child(2) {
      animation: color-cycle 3s infinite;
      animation-delay: 1s;
    }

    &:nth-child(3) {
      animation: color-cycle 3s infinite;
      animation-delay: 2s;
    }
  }
}

@keyframes color-cycle {
  0% {
    opacity: 0.2;
  }

  33% {
    opacity: 0.5;
  }

  66% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>
