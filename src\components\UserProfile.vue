<script lang="ts" setup>
import type { PropType } from 'vue'
import type { MenuChildren, Menus } from './UserInfo.vue'
import { useAppStore } from '@/stores/app'
import { useThemeStore } from '@/stores/theme'
import MarkUpdated from '@/components/MarkUpdated.vue'

const props = defineProps({
  menus: { type: Array as PropType<Menus[]>, default: () => [] },
  showSubmenuArrow: { type: Boolean, default: true },
  menuClass: { type: String, default: '' },
})
const emit = defineEmits(['handleAction'])

const { expandSidebar, isPc } = storeToRefs(useAppStore())

const handleAction = (childMenu: MenuChildren) => {
  if (childMenu.action) {
    childMenu.action()
    if (!isPc.value) {
      expandSidebar.value = false
    }
    emit('handleAction', childMenu)
  }
}

const { store } = useColorMode()

const getThemeClass = (themeStr: string) => {
  return store.value === themeStr
    ? 'text-[var(--theme-current-menu-text)]'
    : 'text-[var(--theme-not-current-menu-text)]'
}
</script>

<template>
  <div class="rounded-2.5 bg-model-select-bg py-lg text-sm text-primary-300" :class="menuClass">
    <ul>
      <template v-for="(menu, i) in props.menus" :key="menu.group">
        <li class="space-y-1">
          <div class="mx-3.5 space-y-1">
            <p v-if="menu.groupTitle"
              class="mb-2 px-4 text-4 text-primary-700 font-700 font-RobotoSlab dark:text-secondary-foreground">{{
                menu.groupTitle }}</p>
            <div v-for="(childMenu, index) in menu.children" :key="index" :class="[
              childMenu.action && 'hover:(bg-[var(--user-info-bg)] text-black) cursor-pointer',
              childMenu.theme && getThemeClass(childMenu.theme),
            ]" class="menu-item h-8.75 min-w-50 flex items-center justify-between rounded-md px-4">
              <el-popover v-if="childMenu.children"
                popper-class="min-w-auto! w-auto! p-0! z-10000! shadow-[0_5px_20px_0_#00000012]! lt-md:shadow-none!"
                trigger="click" placement="left-start" :offset="64" :persistent="false" :show-arrow="false"
                :hide-after="0">
                <template #default>
                  <UserProfile :menus="childMenu.children" :class="menuClass" :show-submenu-arrow="showSubmenuArrow">
                  </UserProfile>
                </template>
                <template #reference>
                  <div class="h-full w-full flex items-center justify-between">
                    <div class="w-full flex-y-c space-x-8px">
                      <i class="w-19px align-text-top text-18px" :class="[childMenu.icon]"></i>
                      <span class="line-clamp-1">
                        <template v-if="childMenu.version">
                          <MarkUpdated :mark="childMenu.icon" closest=".el-tooltip__trigger" :version="childMenu.version">
                            {{ childMenu.name }}
                          </MarkUpdated>
                        </template>
                        <template v-else>
                          {{ childMenu.name }}
                        </template>
                      </span>
                      <span v-if="childMenu.remark">{{ childMenu.remark }}</span>
                    </div>
                    <i v-if="showSubmenuArrow" class="i-ju-arrow-right h-[0.75em] w-[0.75em]" />
                  </div>
                </template>
              </el-popover>
              <div v-else class="h-full w-full flex items-center justify-between" @click="handleAction(childMenu)">
                <div class="w-full flex-y-c space-x-8px">
                  <i class="w-19px align-text-top text-18px" :class="[childMenu.icon]"></i>
                  <span class="line-clamp-1" :class="childMenu.class">
                    <template v-if="childMenu.version">
                      <MarkUpdated :mark="childMenu.icon" closest=".el-tooltip__trigger" :version="childMenu.version">
                        {{ childMenu.name }}
                      </MarkUpdated>
                    </template>
                    <template v-else>
                      {{ childMenu.name }}
                    </template>
                  </span>
                  <span v-if="childMenu.remark" class="text-primary-700 font-RobotoSlab">{{ childMenu.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </li>

        <div v-if="i !== props.menus.length - 1" class="mb-4 mt-2 w-full pl-8">
          <p class="h-[1px] w-full bg-[#F0F3F6] dark:bg-[#272727]"></p>
        </div>
      </template>
    </ul>
  </div>
</template>
