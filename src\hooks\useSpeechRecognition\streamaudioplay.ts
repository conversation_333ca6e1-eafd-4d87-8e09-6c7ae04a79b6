interface AudioPlayerEventArgs {
  loadDone: () => void // 音频加载完成事件
  stop: () => void // 播放停止事件
  durationUpdate: (duration: number) => void // 音频时长更新事件
  timeupdate: () => void // 播放时间更新事件
  canplay: () => void // 音频可以播放事件
}

type AudioPlayerEvent = keyof AudioPlayerEventArgs // 事件类型，表示可能的事件名称

export class AudioPlayer {
  constructor(private reader: ReadableStreamDefaultReader<Uint8Array>) {
    // 使用传入的流读取器初始化播放器
    this.downloadByReader()

    // 绑定播放、暂停和事件触发方法的上下文
    this.play = this.play.bind(this)
    this.pause = this.pause.bind(this)
    this.emit = this.emit.bind(this)
    this.setVolume(1) // 设置最大音量
  }

  // body 全部加载完成
  private loadDone = false

  // 流媒体播放状态待播放队列，用来处理播放中途暂停
  private playQueue: Float32Array[] | null = null
  // 全部音频数据
  private allData: Float32Array[] = []
  // 采样率
  private sampleRate = 44100 // Hz
  // 总时长缓存
  private durationCache = -1

  // 播放器状态
  private playStatus: 'playing' | 'pause' | 'stop' | 'pending' = 'stop'

  // 事件监听器
  private listeners: Record<AudioPlayerEvent, Function[]> = {
    loadDone: [],
    stop: [],
    durationUpdate: [],
    timeupdate: [],
    canplay: [],
  }

  // 注册事件监听器
  on<E extends AudioPlayerEvent, Listener extends AudioPlayerEventArgs[E]>(
    event: E,
    listener: Listener,
  ) {
    // 移除同类型事件的已有监听器，防止重复
    this.listeners[event] = this.listeners[event].filter(
      lisItem => lisItem === listener,
    )
    // 添加新的监听器
    this.listeners[event].push(listener)
  }

  // 移除事件监听器
  off<E extends AudioPlayerEvent, Listener extends AudioPlayerEventArgs[E]>(
    event: E,
    listener: Listener,
  ) {
    // 仅保留不等于传入监听器的监听器
    this.listeners[event] = this.listeners[event].filter(l => l !== listener)
  }

  // 触发事件
  private emit(event: AudioPlayerEvent, ...args: any[]) {
    // 调用所有监听器
    this.listeners[event].forEach(listener => listener(...args))
  }

  // 计算音频总时长
  private computeTime() {
    const audioContext = new AudioContext()
    const playList: Float32Array[] = []

    playList.push(...this.allData.slice())

    const chunk = new Float32Array(
      playList.reduce((acc, val) => acc + (val?.length || 0), 0),
    )
    let offset = 0
    playList.forEach((data) => {
      chunk.set(data!, offset)
      offset += data!.length
    })

    const audioBuffer = audioContext.createBuffer(
      1,
      chunk.length,
      this.sampleRate,
    )
    audioBuffer.copyToChannel(chunk, 0)
    this.durationCache = audioBuffer.duration
    audioContext.close()
    this.emit('durationUpdate', this.durationCache) // 触发时长更新事件
  }

  // 处理加载完成的事件
  private loadDoneHandle() {
    this.loadDone = true
    this.emit('loadDone') // 触发加载完成事件
    this.computeTime() // 计算总时长
  }

  // 使用 reader 下载音频数据
  async downloadByReader() {
    // 导入 MPEG 解码器
    const { MPEGDecoderWebWorker } = await import('mpg123-decoder')
    let receivedLength = 0
    let buffer = new Uint8Array(0)
    const startTime = performance.now()
    let ttft = -1
    const decoder = new MPEGDecoderWebWorker() // 创建解码器实例
    let canplayVerb = false
    let triggerFn = null
    while (true) {
      // 读取数据
      const { done, value } = await this.reader.read()
      if (done) {
        // console.log("下载完成");
        triggerFn?.then(() => {
          decoder.free() // 释放解码器资源
          this.loadDoneHandle()
        })
        break
      }

      // 处理接收到的数据块
      // console.log("接收到数据块，大小：", value.length);
      buffer = this.concatTypedArrays(buffer, value)
      receivedLength += value.length

      const currentTime = performance.now()
      if (ttft < 0) {
        ttft = currentTime - startTime
      }

      // 获取最大长度可被 2 整除
      const max_length = Math.floor(buffer.length / 2) * 2
      const chunk = buffer.slice(0, max_length)
      buffer = buffer.slice(max_length)
      triggerFn = decoder.ready.then(() => {
        decoder
          .decode(chunk)
          .then((result: { channelData: Float32Array[] }) => {
            const { channelData } = result
            const [leftAudioData] = channelData
            // console.log("解码后的音频数据大小：", leftAudioData.length);
            if (leftAudioData.length > 0) {
              this.allData.push(leftAudioData)
              if (this.playQueue) {
                this.playQueue.push(leftAudioData)
              }
              else {
                this.playQueue = [leftAudioData]
              }
              if (!canplayVerb) {
                this.emit('canplay') // 触发可以播放事件
                canplayVerb = true
              }
            }
            else {
              console.warn('解码后的音频数据长度为0，跳过')
            }
          })
          .catch((err: unknown) => {
            console.error('解码错误：', err)
          })
      })
    }
  }

  // 合并两个 TypedArray
  concatTypedArrays(a: Uint8Array, b: Uint8Array) {
    const c = new Uint8Array(a.length + b.length)
    c.set(a, 0)
    c.set(b, a.length)
    return c
  }

  // 循环播放 playlist 中的音频块
  private audioContext: AudioContext | null = null
  private volumeProxy: number = 1
  // gain 节点用于管理当前播放器音量
  private gainNode: GainNode | null = null
  private startTime: number = 0
  private offsetTime: number = 0
  private waitTimer: ReturnType<typeof setTimeout> | null = null
  private timeUpdateTimer: ReturnType<typeof setInterval> | null = null

  // 将 Int16Array 转换为 Float32Array
  int16ToFloat32(int16Array: Int16Array) {
    const float32Array = new Float32Array(int16Array.length)
    for (let i = 0; i < int16Array.length; i++) {
      float32Array[i] = int16Array[i] / 32768.0
    }
    return float32Array
  }

  // 触发时间更新事件
  timeUpdateFn() {
    if (this.playStatus === 'playing') {
      this.emit('timeupdate', this.currentTime)
    }
  }

  // 开始播放
  play() {
    // console.log("开始播放，队列长度：", this.playQueue?.length);
    // 如果播放队列为空
    if (!this.playQueue || this.playQueue.length === 0) {
      // 全部放完了或者还没有数据，等待
      if (this.loadDone) {
        this.stop()
        return
      }
      // 需要等待，100ms后重新尝试
      this.playStatus = 'pending'
      this.waitTimer = setTimeout(() => this.play(), 100)
      return
    }
    this.waitTimer = null

    // 确定是否需要创建新的 AudioContext
    const newContext = this.audioContext === null
    if (this.audioContext === null) {
      this.audioContext = new AudioContext()
      this.timeUpdateTimer = setInterval(() => this.timeUpdateFn(), 200)
    }

    // 恢复 AudioContext
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume()
    }
    this.offsetTime = this.audioContext.currentTime

    const playList = this.playQueue.slice()
    this.playQueue = []

    const totalLength = playList.reduce((acc, val) => acc + val.length, 0)
    if (totalLength === 0) {
      console.warn('音频数据长度为0，跳过此次播放')
      this.waitTimer = setTimeout(() => this.play(), 100)
      return
    }

    // 合并所有播放队列中的数据
    const chunk = new Float32Array(totalLength)
    let offset = 0
    playList.forEach((data) => {
      chunk.set(data, offset)
      offset += data.length
    })

    // console.log("准备播放的音频数据大小：", chunk.length);

    try {
      const audioBuffer = this.audioContext.createBuffer(
        1,
        chunk.length,
        this.sampleRate,
      )
      audioBuffer.copyToChannel(chunk, 0)

      const source = this.audioContext.createBufferSource()
      source.buffer = audioBuffer

      this.gainNode = this.audioContext.createGain()
      source.connect(this.gainNode)
      this.gainNode.connect(this.audioContext.destination)
      this.gainNode.gain.value = this.volumeProxy

      // 播放结束后重新调用 play 方法
      source.onended = () => {
        this.startTime = this.currentTime
        setTimeout(() => this.play(), 0)
      }

      if (newContext) {
        source.start(0, this.startTime)
      }
      else {
        source.start()
      }

      // ("音频源已开始播放");
      this.playStatus = 'playing'
    }
    catch (error) {
      console.error('创建或播放音频缓冲区时出错：', error)
      this.waitTimer = setTimeout(() => this.play(), 100)
    }
  }

  // 清理 AudioContext 的资源
  private clearAudioContext() {
    if (this.audioContext) {
      this.audioContext.suspend()
      this.audioContext.close()
      this.audioContext = null
      this.offsetTime = 0
      this.gainNode = null
    }
    if (this.waitTimer) {
      clearTimeout(this.waitTimer)
    }
    if (this.timeUpdateTimer) {
      clearInterval(this.timeUpdateTimer)
    }
    this.playQueue = null
  }

  // 暂停播放
  pause() {
    if (this.audioContext && this.playStatus !== 'pending') {
      // 记录暂停位置，如果是 pending 状态，则以当前时间为准（原因：Pending 下 audioContext 仍然在计时）
      this.startTime = this.currentTime
    }
    this.clearAudioContext()
    this.playStatus = 'pause'
    this.emit('stop')
  }

  // 停止播放
  stop() {
    this.clearAudioContext()
    this.startTime = 0
    this.playStatus = 'stop'
    this.emit('stop')
    this.emit('timeupdate', 0)
  }

  // 关闭播放器
  close() {
    this.audioContext?.close()
  }

  // 获取音频总时长
  get duration() {
    return this.durationCache
  }

  // 音量操作
  get volume() {
    return this.gainNode?.gain.value || this.volumeProxy
  }

  set volume(val: number) {
    this.volumeProxy = val
    if (this.gainNode) {
      this.gainNode.gain.value = val
    }
  }

  // 设置当前播放时间
  set currentTime(val: number) {
    if (this.playStatus === 'playing' || this.playStatus === 'pending') {
      this.stop()
      this.startTime = val
      this.play()
      return
    }
    this.startTime = val
  }

  // 获取当前播放时间
  get currentTime() {
    if (this.playStatus === 'playing') {
      return (
        (this.audioContext?.currentTime || 0) + this.startTime - this.offsetTime
      )
    }
    return this.startTime
  }

  // 设置音量
  setVolume(volume: number) {
    this.volumeProxy = volume
    if (this.gainNode) {
      this.gainNode.gain.value = volume
    }
  }
}
