html {
  font-size: 16px;

  --header-bar-height: 78px;
}

body {
  background-color: var(--p);
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

/* element-plus loading解决自定义组件Modal的zIndex层级问题，置顶 */
.container-loading {
  z-index: 999999 !important;
}

.at-float-container {
  position: fixed;
  display: none;
  z-index: 9999999;

  &.open {
    display: block;
  }
}

.smooth-item,li::marker {
  animation-name: ft-fadeIn;
  animation-duration: 0.6s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
  white-space: pre-wrap;
  display: inline;
}

@keyframes ft-fadeIn {
  0% {
      opacity: 0
  }

  to {
      opacity: 1
  }
}
