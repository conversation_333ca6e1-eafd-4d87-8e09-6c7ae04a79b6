<template>
  <div class="verification-code-input">
    <input
      v-for="(code, index) in codes"
      :key="index"
      :ref="el => inputRefs[index] = el as HTMLInputElement"
      type="text"
      :value="code"
      maxlength="1"
      inputmode="numeric"
      :class="{
        active: activeIndex === index,
      }"
      @input="handleInput(index, $event)"
      @paste="handlePaste"
      @keydown="handleKeydown(index, $event)"
      @click="handleClick(index)"
      @focus="handleFocus(index)"
      @blur="handleBlur"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  length?: number
}

const props = withDefaults(defineProps<Props>(), {
  length: 6,
})

const modelValue = defineModel<string>('modelValue', { default: '' })

// 存储每个格子的值
const codes = ref<string[]>(Array.from({ length: props.length }, () => ''))
// 当前激活的输入框索引
const activeIndex = ref(-1)
// 所有输入框的引用
const inputRefs = ref<HTMLInputElement[]>([])

// 添加一个新的ref来跟踪是否有任何输入框处于focus状态
const isFocused = ref(false)

// 初始化输入框引用数组
onMounted(() => {
  inputRefs.value = Array.from({ length: props.length })
  // 初始化时填充已有的值
  if (modelValue.value) {
    const values = modelValue.value.split('')
    codes.value = Array.from({ length: props.length }).fill('').map((_, i) => values[i] || '')
  }

  // 自动聚焦到第一个输入框
  nextTick(() => {
    activeIndex.value = 0
    focusInput(0)
  })
})

// 获取第一个空格子的索引
const getFirstEmptyIndex = () => {
  return codes.value.findIndex(code => !code)
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedText = event.clipboardData?.getData('text') || ''
  const numbers = pastedText.split('').slice(0, props.length)

  if (numbers.length > 0) {
    // 从当前激活的索引开始填充
    const startIndex = activeIndex.value >= 0 ? activeIndex.value : 0
    const newCodes = [...codes.value]

    numbers.forEach((num, index) => {
      const targetIndex = startIndex + index
      if (targetIndex < props.length) {
        newCodes[targetIndex] = num
      }
    })

    codes.value = newCodes
    updateModelValue()

    // 移动焦点到最后一个填充的位置或最后一个格子
    const nextIndex = Math.min(startIndex + numbers.length, props.length - 1)
    activeIndex.value = nextIndex
    focusInput(nextIndex)
  }
}

// 处理输入事件
const handleInput = (index: number, event: Event) => {
  const input = event.target as HTMLInputElement
  const value = input.value.slice(-1) // 只取最后一个字符

  // 只允许在当前位置是第一个空格子或当前格子已有值的情况下输入
  const firstEmptyIndex = getFirstEmptyIndex()
  if (index === firstEmptyIndex || codes.value[index]) {
    if (/^\d$/.test(value)) {
      codes.value[index] = value
      updateModelValue()

      // 如果不是最后一个格子，移动到下一个
      if (index < props.length - 1) {
        activeIndex.value = index + 1
        focusInput(index + 1)
      }
    }
  }
  else {
    // 如果不是有效的输入位置，将焦点移到第一个空格子
    const validIndex = firstEmptyIndex === -1 ? props.length - 1 : firstEmptyIndex
    activeIndex.value = validIndex
    focusInput(validIndex)
  }

  // 总是清空输入框的值，让显示由codes数组控制
  input.value = ''
}

// 处理删除事件
const handleKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace') {
    event.preventDefault() // 阻止默认的删除行为

    if (codes.value[index]) {
      // 如果当前格子有值，清空当前格子
      codes.value[index] = ''
      updateModelValue()
      // 保持在当前格子
      activeIndex.value = index
    }
    else if (index > 0 && !codes.value[index]) {
      // 如果当前格子无值，直接删除前一个格子的值
      codes.value[index - 1] = ''
      updateModelValue()
      // 移动到前一个格子
      activeIndex.value = index - 1
      focusInput(index - 1)
    }
  }
}

// 处理点击事件
const handleClick = (index: number) => {
  const firstEmptyIndex = getFirstEmptyIndex()

  if (codes.value[index]) {
    // 如果点击的格子有值，允许选中修改
    activeIndex.value = index
    inputRefs.value[index]?.select()
  }
  else if (firstEmptyIndex === index) {
    // 如果点击的是第一个空格子，允许输入
    activeIndex.value = index
    focusInput(index)
  }
  else {
    // 否则，焦点移到第一个空格子或最后一个格子
    const validIndex = firstEmptyIndex === -1 ? props.length - 1 : firstEmptyIndex
    activeIndex.value = validIndex
    focusInput(validIndex)
  }
}

// 处理失去焦点事件
const handleBlur = () => {
  activeIndex.value = -1
  isFocused.value = false
}

// 处理获得焦点事件
const handleFocus = (index: number) => {
  isFocused.value = true
  // 不再复用点击逻辑，只更新 activeIndex
  const firstEmptyIndex = getFirstEmptyIndex()

  if (codes.value[index]) {
    // 如果当前格子有值，直接激活但不选中
    activeIndex.value = index
  }
  else if (firstEmptyIndex === index) {
    // 如果是第一个空格子，允许输入
    activeIndex.value = index
  }
  else {
    // 否则，移动到第一个空格子或最后一个格子
    const validIndex = firstEmptyIndex === -1 ? props.length - 1 : firstEmptyIndex
    activeIndex.value = validIndex
    focusInput(validIndex)
  }
}

// 聚焦指定输入框
const focusInput = (index: number) => {
  if (inputRefs.value[index]) {
    inputRefs.value[index].focus()
  }
}

// 更新v-model值
const updateModelValue = () => {
  // 保持空格子的位置，将数组转换为字符串
  modelValue.value = codes.value.join('')
}

// 暴露isFocused状态
defineExpose({
  isFocused,
})
</script>

<style lang="scss" scoped>
.verification-code-input {
  caret-color: #F65134;
  display: flex;
  justify-content: space-between;

  &::selection {
    color: #fff;
    background-color: #F65134;
  }

  &::first-line {
    color: #F65134;
  }

  input {
    width: 50px;
    height: 50px;
    text-align: center;
    border: 1px solid var(--login-code-input-border);
    border-radius: 8px;
    font-size: 22px;
    transition: all 0.2s var(--ani-bezier);
    outline: none;
    cursor: pointer;

    @apply lt-lg:size-40px lt-lg:text-18px;

    &.active {
      border-color: #F65134;
      box-shadow: 0 0 0 2px #F65134;
    }
  }
}
</style>
