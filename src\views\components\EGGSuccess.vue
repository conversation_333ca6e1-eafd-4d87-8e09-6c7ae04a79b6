<template>
  <!-- EGG SUCCESS START -->
  <div class="egg-success-box absolute w-full flex flex-col items-center">
    <Transition
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div
        v-if="!thankYouAnimateEnd"
        ref="lottieElement"
        class="thanks-box fixed"
      ></div>
    </Transition>
    <Transition
      enter-active-class="animate__animated animate__fadeInDown"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div v-if="thankYouAnimateEnd" class="text-center">
        <h1 class="success-title1">
          {{ $t("eGGSuccess.greet") }} {{ userInfo.name }}!
        </h1>
        <h2 class="success-title2">{{ $t("eGGSuccess.congratulations") }}</h2>
      </div>
    </Transition>
    <div class="relative w-full flex justify-center">
      <Transition
        enter-active-class="animate__animated animate__fadeInUp"
        leave-active-class="animate__animated animate__fadeOut"
      >
        <div
          v-if="thankYouAnimateEnd && giftInfo.title"
          class="success-data absolute"
        >
          <div class="common-border">
            <div class="flex items-center justify-between">
              <div class="line1-left">{{ giftInfo.title }}</div>
              <div class="line1-right">{{ giftInfo.ticketNo }}</div>
            </div>
            <div class="flex items-center justify-between">
              <div class="line2-left">
                {{ $t("eGGSuccess.subscribePackage") }} : {{ giftInfo.title }}
              </div>
              <div class="line2-right">
                {{ moment(giftInfo.expireTime).format("YYYY.MM.DD") }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div class="line3-left">
                {{ $t("eGGSuccess.cycle") }} : {{ formatUnitPlus({ amount: giftInfo.expireDays }) }}
              </div>
              <div class="line3-right">{{ $t("eGGSuccess.serialNumber") }} {{ eggCode }}</div>
            </div>
            <div class="flex items-center justify-between">
              <div class="line4-left">
                {{ $t("eGGSuccess.deadline") }}  :
                {{ moment(giftInfo.expireTime).format("YYYY.MM.DD") }}
              </div>
              <div class="line4-right">
                <svg ref="barCodeBox"></svg>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
  <!-- EGG SUCCESS end -->
</template>

<script setup lang="ts">
// import services from '@/common/axios'

import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import JsBarcode from "jsbarcode";
import moment from "moment";
import lottie from "lottie-web";
import ThankYouLottieJSON from "@/assets/json/lottie-thankyou.json";
import { useUserStore } from "@/stores/user";
import services from "@/common/axios";

const props = defineProps({
  giftInfo: {
    type: Object,
    default: () => {},
  },
});

const router = useRouter();

const thankYouAnimateEnd = ref(false);

const lottieElement = ref();
const barCodeBox = ref();
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const eggCode = ref("");
getInfo();

onMounted(() => {
  nextTick(() => {
    const lottieThankyou = lottie.loadAnimation({
      container: lottieElement.value, // the dom element
      renderer: "svg",
      loop: false,
      autoplay: true,
      animationData: ThankYouLottieJSON, // the animation data
    });

    lottieThankyou.setSpeed(0.6);
    lottieThankyou.addEventListener("complete", () => {
      goChat();
      thankYouAnimateEnd.value = true;
      if (props.giftInfo.title) {
        nextTick(() => {
          if (barCodeBox.value) {
            JsBarcode(barCodeBox.value, String(props.giftInfo.ticketNo), {
              format: "CODE39",
              width: 1.8,
              height: 18,
              displayValue: false,
              textMargin: 0,
              fontSize: 15,
              margin: 0, // 设置条形码周围的空白边距
            });
          }
        });
      }
    });
  });
});

let goTimer: ReturnType<typeof setTimeout> | number;
function goChat() {
  if (goTimer) {
    clearTimeout(goTimer);
  }
  goTimer = setTimeout(() => {
    router.replace({
      name: "Chat",
    });
  }, 2200);
}

async function getInfo() {
  await userStore.getUserInfo();
}

const formatUnitPlus = ({ amount }: any) => {
  let string: any = "";
  switch (amount) {
    case 1:
      string = "One Day";
      break;
    case 31:
      string = "One Month";
      break;
    case 365:
      string = "One Year";
      break;
    default:
      string = `${amount}Days`;
      break;
  }
  return string;
};
</script>

<style lang="scss" scoped>
/* stylelint-disable font-family-no-missing-generic-family-keyword */
/* stylelint-disable no-descending-specificity */
/* stylelint-disable custom-property-pattern */
/* stylelint-disable at-rule-no-unknown */
@import "@/assets/styles/typed";

:root {
  --animate-duration: 300ms;
  --animate-delay: 0.9s;
}

.g-btn-wrapper {
  width: 100%;
}

.egg-success-box {
  line-height: 1;

  .line1-left {
    font-size: 32px;
    font-style: italic;
  }

  .line1-right {
    font-family: EXEPixelPerfect;
    font-size: 80px;
  }

  .line2-left,
  .line3-left,
  .line4-left {
    font-size: 14px;
    white-space: nowrap;
  }

  .line4-right {
    margin-left: 26px;

    svg {
      width: 100%;
      max-height: 30px;
    }
  }

  .line3-right,
  .line2-right {
    font-size: 18px;
    font-weight: lighter;
    font-family: "01 Digit";
    text-transform: uppercase;
  }

  .success-title1 {
    font-size: 48px;
    font-style: italic;
    font-weight: lighter;
  }

  .success-title2 {
    font-size: 80px;
    font-style: italic;
    font-weight: 500;
  }

  .thanks-box {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding: 200px;
  }

  .common-border {
    margin-top: 40px;
    width: 570px;

    > div:not(:first-child) {
      margin: 10px 0;
    }
  }
}

.common-border {
  border: 1px dashed #fff;
  border-radius: 10px;
  position: relative;
  padding: 10px 24px;

  &.message-item {
    border-radius: 5px;
    padding: 15px 20px 10px;

    &::before {
      border-radius: 5px;
    }
  }

  &::before {
    content: "";
    position: absolute;
    pointer-events: none;
    width: 100%;
    height: 100%;
    border: 1px dashed #979797;
    border-radius: 10px;
    left: 8px;
    top: 8px;
  }
}

.login-btn {
  background: rgba(white, 0.1);
  border: 1px solid white;
  border-radius: 10px;
  padding: 15px 0;
  position: relative;
  line-height: 1;
  overflow: hidden;
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    background: rgba(white, 0.2);

    .icon-box {
      &::after {
        transform: skewX(0);
      }
    }
  }

  .login-input {
    margin-left: 40px;
    text-align: center;
  }

  .login-btn-txt {
    margin-left: 25px;
    font-size: 14px;
  }

  .icon-box {
    padding: 0 15px;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 18px;
    }

    &::after {
      content: "";
      transform: skewX(-6deg);
      transition: 0.3s;
      position: absolute;
      inset: -1px 0 -1px -4px;
      border: 1px solid white;
      border-left: none;
      border-radius: 10px 10px 14px;
    }
  }
}

.egg-box {
  .login-tip-text {
    margin: 40px 0;
  }

  .x {
    margin: 0 15px;
  }

  .invitation-code-owner {
    font-size: 18px;
    font-style: italic;
  }

  .avatar {
    width: 36px;
    height: 36px;
    margin-right: 12px;
    border-radius: 50%;
    border: 1px solid white;
    background: rgba(white, 0.2);
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.section-box {
  min-height: 320px;
}

.mt25 {
  margin-top: 25px;
}

.login-text-btn {
  margin-top: 15px;

  &:hover {
    .text {
      opacity: 1;
    }
  }

  .text {
    transition: 0.3s;
    opacity: 0.5;
    line-height: 1;
  }

  .icon-arrow {
    font-size: 20px;
  }
}

.login-tip-text {
  font-size: 14px;
  text-transform: capitalize;
}

.main-slogan {
  font-size: 72px;
  font-weight: 600;
  font-style: italic;
  line-height: 1;
  white-space: nowrap;

  &::before {
    white-space: nowrap !important;
  }

  @include typed(
    "Juchats infinite love",
    null,
    (
      iterations: 0,
      caret-width: 3px,
      caret-space: 30px,
    )
  );
}

.footer {
  left: 0;
  bottom: 0;
  padding: 40px;
  border-top: 1px solid rgba(white, 0.2);

  .studio {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      display: block;
      width: 8px;
      height: 8px;
      background-color: white;
      border-radius: 50%;
      right: 1px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .connecting-text {
    margin-right: 15px;
    opacity: 0.3;
  }

  .copy {
    margin-right: 30px;
  }

  .grey {
    opacity: 0.3;
    margin: 0 5px;
  }

  .f-right {
    img {
      height: 22px;
    }
  }
}

.login-page {
  font-family: Kanit;
  color: white;

  a {
    color: white;
  }
}

.header {
  left: 0;
  top: 0;
  padding: 30px 40px;

  .fns {
    line-height: 1;
  }

  .link {
    margin-right: 10px;
  }

  .light-switch {
    cursor: pointer;
    transition: 0.3s;

    &:hover {
      filter: invert(1);
    }
  }

  .link,
  .light-switch {
    background: rgba(black, 0.4);
    border-radius: 10px;
    padding: 15px;
  }

  .light-icon {
    width: 18px;
    height: 18px;
  }

  .auth-split {
    margin: 0 5px;
  }

  .auth-split,
  .auth-link {
    &:hover {
      font-weight: 600;
    }
  }

  .fn-split {
    margin: 0 15px;
  }

  .social-link {
    opacity: 0.8;
    transition: 0.3s;

    &:first-child {
      margin-right: 15px;
    }

    &:hover {
      opacity: 1;
    }
  }

  .social-text {
    font-size: 13px;
  }

  .icon-social {
    font-size: 14px;
    margin-right: 5px;
  }

  .icon-discord {
    font-size: 18px;
  }
}

.logo {
  font-size: 48px;
  font-style: italic;
  font-weight: bold;
  line-height: 1;

  span {
    color: var(--d3);
  }
}

@keyframes jumbo {
  from {
    background-position: 50% 50%, 50% 50%;
  }

  to {
    background-position: 350% 50%, 350% 50%;
  }
}

.dark {
  background: #000;
}

.dark .jumbo {
  background-image: var(--stripesDark), var(--rainbow);
  filter: blur(10px) opacity(50%) saturate(200%);
}

.dark .jumbo::after {
  background-image: var(--stripesDark), var(--rainbow);
}

.jumbo {
  --stripes: repeating-linear-gradient(
    100deg,
    #fff 0%,
    #fff 7%,
    transparent 10%,
    transparent 12%,
    #fff 16%
  );
  --stripesDark: repeating-linear-gradient(
    100deg,
    #000 0%,
    #000 7%,
    transparent 10%,
    transparent 12%,
    #000 16%
  );
  --rainbow: repeating-linear-gradient(
    100deg,
    #60a5fa 10%,
    #e879f9 15%,
    #60a5fa 20%,
    #5eead4 25%,
    #60a5fa 30%
  );

  background-image: var(--stripes), var(--rainbow);
  background-size: 300%, 200%;
  background-position: 50% 50%, 50% 50%;
  filter: blur(10px) invert(100%);
  mask-image: radial-gradient(ellipse at 100% 0%, black 40%, transparent 70%);
  pointer-events: none;
}

.jumbo::after {
  content: "";
  position: absolute;
  inset: 0;
  background-image: var(--stripes), var(--rainbow);
  background-size: 200%, 100%;
  animation: jumbo 60s linear infinite;
  background-attachment: fixed;
  mix-blend-mode: difference;
}
</style>
