<template>
  <div
    class="package-box fixed z-[1] h-full w-full overflow-auto bg-[#FAFAFA] pb-[100px]"
  >
    <!-- packages -->
    <div>
      <swiper-container class="h-full w-full">
        <swiper-slide v-for="(item, index) in selectedPackages" :key="index">
          <div class="flex justify-center pb-[160px] pt-[230px]">
            <div
              v-for="(data, index) in item"
              :key="index"
              class="group moimoi__group relative m-[20px]"
            >
              <img
                v-if="data.images && [1].includes(data.activityId)"
                :class="`moimoi__1 absolute pointer-events-none left-[0] right-[50px] m-auto w-[250px] ${
                  data.actived ? 'bottom-[-130px]' : 'bottom-[-70px]'
                } ${
                  data.current || (packageData.id && packageData.id !== data.id)
                    ? '!bottom-[-70px]'
                    : ''
                } group-hover:bottom-[310px] transition-all`"
                src="@/assets/images/Moimoi_1.png"
                alt=""
              />
              <div
                v-if="['Daily', 'Plus', 'Premium'].includes(data.title)"
                v-debounce="
                  () =>
                    packageData.id === data.id && packageData.orderNo
                      ? hrefStripe(packageData.locationHref)
                      : getPackage({
                        ...data,
                        index,
                        expireTime,
                      })
                "
                :class="`${data.disabled ? 'bg-[#E8E8E8]' : 'bg-[#F65134]'} ${
                  data.actived ? 'bottom-[-60px]' : 'bottom-[0px] '
                } h-[80px] leading-[60px] absolute w-[640px] transition-all rounded-b-[28px] cursor-pointer group-hover:bottom-[-60px] cursor-pointer text-[#ffffff] left-[20px] ${
                  data.disabled
                  || (packageData.id && packageData.id !== data.id)
                    ? '!bottom-[0px]'
                    : ''
                }`"
              >
                <div class="relative mt-[20px] text-center text-[18px]">
                  <!-- unpaidExpireTime -->
                  <div v-if="unpaidExpireTime" class="font-['Roboto_Slab']">
                    {{
                      packageData.id === data.id && packageData.orderNo
                        ? $t("packageBox.confirmPayment")
                        : `${$t(
                          "packageBox.waitPayment",
                        )} (${unpaidExpireTime})`
                    }}
                  </div>
                  <div v-else>
                    {{
                      data.current
                        ? $t("packageBox.renewalPurchase")
                        : packageData.id === data.id && packageData.orderNo
                          ? $t("packageBox.confirmPayment")
                          : data.images && [1].includes(data.activityId)
                            ? $t("packageBox.jointPurchase")
                            : $t("packageBox.buyNow")
                    }}
                  </div>
                  <div
                    class="absolute bottom-[5px] right-[20px] text-[48px] font-semibold font-['Kanit'] opacity-20"
                  >
                    {{ $t("packageBox.stripe") }}
                  </div>
                </div>
              </div>
              <div
                class="group relative w-[680px] overflow-hidden rounded-[28px] bg-[#ffffff] px-[30px] py-[30px] pb-[0] shadow-[0_0_100px_0_rgba(0,0,0,0.14)]"
              >
                <div
                  v-if="
                    data.images
                      && [1].includes(data.activityId)
                      && packageData.id !== data.id
                  "
                  class="absolute left-[0] top-[0] z-[1] h-full w-full bg-[#fff] opacity-0 transition-all group-hover:opacity-100"
                >
                  <img
                    v-if="selected === 1"
                    class="moimoi__2 absolute bottom-[-130px] left-[-100px] w-[270px]"
                    src="@/assets/images/Moimoi_2.svg"
                    alt=""
                  />
                  <img
                    v-if="selected === 1"
                    class="moimoi__3 absolute bottom-[-130px] left-[0] right-[0] m-auto w-[270px]"
                    src="@/assets/images/Moimoi_3.svg"
                    alt=""
                  />
                  <img
                    v-if="selected === 1"
                    class="moimoi__4 absolute bottom-[-130px] right-[-100px] w-[270px]"
                    src="@/assets/images/Moimoi_4.svg"
                    alt=""
                  />
                  <div class="moimoi__content text-center">
                    <div
                      class="relative pt-[50px] text-[70px] font-bold leading-[70px]"
                    >
                      <div>{{ data.actName }}</div>
                      <div
                        v-if="data.current"
                        class="absolute bottom-[20px] right-[100px] h-[30px] w-[80px] rounded-[5px] bg-[#FFDAC4] text-center text-[14px] text-[#EF752F] leading-[30px]"
                      >
                        {{ $t("packageBox.currentMember") }}
                      </div>
                    </div>
                    <div class="text-[36px] text-[#FF0000] font-['Damion']">
                      {{ data.title }}
                    </div>
                    <div class="text-[30px] font-semibold font-['Roboto_Slab']">
                      {{ data.price }}
                    </div>
                    <div class="font-semibold">{{ data.actDescription }}</div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div
                    v-if="
                      data.activityCount !== 0
                        && (data.actNewDiscountPercent || data.actOldDiscountPercent)
                    "
                    class="absolute right-[45px] top-[20px] text-[14px] text-[#F25033] font-semibold font-['Roboto_Slab']"
                  >
                    -{{
                      (
                        (1
                          - (userInfo.userType === 1
                            ? data.actNewDiscountPercent
                            : data.actOldDiscountPercent))
                        * 100
                      ).toFixed(0)
                    }}%
                  </div>
                  <div class="flex items-center">
                    <div>
                      <span class="text-[24px]">{{ data.description }}</span>
                      <span
                        :class="`text-[24px] font-['Damion'] pl-[5px] ${
                          ['Plus', 'Premium'].includes(data.title)
                            ? 'text-[#F65134]'
                            : ''
                        }`"
                      >{{ data.title }}</span>
                    </div>
                    <div
                      v-if="data.current"
                      class="ml-[20px] h-[30px] w-[80px] rounded-[5px] bg-[#FFDAC4] text-center text-[14px] text-[#EF752F]"
                    >
                      <span class="text-[14px] leading-[30px]">
                        {{ $t("packageBox.currentMember") }}</span>
                    </div>
                  </div>
                  <div
                    v-if="data.price"
                    class="text-[14px] font-['Roboto_Slab']"
                  >
                    <span class="text-[28px] font-semibold">{{
                      data.price
                    }}</span>
                    <span class="pl-[5px] align-super text-[18px]">$</span>
                  </div>
                </div>
                <div class="flex flex-wrap pt-[30px]">
                  <div
                    v-for="(item, index) in packageInfo"
                    :key="index"
                    :class="`${item.style} pb-[40px] ${
                      packageData.id === data.id
                      && packageData.orderNo
                      && !packageData.status
                        ? 'opacity-0'
                        : ''
                    }`"
                  >
                    <template v-if="item.label">
                      <div class="flex items-center">
                        <i :class="`${item.icon} w-[20px] block`"></i>
                        <span
                          class="text-[14px] font-medium font-['Roboto_Slab']"
                        >{{ item.label }}</span>
                      </div>
                      <div
                        v-for="(child, index) in item.children"
                        :key="index"
                        :class="`flex items-center text-[14px] pl-[20px] font-['Roboto_Slab'] pt-[10px] ${child.style}`"
                      >
                        <div class="whitespace-nowrap">
                          {{ child.label }}
                        </div>
                        <div
                          v-if="!child.text"
                          class="ml-[10px] min-w-[35px] rounded-[10px] bg-[#F0F0F0] text-center text-[12px] leading-[16px]"
                        >
                          {{ data[child.key] > 9999 ? "∞" : data[child.key] }}
                        </div>
                        <div
                          v-else
                          class="whitespace-nowrap pl-[5px] text-[#CBD5E1]"
                        >
                          {{ child.text }}
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div
                        v-for="(child, index) in item.children"
                        :key="index"
                        class="flex items-center pb-[10px]"
                      >
                        <div class="flex items-center">
                          <i :class="`${child.icon} w-[20px] block`"></i>
                          <span
                            class="text-[14px] font-medium font-['Roboto_Slab']"
                          >{{ child.label }}</span>
                        </div>
                        <div
                          class="ml-[10px] rounded-[10px] bg-[#F0F0F0] px-[10px] text-center text-[12px] leading-[16px]"
                        >
                          {{ data[child.key] > 9999 ? "∞" : data[child.key] }}
                        </div>
                      </div>
                    </template>
                  </div>
                  <div
                    v-if="
                      packageData.id === data.id
                        && packageData.orderNo
                        && !packageData.status
                    "
                    class="absolute left-[0] w-full"
                  >
                    <div
                      class="mx-[30px] border border-[#CFCFCF] rounded-[10px] border-dashed p-[50px] text-[14px] leading-[35px] font-['Roboto_Slab']"
                    >
                      <div>
                        {{ $t("packageBox.orderNumber") }}：#{{
                          packageData.orderNo
                        }}
                      </div>
                      <div
                        v-if="discount.deductionAmt"
                        class="flex items-center justify-between"
                      >
                        <span> {{ $t("packageBox.packageCredit") }}：</span>
                        <span
                          class="mx-[10px] flex-1 border-t border-[#B3B3B350] border-dashed"
                        ></span>
                        <span class="text-[#2E8400]">{{ discount.deductionAmt }}$</span>
                      </div>
                      <div
                        v-if="discount.commissionAmt"
                        class="flex items-center justify-between"
                      >
                        <span>{{ $t("packageBox.rewardCredit") }}：</span>
                        <span class="text-[#B3B3B3]">{{ discount.commissionAmt
                        }}{{ $t("packageBox.incentive") }}</span>
                        <span
                          class="mx-[10px] flex-1 border-t border-[#B3B3B350] border-dashed"
                        ></span>
                        <span class="text-[#FF8D00]">{{ discount.commissionAmt }}$</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span>{{ $t("packageBox.actualPayments") }}：</span>
                        <span
                          class="mx-[10px] flex-1 border-t border-[#B3B3B350] border-dashed"
                        ></span>
                        <span class="text-[#FF0000]">{{ packageData.payAmt }}$</span>
                      </div>
                      <div
                        v-if="
                          data.activityCount !== 0
                            && packageData.actNewDiscountPercent
                            && userInfo.userType === 1
                        "
                        class="flex items-center justify-between"
                      >
                        <span>{{ $t("packageBox.newUserDiscount") }}：</span>
                        <span
                          class="mx-[10px] flex-1 border-t border-[#B3B3B350] border-dashed"
                        ></span>
                        <span class="text-[#FF0000]">{{
                          (packageData.actNewDiscountPercent * 100).toFixed(
                            0,
                          )
                        }}%</span>
                      </div>
                      <div
                        v-if="
                          data.activityCount !== 0
                            && packageData.actOldDiscountPercent
                            && userInfo.userType === 2
                        "
                        class="flex items-center justify-between"
                      >
                        <span>{{ $t("packageBox.discountRegularUsers") }}：</span>
                        <span
                          class="mx-[10px] flex-1 border-t border-[#B3B3B350] border-dashed"
                        ></span>
                        <span class="text-[#FF0000]">{{
                          (packageData.actOldDiscountPercent * 100).toFixed(
                            0,
                          )
                        }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper-container>
      <div
        class="absolute left-[0] right-[0] top-[430px] m-auto max-w-[1510px] w-full"
      >
        <div
          v-if="selected === 1"
          class="absolute left-[0px] z-[1] h-[60px] w-[25px] cursor-pointer rounded-[6px] bg-[#ffffff] text-center text-[#bbb9b9] leading-[60px] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)] transition hover:text-[#000000] hover:shadow-[0_0_1.25rem_0_rgba(0,0,0,0.3)]"
          @click="swiperNavigation('prev')"
        >
          <i class="i-ri-arrow-left-s-line"></i>
        </div>
        <div
          v-if="selected === 0"
          class="absolute right-[0px] z-[1] h-[60px] w-[25px] cursor-pointer rounded-[6px] bg-[#ffffff] text-center text-[#bbb9b9] leading-[60px] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)] transition hover:text-[#000000] hover:shadow-[0_0_1.25rem_0_rgba(0,0,0,0.3)]"
          @click="swiperNavigation('next')"
        >
          <i class="i-ri-arrow-right-s-line"></i>
        </div>
      </div>
    </div>
    <!-- title & gift input -->
    <div class="absolute left-[0] right-[0] top-[0] z-[1] m-auto">
      <!-- title -->
      <div class="my-[40px] flex items-center justify-center">
        <div class="text-[40px] font-semibold font-['Kanit'] italic">
          <span class="text-[#f65134]">Ju</span>
          <span class="text-[#000000]">chats</span>
        </div>
        <div class="mx-[15px] mt-[5px] text-[20px] text-[#d8d8d8] font-thin">
          |
        </div>
        <div class="mt-[5px] text-[24px] font-normal">
          {{ $t("packageBox.packageSubscription") }}
        </div>
        <div
          class="relative ml-[30px] mt-[5px] flex rounded-[5px] bg-[#ffffff] p-[5px] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)]"
        >
          <div
            :class="`rounded-[5px] w-[70px] h-[30px] text-[14px] text-center bg-[#F65134] text-[#ffffff] absolute transition ${selectedData[selected].translate}`"
          ></div>
          <div
            v-for="({ label }, index) in selectedData"
            :key="index"
            :class="`rounded-[5px] w-[70px] h-[30px] text-[14px] leading-[30px] text-center cursor-pointer relative transition ${
              selected === index ? selectedData[selected].style : ''
            }`"
            @click="changeSelected({ index })"
          >
            {{ label }}
          </div>
        </div>
      </div>
      <!-- close icon -->
      <i
        class="i-ri-arrow-left-line absolute right-[2.8rem] top-[2.2rem] z-[1] h-[2.6rem] w-[2.6rem] flex cursor-pointer items-center justify-center border border-[#CBD5E1] rounded-full text-[1.5rem] text-[#A8AFBF]"
        @click="close"
      ></i>
      <!-- gift input -->
      <div
        v-if="giftButtonVisible"
        class="m-auto h-[3.5rem] w-[22rem] flex items-center rounded-[1.25rem] bg-[#FFFFFF] p-[0.5rem] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)] 2xl:h-[3rem] 2xl:w-[20rem]"
      >
        <AvatarFrame
          :src="userInfo.thumbnailUrl"
          name="w-[3rem] h-[3rem] mr-[0.5rem] 2xl:w-[2.5rem] 2xl:h-[2.5rem]"
        ></AvatarFrame>
        <input
          v-model="code"
          class="mr-[0.5rem] h-[2.2rem] w-[15rem] rounded-[0.63rem] bg-[#F4F4F4] p-[0.5rem] text-[0.8rem] outline-none"
          :placeholder="$t('packageBox.eggCodeTip')"
          type="text"
        />
        <div
          class="h-[2.2rem] w-[4.5rem] flex cursor-pointer items-center justify-center rounded-[0.63rem] bg-[#F65134] text-[0.8rem] text-[#FFFFFF] shadow-[0_0_3rem_0_rgba(246,81,52,0.46)]"
          @click="getGift"
        >
          {{ $t("packageBox.exchange") }}
        </div>
      </div>
    </div>
    <!-- gift exchange -->
    <div
      v-if="giftVisible"
      class="fixed top-[80px] z-[1] h-full w-full flex items-center justify-center bg-[#FAFAFA]"
    >
      <div
        ref="lottieElement"
        class="absolute top-[-20rem] z-[1] h-[70rem] w-[70rem]"
      ></div>
      <i
        class="i-ri-close-line absolute right-[2.8rem] top-[2.2rem] z-[1] h-[2.6rem] w-[2.6rem] flex cursor-pointer items-center justify-center border border-[#CBD5E1] rounded-full text-[1.5rem] text-[#A8AFBF]"
        @click="giftVisible = false"
      ></i>
      <div>
        <div class="relative mt-[-5rem] flex items-center justify-center">
          <div
            class="flex items-center rounded-[1.25rem] p-[0.5rem] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)]"
          >
            <AvatarFrame
              :src="userInfo.thumbnailUrl"
              name="w-[3rem] h-[3rem]"
            ></AvatarFrame>
            <div
              class="ml-[0.5rem] mr-[0.3rem] font-['Roboto_Slab'] font-[800]"
            >
              {{ formatMobile(userInfo.name) }}
            </div>
          </div>
          <div class="mx-[1.2rem] text-[1.4rem]">×</div>
          <div class="flex text-[3.5rem] text-[#FF0000] font-['Damion']">
            <div>{{ giftData.title?.replace("年", "") }}</div>
            <PremiumPro v-if="['Premium年'].includes(giftData.title)" />
          </div>
        </div>
        <div class="pb-[1.5rem] pt-[0.5rem] text-center text-[1rem]">
          {{ $t("packageBox.congratulate", { title: giftData.title }) }}
        </div>
        <div class="h-[15rem] bg-[#ffffff] p-[2rem] pt-[3rem]">
          <div class="w-[30rem]">
            <div class="gift-view__print">
              <div class="gift-view__outlet">
                <div class="gift-view__ticket">
                  <div class="gift-view__ticket__content">
                    <div class="pt-6 text-center text-xs text-[#EDEDED]">
                      ////////////////////////////////////////////////////////////////////////////////////////////////////
                    </div>
                    <div class="gift-view__ticket__container">
                      <div class="flex pt-10 text-sm">
                        <div>{{ $t("packageBox.subscriptionPackages") }}：</div>
                        <div
                          class="flex text-[1.2rem] text-[#FF0000] font-['Damion']"
                        >
                          <div>{{ giftData.title?.replace("年", "") }}</div>
                          <div
                            v-if="['Premium年'].includes(giftData.title)"
                            class="ml-[10px] h-[22px] rounded-[50px] from-orange-400 to-red-500 bg-gradient-to-r px-[8px] text-[#ffffff] font-semibold leading-[22px] font-['Arial'] not-italic shadow-[0_0_30px_0_rgba(255,111,0,0.5)] !text-[12px]"
                          >
                            PRO
                          </div>
                        </div>
                      </div>
                      <div class="flex pt-2 text-sm">
                        <div>{{ $t("packageBox.subscriptionTerm") }}：</div>
                        <div class="text-[#B3B3B3]">
                          {{ formatUnitPlus({ amount: giftData.expireDays }) }}
                        </div>
                      </div>
                      <div class="flex pb-10 pt-2 text-sm">
                        <div>{{ $t("packageBox.cutOffDate") }}：</div>
                        <div class="text-[#B3B3B3]">
                          {{ moment(giftData.expireTime).format("YYYY.MM.DD") }}
                        </div>
                      </div>
                      <div class="gift-view__ticket__point">
                        {{
                          moment(giftData.expireTime).format("YYYY / MM / DD")
                        }}
                      </div>
                      <div class="gift-view__ticket__point">
                        {{ $t("billDetail.serialNumber") }} {{ code }}
                      </div>
                      <div class="pb-5 pt-2">
                        <svg class="gift-view__ticket__barcode"></svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- pay success -->
    <div
      v-if="successVisible"
      class="fixed top-[0] z-[1] h-full w-full flex items-center justify-center bg-[#FAFAFA]"
    >
      <i
        class="i-ri-close-line absolute right-[2.8rem] top-[120px] z-[1] h-[2.6rem] w-[2.6rem] flex cursor-pointer items-center justify-center border border-[#CBD5E1] rounded-full text-[1.5rem] text-[#A8AFBF]"
        @click="successVisible = false"
      ></i>
      <div
        ref="lottieElement"
        class="absolute top-[-20rem] h-[70rem] w-[70rem]"
      ></div>
      <div>
        <div class="relative mt-[-5rem] flex items-center justify-center">
          <div
            class="flex items-center rounded-[1.25rem] p-[0.5rem] shadow-[0_0_1.25rem_0_rgba(0,0,0,0.12)]"
          >
            <AvatarFrame
              :src="userInfo.thumbnailUrl"
              name="w-[3rem] h-[3rem]"
            ></AvatarFrame>
            <div
              class="ml-[0.5rem] mr-[0.3rem] font-['Roboto_Slab'] font-[800]"
            >
              {{ formatMobile(userInfo.name) }}
            </div>
          </div>
          <div class="mx-[1.2rem] text-[1.4rem]">×</div>
          <div class="text-[3.5rem] text-[#FF0000] font-['Damion']">
            {{ packageData.title }}
          </div>
        </div>
        <div class="pt-[0.5rem] text-[1rem]">
          {{ $t("packageBox.congratulate", { title: packageData.title }) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import avatar from '@/assets/images/avatar_default.png'
import Daily from '@/assets/images/Daily.png'
import Guest from '@/assets/images/Guest.png'
import Plus from '@/assets/images/Plus.png'
import Premium from '@/assets/images/Premium.png'
import {
  filterTime,
  formatMobile,
  formatUnitPlus,
  lottieAnimation,
} from '@/common'
import services from '@/common/axios'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useUserStore } from '@/stores/user'
import JsBarcode from 'jsbarcode'
import moment from 'moment'
import { storeToRefs } from 'pinia'
import { register } from 'swiper/element/bundle'
import 'swiper/css'

defineProps({
  giftButtonVisible: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['close'])
const t = useGlobalI18n()
const selected: any = ref(0)

const selectedData: any = ref([
  {
    label: t('packageBox.dailyPackage'),
    style: 'text-[#ffffff]',
    translate: 'translate-x-[0px]',
  },
  {
    label: t('packageBox.monthlyPackage'),
    style: 'text-[#ffffff]',
    translate: 'translate-x-[70px]',
  },
])
const discount: any = ref({})
const packages: any = ref([])
const packageData: any = ref({})
const packagesData: any = ref([])
const expireTime = ref('')
const unpaidInterval = ref()
const unpaidExpireTime = ref('')
const countdownInterval = ref()
const orderInterval = ref()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const successVisible = ref(false)
const code = ref('')
const giftData: any = ref({})
const giftVisible = ref(false)
const lottieElement = ref()
const lottieSVG = ref()
const images = ref([Guest, Daily, Plus, Premium])
const selectedPackages = computed(() => {
  return [
    packagesData.value.filter((element: any) =>
      ['Guest', 'Daily'].includes(element.title),
    ),
    packagesData.value.filter((element: any) =>
      ['Plus', 'Premium'].includes(element.title),
    ),
  ]
})
const packageInfo: any = ref([
  {
    label: 'OpenAI',
    icon: 'i-ri-openai-line',
    style: 'w-[150px]',
    children: [
      {
        label: 'GPT Forte',
        key: 'gptFortePreview',
        style: 'justify-between',
      },
      {
        label: 'GPT Piano',
        key: 'gptPianoPreview',
        style: 'justify-between',
      },
      {
        label: 'GPT Mezzo',
        key: 'gptMezzoPreview',
        style: 'justify-between',
      },
    ],
  },
  {
    label: 'Anthropic',
    icon: 'iconfont icon-Claude !text-[12px]',
    style: 'w-[170px]',
    children: [
      {
        label: 'Claude Mezzo',
        key: 'claudeMezzoPreview',
        style: 'justify-between',
      },
      {
        label: 'Claude3 Opus',
        key: 'claudeOpus',
        style: 'justify-between',
      },
    ],
  },
  {
    label: 'Mistral',
    icon: 'iconfont icon-Mixtral !text-[12px]',
    style: 'w-[160px]',
    children: [
      {
        label: 'Mixtral Forte',
        key: 'mixtralFortePreview',
      },
    ],
  },
  {
    label: 'Google',
    icon: 'iconfont icon-Gemini !text-[12px]',
    style: 'w-[120px]',
    children: [
      {
        label: 'Gemini1.5 Pro',
        visible: ['Guest', 'Daily', 'Plus'],
      },
    ],
  },
  {
    style: 'w-[150px]',
    children: [
      {
        label: 'Browsing',
        icon: 'i-ri-planet-line',
        key: 'browsing',
      },
      {
        label: 'Voice',
        icon: 'i-ri-voiceprint-line',
        key: 'voice',
      },
    ],
  },
  {
    label: 'Archive',
    icon: 'i-ri-archive-2-line',
    style: 'w-[170px]',
    children: [
      {
        label: t('packageBox.histories'),
        key: 'dialogLimit',
      },
      {
        label: t('packageBox.starburst'),
        key: 'startGroupDialogLimit',
      },
      {
        label: t('packageBox.group'),
        key: 'groupLimit',
      },
    ],
  },
  {
    label: 'Labs',
    icon: 'iconfont icon-Labs !text-[12px]',
    style: 'w-[190px]',
    children: [
      {
        label: 'DALL · E3',
        key: 'e3',
      },
      {
        label: 'Meta Llama3 70B',
        key: 'metaLlama',
      },
      {
        label: 'Midjourney',
        text: 'Coming soon✨',
      },
    ],
  },
])
const getInfo = async () => {
  await userStore.getUserInfoV2()
}
const close = () => {
  if (orderInterval.value) {
    clearInterval(orderInterval.value)
  }
  emits('close')
}
const getStatus = async (value: any) => {
  if (orderInterval.value) {
    clearInterval(orderInterval.value)
  }
  orderInterval.value = setInterval(async () => {
    try {
      const { status }: any = await services.post(
        `${import.meta.env.VITE_APP_PAY_URL}/order/detail`,
        {
          orderNo: value,
        },
        {
          headers: {
            noninductive: true,
          },
        },
      )
      if ([1].includes(status)) {
        getInfo()
        packageData.value = { title: packageData.value.title }

        getPackages()
        clearInterval(orderInterval.value)
        if (unpaidInterval.value) {
          clearInterval(unpaidInterval.value)
          unpaidExpireTime.value = ''
        }
        successVisible.value = true
        setTimeout(() => {
          lottieSVG.value = lottieAnimation(lottieElement.value)
        }, 0)
      }
    }
    catch (error) {
      clearInterval(orderInterval.value)
    }
  }, 2000)
}
const countdown = () => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
    expireTime.value = ''
  }
  const date = packageData.value.expireTime
  const expire = moment(date)
  countdownInterval.value = setInterval(() => {
    const minutes = expire.diff(new Date(), 'minutes') % 60
    const seconds = expire.diff(new Date(), 'seconds') % 60
    if (minutes < 0 || seconds < 0) {
      clearInterval(countdownInterval.value)
    }
    expireTime.value = `${filterTime(minutes)}:${filterTime(seconds)}`
  }, 100)
}
const payMethod = async ({ param, index }: any) => {
  let payAmt: any,
    orderNo: any,
    locationHref: any,
    expireTime: any,
    commissionAmt: any,
    deductionAmt: any
  if (['stripe'].includes(param)) {
    try {
      const response: any = await services.post(
        `${import.meta.env.VITE_APP_PAY_URL}/order/stripe/pay`,
        {
          packageId: packageData.value.id,
        },
      )
      payAmt = response.payAmt
      orderNo = response.orderNo
      locationHref = response.url
      expireTime = response.expireTime
      commissionAmt = response.commissionAmt
      deductionAmt = response.deductionAmt
    }
    catch (error) {
      packageData.value = {}
      throw error
    }
  }
  else if (['repayment'].includes(param)) {
    locationHref = packageData.value.paymentIdentificationText
    payAmt = packageData.value.payAmt
    orderNo = packageData.value.orderNo
    expireTime = packageData.value.expireTime
    commissionAmt = packageData.value.commissionAmt
    deductionAmt = packageData.value.deductionAmt
  }
  packagesData.value = packagesData.value.map((element: any) => ({
    ...element,
    selected: index,
  }))
  packageData.value = {
    ...packageData.value,
    payAmt,
    orderNo,
    locationHref,
    expireTime,
  }
  discount.value = {
    commissionAmt,
    deductionAmt,
  }
  countdown()
  getStatus(orderNo)
}
const getPackage = (params: any) => {
  // 无法订购套餐 和 当前套餐
  if (params.disabled) {
    return
  }
  // 禁止购买指定套餐
  if (['Guest'].includes(params.title)) {
    return
  }
  packageData.value = { ...params }
  if (params.actived) {
    payMethod({ param: 'repayment', index: params.index })
  }
  else {
    payMethod({ param: 'stripe', index: params.index })
  }
}

const hrefStripe = (locationHref: string) => {
  if (locationHref) {
    window.open(locationHref)
  }
}

const unpaiddown = (param: any) => {
  if (unpaidInterval.value) {
    clearInterval(unpaidInterval.value)
    unpaidExpireTime.value = ''
  }
  const expire = moment(param)
  unpaidInterval.value = setInterval(() => {
    const minutes = expire.diff(new Date(), 'minutes') % 60
    const seconds = expire.diff(new Date(), 'seconds') % 60
    if (minutes < 0 || seconds < 0) {
      getPackages()
      clearInterval(unpaidInterval.value)
      unpaidExpireTime.value = ''
      return
    }
    unpaidExpireTime.value = `${filterTime(minutes)}:${filterTime(seconds)}`
  }, 100)
}
const getPackages = async () => {
  const orderData: any = await services.post(
    `${import.meta.env.VITE_APP_PAY_URL}/order/notCompletedRecord`,
  )
  const packagesArray: any = await services.get(
    `${import.meta.env.VITE_APP_PAY_URL}/order/packages`,
    {},
  )
  packages.value = packagesArray.map((element: any) => {
    const unpaid = !!(
      orderData
      && orderData.expireTime
      && moment(orderData.expireTime).isAfter(moment(new Date()))
    )
    if (unpaid && element.id === orderData.packageId) {
      unpaiddown(orderData.expireTime)
      return {
        ...element,
        ...orderData,
        id: orderData.packageId || element.id,
        payAmt: orderData.price,
        current: userInfo.value.discountPackage === element.title,
        disabled: false,
        actived: true,
      }
    }
    else {
      return {
        ...element,
        disabled: unpaid,
        actived: false,
        current: userInfo.value.discountPackage === element.title,
      }
    }
  })
  packagesData.value = packages.value.map((element: any, index: number) => {
    return {
      ...element,
      selected: null,
      title: index ? element.title : 'Guest',
      description: index ? element.description : t('packageBox.freeMember'),
      browsing: index ? '∞' : 5,
      image: images.value[index],
    }
  })
}

const changeSelected = ({ index }: any) => {
  selected.value = index
  const swiperEl: any = document.querySelector('swiper-container')
  swiperEl.swiper.slideTo(index)
}
const swiperNavigation = (type: any) => {
  const swiperEl: any = document.querySelector('swiper-container')
  if (type === 'prev') {
    swiperEl.swiper.slidePrev(300)
  }
  else {
    swiperEl.swiper.slideNext(300)
  }
}
const getGift = async () => {
  giftData.value = await services.post(
    `${import.meta.env.VITE_APP_PAY_URL}/order/exchange`,
    {
      code: code.value,
      headers: {
        noninductive: true,
      },
    },
  )
  giftVisible.value = true
  setTimeout(() => {
    lottieSVG.value = lottieAnimation(lottieElement.value)
    JsBarcode('.gift-view__ticket__barcode', String(giftData.value.ticketNo), {
      format: 'CODE39',
      width: 1.8,
      height: 18,
      displayValue: false,
      textMargin: 0,
      fontSize: 15,
      margin: 0, // 设置条形码周围的空白边距
    })
    getInfo()
    getPackages()
  }, 1500)
}
onMounted(async () => {
  await getPackages()
  register()
  const swiperEl: any = document.querySelector('swiper-container')
  swiperEl.addEventListener('swiperslidechange', ({ detail }: any) => {
    const [swiper] = detail
    selected.value = swiper.activeIndex
  })
})
</script>

<style lang="scss" scoped>
@import "animate.css";
@import "@/assets/styles/package-box";

@keyframes shake2 {
  0% {
    transform: translateX(0);
  }

  15% {
    transform: translateX(-10px);
  }

  30% {
    transform: translateX(10px);
  }

  45% {
    transform: translateX(-10px);
  }

  65% {
    transform: translateX(10px);
  }

  75% {
    transform: translateX(-10px);
  }

  90% {
    transform: translateX(10px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes down2 {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}

.gift-view {
  @apply size-full fixed z-100 top-0;

  background: url("//hermosssvip.herm.tools/images/background.jpg") no-repeat;

  .gift-view__lottie {
    @apply w-560px h-800px absolute -top-300px inset-x-0 m-auto z-10 pointer-events-none;
  }

  .gift-view__position {
    @apply absolute top-200px inset-x-0 m-auto;
  }

  .gift-view__container {
    @apply relative w-560px m-auto;
  }

  .gift-view__card {
    @apply relative z-1 size-full drop-shadow-[0_0_20px_#00000010] pb-0.01px bg-repeat-x;

    background-image: radial-gradient(transparent 50%, #fff 0);
    background-size: 40px 40px;
    background-position: left bottom calc(-40px / 2);
  }

  .gift-view__egg {
    @apply w-140px absolute inset-x-0 m-auto -top-90px;

    .gift-view__egg--shake {
      animation: shake2 1.5s;
    }
  }

  .gift-view__logo {
    @apply text-48px font-700 absolute -top-35px text-center w-full;
  }

  .gift-view__title {
    @apply w-560px h-123px bg-#fcfbfc;
  }

  .gift-view__name {
    @apply text-24px font-800 pl-10px;

    font-family: Alegreya;
  }

  .gift-view__flag {
    @apply font-400 text-16px pl-6px lh-5px pb-8px;
  }

  .gift-view__content {
    @apply h-335px p-50px pt-0 mb-20px bg-#fff;
  }

  .gift-view__input {
    @apply bg-#fff text-14px relative;

    input {
      @apply border-1px border-solid border-[#e1e5ea] pl-20px mt-50px w-440px h-60px;
    }
  }

  .gift-view__button {
    @apply transition-cusbezier-300 right-40px -bottom-24px absolute w-140px h-48px bg-#f65134 shadow-[0_10px_30px_0_#F6513433] rounded-6px font-400 text-14px text-#fff cursor-pointer;

    &:hover {
      @apply shadow-[0_10px_30px_0_#ff88008d];
    }

    & > div {
      & > div {
        &:first-child {
          @apply pr-5px;
        }
      }
    }

    .i-ri-arrow-right-circle-fill {
      @apply text-20px pl-5px;
    }
  }

  .gift-view__describe {
    & > div {
      @apply leading-30px;
    }
  }

  .gift-view__label {
    @apply text-center text-32px leading-60px;

    font-family: EXEPixelPerfect;
  }

  .gift-view__outlet {
    @apply w-440px h-25px m-auto;

    background-image: linear-gradient(180deg, #00000014 0%, #00000005 100%);
  }

  .gift-view__print {
    @apply overflow-hidden absolute left-0 right-0 h-10000px;
  }

  .gift-view__ticket {
    @apply absolute w-420px drop-shadow-[0_30px_20px_#00000010] inset-x-0 m-auto pb-20px;

    background-image: radial-gradient(transparent 50%, #fff 0);
    background-size: 40px 40px;
    background-position: left bottom -20px;
    background-repeat: repeat-x;
    animation: down2 2s linear forwards;

    .gift-view__ticket__content {
      @apply relative overflow-hidden;

      background: linear-gradient(to bottom, #ebeaea, #fff) no-repeat top / 100%
        25px,
        linear-gradient(to bottom, #fff, #fff) no-repeat 100% 25px / 100% 115px,
        radial-gradient(
          circle at 45px 45px,
          transparent 15px 15px,
          #fff 15px 15px
        )
        repeat-x -45px 115px / 100% 70px,
        linear-gradient(to bottom, #fff, #fff) no-repeat 100% 175px / 100% 100%;
    }

    .gift-view__ticket__code {
      @apply text-120px leading-110px text-[#f65134] text-center;

      font-family: EXEPixelPerfect;
    }

    .gift-view__ticket__container {
      @apply px-25px py-0 font-RobotoMono;
    }

    .gift-view__ticket__point {
      @apply text-16px;

      font-family: "01 Digit";
    }

    .gift-view__ticket__barcode {
      @apply h-18px;
    }
  }

  .gift-view__links {
    @apply absolute top-0 -left-60px border-2px border-solid border-[#000] rounded-50% text-#000 size-35px text-25px flex-c cursor-pointer m-auto;
  }
}
</style>
