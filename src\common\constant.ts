import i18n from '@/i18n'
/** 邮箱正则 */
export const emailRegular = /^[\w.%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i

/** 更新日志 */
export const newFeatures = [
  i18n.global.t('headerBar.brandReconstruction'),
  i18n.global.t('headerBar.modelSupportGPT'),
  i18n.global.t('headerBar.modelSupportClaude'),
  i18n.global.t('headerBar.modelSupportMixtral'),
  i18n.global.t('headerBar.modelSupportDALL'),
  i18n.global.t('headerBar.modelSupportLlama'),
  i18n.global.t('headerBar.optimizeLeTax'),
  i18n.global.t('headerBar.optimizeMarkdown'),
  i18n.global.t('headerBar.optimizeConnection'),
  i18n.global.t('headerBar.fixBugs'),
  i18n.global.t('headerBar.subscriptionOpen'),
  i18n.global.t('headerBar.subscriptionUnlimited'),
  i18n.global.t('headerBar.subscriptionHistory'),
]

// 只有客户端使用该 host
export const CLIENT_HOST = ['hermchats.herm.link']

export const ATTACHMENTS_NUMBER_LIMIT = 10

// export const DEFAULT_PROMPTS = ['看看图 / 照片中有什么？', '总结文件', '分析文件中的数据']

// 上传图片时的默认 prompt
// export const SCAN_DEFAULT_PROMPT = DEFAULT_PROMPTS[0]

// 上传文本文件时默认 prompt
// export const TEXT_FILE_DEFAULT_PROMPT = DEFAULT_PROMPTS[1]

// 上传数据文件时默认 prompt
// export const DATA_FILE_DEFAULT_PROMPT = DEFAULT_PROMPTS[2]

export const accepts = [
  '.sh',
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm
  'application/vnd.ms-excel.sheet.binary.macroEnabled.12', // .xlsb
  'application/vnd.ms-excel', // .xlt
  'application/vnd.openxmlformats-officedocument.spreadsheetml.template', // .xltx
  'application/vnd.ms-excel.template.macroEnabled.12', // .xltm
  'text/csv', // .csv
  '.csv',
  'text/plain', // .log
  '.log',
  'application/xml', // .xml
  'text/xml', // .xml
  '.xml',
  'application/msword', // .doc
  '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  '.docx',
  'application/pdf', // .pdf
  '.pdf',
  'application/vnd.ms-powerpoint', // .ppt
  '.ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  '.pptx',
  'text/plain', // .txt
  '.txt',
  'text/plain;charset=utf-8',
  'application/rtf', // .rtf
  '.rtf',
  'text/html', // .html
  '.html',
  'text/markdown', // .md
  '.md',
  'application/json', // .json
  '.json',
  'image/jpeg', // .jpg
  '.jpg',
  '.jpeg',
  'image/png', // .png
  '.png',
  'image/gif', // .gif
  '.gif',
  'image/bmp', // .bmp
  '.bmp',
  'image/webp', // .webp
  '.webp',
  'image/tiff', // .tif
  '.tif',
  '.tiff',
  'image/heif', // .heif
  '.heif',
  'image/heic', // .heic
  '.heic',
  'audio/mpeg', // .mp3
  '.mp3',
  '.ts',
  '.tsx',
  '.js',
  '.mjs',
  '.py',
  '.htm',
  '.yml',
  '.yaml',
]

// 纯图片
export const image_accept = [
  'image/jpeg', // .jpg
  '.jpg',
  '.jpeg',
  'image/png', // .png
  '.png',
  'image/gif', // .gif
  '.gif',
  'image/bmp', // .bmp
  '.bmp',
  'image/webp', // .webp
  '.webp',
  'image/heif', // .heif
  '.heif',
  'image/heic', // .heic
  '.heic',
  'image/tiff', // .tif
  '.tif',
  '.tiff',
]

// 纯文件
export const file_accept = [
  'application/vnd.ms-excel', // .xls
  '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  '.xlsx',
  'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm
  '.xlsm',
  'application/vnd.ms-excel.sheet.binary.macroEnabled.12', // .xlsb
  '.xlsb',
  'application/vnd.ms-excel', // .xlt
  '.xlt',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.template', // .xltx
  '.xltx',
  'application/vnd.ms-excel.template.macroEnabled.12', // .xltm
  '.xltm',
  'text/csv', // .csv
  '.csv',
  'text/plain', // .log
  '.log',
  'application/xml', // .xml
  'text/xml', // .xml
  '.xml',
  'application/msword', // .doc
  '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  '.docx',
  'application/pdf', // .pdf
  '.pdf',
  'application/vnd.ms-powerpoint', // .ppt
  '.ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  '.pptx',
  'text/plain', // .txt
  '.txt',
  'text/plain;charset=utf-8',
  'application/rtf', // .rtf
  '.rtf',
  'text/html', // .html
  '.html',
  'application/json', // .json
  '.json',
  '.ts',
  '.tsx',
  '.js',
  '.mjs',
  '.py',
  '.htm',
  '.yml',
  '.yaml',
]

/**
 * 产品默认的模型 ID
 */
export const DefaultModelId = 36

export const CelHiveLinkSplitSymbol = '---celhive-link---'
