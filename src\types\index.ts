export enum SectionType {
  Artifact = 1
}

export interface Section {
  id?: string | number
  unionid?: string
  receiving: boolean
  content?: string
  question?: string
  children?: Section[]
  page?: number
  img?: string
  imgText?: string
  attachments?: Array<{
    fileId: string
    fileName: string
  }>
  thumbnailUrls?: string[]
  artifacts?: string
  toolsType?: SectionType
  nickName: string
}

declare global {
  interface Window {
    downloadImage: (encodedUrl: string) => Promise<void>
    previewImage: (src: string, text: string) => void
    updateCodeWrapperMaxWidth: () => void
    __TAURI__?: {
      shell: {
        open: (url: string) => Promise<void>
      }
    }
  }
}
