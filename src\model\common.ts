import type { LOGIN_TYPE } from '@/enum'
import type { HttpStatusCode } from 'axios'

export enum BOOL {
  YES = 1,
  NO = 0,
}

export interface OSSImageModel {
  name?: string
  originUrl?: string
  thumbnailUrl?: string
  timeSeconds?: number
  uuid: string
}

export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARN = 'warn',
}
export interface NotificationItem {
  id: string
  type: NotificationType
  content: string
  timer: ReturnType<typeof setTimeout> | number
}

export interface UserInfo {
  id?: number
  originUrl?: string
  thumbnailUrl?: string
  level: number
  name?: string
  phone?: string
  email?: string
  plusExpireTime?: string
  discountPackage?: string
  expirePackageName?: string
  packageId?: number
  inviteCode?: string
  inviteCount?: number
  commission?: number
  title?: string
  modelUsed?: {
    [key: string]: Array<{
      name: string
      limit: number
      used: number
    }>
  }
  usedVo?: {
    e3: number
    voice: number
    gptFortePreview: number
    gptPianoPreview: number
    gptMezzoPreview: number
    claudeMezzoPreview: number
    claudeOpus: number
    mixtralFortePreview: number
    metaLlama: number
    gemini: number
    deepseek: number
  }
  levelLimitVo?: {
    dialogUsed: number
    starGroupDialogUsed: number
    groupUsed: number
    dialogLimit: number
    starGroupDialogLimit: number
    groupDialogLimit: number
    groupLimit: number
    period: number
    gptFortePreview: number
    gptPianoPreview: number
    gptMezzoPreview: number
    claudeMezzoPreview: number
    claudeOpus: number
    mixtralFortePreview: number
    metaLlama: number
    deepseek: number
    gemini: number
    voice: number
    e3: number
  }
  googleEmail?: string
  googleUserId?: string
  googleUserName?: string
  githubUserId?: string
  githubUserName?: string
  upUserName?: string
  upUserThumbnailUrl?: string
  userType?: number
  loginType?: LOGIN_TYPE
  style?: number
}

export enum FILE_CATEGORY {
  TEXT = 'TEXT',
  DATA = 'DATA',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
}

export enum FILE_TYPE {
  IMAGE = 'IMAGE',
  PDF = 'PDF',
  WORD = 'WORD',
  PPT = 'PPT',
  EXCEL = 'EXCEL',
  OTHER = 'OTHER',
}

// response type from V1 API
export interface ResponseRes<T> {
  code: HttpStatusCode
  success: boolean
  data: T
}
