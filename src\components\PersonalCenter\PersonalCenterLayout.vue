<script lang="ts" setup>
import { useAppStore } from "@/stores/app";
import Info from "./Info.vue";
import Invitation from "./Invitation.vue";
import Subscription from "./Subscription.vue";
import { useGlobalI18n } from "@/hooks/useGlobalI18n";
defineOptions({
  name: "PersonalCenterLayout",
});

const t = useGlobalI18n();
const props = defineProps({
  active: {
    type: String as PropType<"info" | "invite" | "subscribe">,
    default: "info",
  },
});

const { isPc } = storeToRefs(useAppStore());
const activeMenu = ref("info");
const anchor = ref("10px");
const handleAnchor = (el: HTMLElement) => {
  if (!el) return
  const fatherEl = el.parentNode as HTMLElement;
  const { top } = el.getBoundingClientRect();
  const { top: fTop } = fatherEl.getBoundingClientRect();
  // 增加padding-top的高度
  anchor.value = `${top - fTop + 10}px`;
};
const handleSelectMenu = (el: HTMLElement, val: string) => {
  handleAnchor(el);
  activeMenu.value = val;
};
const getCurrentComp = computed(() => {
  return layout.filter((element: any) => element.value === activeMenu.value)[0]
    .component;
});
const layout = [
  {
    value: "info",
    name: t("userInfo.myProfile"),
    component: Info,
  },
  {
    value: "invite",
    name: t("userInfo.myInvite"),
    component: Invitation,
  },
  {
    value: "subscribe",
    name: t("personalCenterLayout.mySubscription"),
    component: Subscription,
  },
];
watch(
  () => props.active,
  (val) => {
    activeMenu.value = val;
    nextTick(() => {
      const el = document.querySelector("[data-active=true]") as HTMLElement;
      handleAnchor(el);
    });
  },
  { immediate: true }
);
</script>

<template>
  <div v-if="isPc" class="flex-y-c flex-col">
    <div class="mb-33 flex items-center">
      <PageTitle> {{ $t("personalCenterLayout.myCenter") }} </PageTitle>
    </div>
    <div class="w-full flex">
      <div class="mx-auto w-200 flex">
        <ul>
          <li
            v-for="(item, index) in layout"
            :key="index"
            :data-active="item.value === activeMenu"
            class="relative cursor-pointer px-2 py-2.5 text-left text-sm text-primary-300 font-normal data-[active=true]:(text-destructive-100 font-600)"
            @click="(e) => handleSelectMenu(e.target as HTMLElement, item.value)"
          >
            {{ item.name }}
          </li>
        </ul>
        <component :is="getCurrentComp" class="ml-12 flex-1"></component>
      </div>
    </div>
  </div>
  <component :is="getCurrentComp" v-else></component>
</template>

<style lang="scss" scoped>
ul {
  @apply relative flex flex-col flex-[0_0_7.25rem] border-r-1px border-solid border-dialog-100;

  &::before {
    // content: '';
    // background: #FF4A00;
    // position: absolute;
    // transition: ease-in-out 200ms;
    // transition-property: left, top, width, height, opacity;
    // transition-behavior: allow-discrete;
    // pointer-events: none;
    // left: anchor(--item left);
    // top: calc(anchor(--item top) + 0.5rem);
    // width: 3px;
    // height: calc(anchor-size(--item height) - 1rem);
    // border-radius: 5px;
    content: "";
    position: absolute;
    background: var(--d1);
    transition: all 200ms linear;
    top: v-bind(anchor);
    left: 0;
    width: 3px;
    height: 20px;
    border-radius: 5px;
  }
}
</style>
