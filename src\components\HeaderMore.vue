<template>
  <div>
    <el-popover
      v-if="!isPc"
      ref="popoverRef"
      transition="el-zoom-in-top"
      popper-class="w-100px p-0! " trigger="click" :placement="placement" :persistent="false"
      :show-arrow="false"
      :popper-options="popperOptions"
    >
      <template #default>
        <UserProfile
          :show-submenu-arrow="false"
          menu-class="mobile-menu"
          :menus="menus"
        ></UserProfile>
      </template>
      <template #reference>
        <button :class="buttonClass">
          <i class="i-ju-header-more-m" />
        </button>
      </template>
    </el-popover>
    <ExportImageDialog v-model:visible="visible" :canvas="canvas" />
  </div>
</template>

<script setup lang="ts">
import service from '@/common/axios'
import messageDB from '@/common/message'
import { notify } from '@/common/tools'
import { useControlBar } from '@/hooks/useControlBar'
import { useDialog } from '@/hooks/useDialog'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useModalPosition } from '@/hooks/useModalPosition'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { toRefs } from '@vueuse/core'

defineProps({
  buttonClass: {
    type: String,
    default: '',
  },
  placement: {
    type: String,
    default: 'top',
  },
  popperOptions: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['refresh'])

const t = useGlobalI18n()
const { sendFavoriteMessage, sendMoveGroup } = useAppStore()
const popoverRef = ref()

const { userInfo } = storeToRefs(useUserStore())
const { isPc, exportOptions, chatViewData } = storeToRefs(useAppStore())
const { canvas, visible, download, toShare } = useControlBar()
const { messageId, groups, sections, groupId, chatContent }
  = toRefs(chatViewData)
const { modalStyle } = useModalPosition()
const { openDialog, closeDialog } = useDialog()

const exportMenuItem = computed(() => {
  return exportOptions.value.map((item) => {
    return {
      icon: '',
      name: item.label,
      action: () => {
        handleDownload({ type: item.type })
        unref(popoverRef).hide()
      },
    }
  })
})

const groupList = computed(() => {
  return groups.value.map((item) => {
    return {
      icon: groupId.value === item.id ? 'i-ri-check-line' : '',
      name: item.name,
      action: () => {
        sendMoveGroup(item.id)
        unref(popoverRef).hide()
      },
    }
  })
})

const handleDownload = ({
  type,
}: {
  type: 'PDF' | 'PNG' | 'TXT' | 'MARKDOWN'
}) => {
  download({
    messageId: messageId.value,
    inviteCode: userInfo.value.inviteCode,
    type,
    sections: sections.value,
  })
}

const clearDialog = () => {
  openDialog({
    titleContent: t('controlBar.clearDialogTitle'),
    style: modalStyle.value,
    content: () => t('controlBar.clearDialogContent').value,
    onSubmit: async () => {
      const data: any = await service.post('/gpt/clearChats', {
        id: messageId.value,
      })
      if (data.code === 200) {
        messageDB.deleteMessageById(chatViewData.value.messageId)
        notify.success({
          title: t('controlBar.clearDialogSuccess').value,
        })
        emits('refresh')
      }
      closeDialog(false)
    },
  })
}

const menus = computed(() => [
  {
    group: 'Options',
    groupTitle: '',
    children: [
      {
        icon: '',
        name: t('controlBar.clear'),
        action: () => {
          clearDialog()
          unref(popoverRef).hide()
        },
      },
      {
        icon: '',
        name: t('packageBox.starburst'),
        action: () => {
          sendFavoriteMessage(messageId.value)
          unref(popoverRef).hide()
        },
      },
      {
        icon: '',
        name: t('headerMore.export'),
        action: 'export',
        children: [
          {
            group: 'export',
            children: exportMenuItem.value,
          },
        ],
      },
      {
        icon: '',
        name: t('packageBox.group'),
        action: 'group',
        children: [
          {
            group: 'group',
            children: groupList.value,
          },
        ],
      },
      {
        icon: '',
        name: t('sideBar.share'),
        action: () => {
          toShare(messageId.value, chatViewData.value.messageName)
          unref(popoverRef).hide()
        },
      },
    ],
  },
])
</script>

<style lang="scss" scoped>
</style>
