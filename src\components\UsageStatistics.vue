<script lang="ts" setup>
import moment from "moment";
import type { CalendarDateType } from "element-plus";
import services from "@/common/axios";

const calendar = ref();
const month = ref(new Date());
const calendarData: any = ref([]);

const selectDate = (val: CalendarDateType) => {
  const y = Number(moment(month.value).format("YYYY"));
  const m = Number(moment(month.value).format("MM"));
  const new_y = Number(moment(new Date()).format("YYYY"));
  const new_m = Number(moment(new Date()).format("MM"));
  if (y >= new_y && m >= new_m && val === "next-month") {
    return;
  }
  // if (val === 'prev-month') {
  //   month.value.subtract(1, 'month')
  // }
  // else {
  //   month.value.add(1, 'month')
  // }
  // getMonth(month.value)
  calendar.value.selectDate(val);
  //   month.value = new Date()
};

const formatCount = (val: any) => {
  if (val >= 50) {
    return `text-[#FF5D00]`;
  } else if (val >= 30) {
    return `text-[#FFAE80]`;
  } else if (val >= 10) {
    return `text-[#FFEFE6]`;
  } else {
    return `text-[#F6F6F6]`;
  }
};
const getMonth = async (month: any) => {
  const formatMonth = moment(month).format("YYYY-MM");
  const daysInMonth = moment(formatMonth).daysInMonth();
  const array = [];
  for (let i = 1; i <= daysInMonth; i++) {
    const currentDate = moment(formatMonth).date(i);
    array.push({
      time: currentDate.format("YYYY-MM-DD"),
      count: 0,
    });
  }

  const data: any = await services.post(
    "/user/monthUsage",
    {
      month: formatMonth,
    },
    {
      headers: {
        noninductive: true,
      },
    }
  );
  calendarData.value = array.map((arrayElement: any) => ({
    time: arrayElement.time,
    count:
      data.find((dataElement: any) => dataElement.time === arrayElement.time)
        ?.count || 0,
  }));
};
// const formattedMonth = computed(() => {
//   return month.value.format('YYYY.MM')
// })

watch(month, (newValue) => {
  if (newValue) {
    getMonth(newValue);
  }
});

onMounted(() => {
  getMonth(new Date());
});
</script>

<template>
  <div class="h-50 w-95 rounded-4.5">
    <el-calendar
      ref="calendar"
      v-model="month"
      class="[&_.el-calendar\_\_body]:hidden ![&_.el-calendar\_\_header]:border-0"
    >
      <template #header>
        <div class="w-full flex items-center justify-between">
          <div class="text-2xl text-[#FF5D00] font-Damion">
            {{ $t("usageStatistics.title") }}
          </div>
          <div class="flex items-center text-sm font-RobotoSlab">
            <i
              class="i-ri-arrow-drop-left-line cursor-pointer text-2xl text-[#CBD5E1] hover:text-[#000000]"
              @click="selectDate('prev-month')"
            ></i>
            <span>{{ moment(month).format("YYYY.MM") }}</span>
            <i
              class="i-ri-arrow-drop-right-line cursor-pointer text-2xl text-[#CBD5E1] hover:text-[#000000]"
              @click="selectDate('next-month')"
            ></i>
          </div>
        </div>
      </template>
    </el-calendar>
    <div class="flex flex-wrap justify-start gap-1 px-5 py-3">
      <i
        v-for="({ count }, index) in calendarData"
        :key="index"
        :class="`i-ri-chat-1-fill w-[20px] h-[20px] text-[26px] leading-[20px] rotate-y-180 ${formatCount(
          count
        )}`"
      />
    </div>
  </div>
</template>
