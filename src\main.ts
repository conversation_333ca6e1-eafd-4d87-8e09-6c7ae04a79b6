import { ElPopover } from 'element-plus'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import vue3GoogleLogin from 'vue3-google-login'
import App from '@/App.vue'
import debounce from '@/common/debounce'
import i18n from '@/i18n/index'
import router from '@/router'
import { initSentry } from './config/sentry'
import directive from './directives'
import '@unocss/reset/tailwind.css'
import 'animate.css'
// import 'remixicon/fonts/remixicon.css'
import 'element-plus/es/components/notification/style/css'
import 'element-plus/es/components/loading/style/css'
import 'virtual:uno.css'
import '@/assets/main.scss'
import '@/assets/global.css'
import '@/assets/vars.scss'
// import 'amfe-flexible'
import 'large-small-dynamic-viewport-units-polyfill'
import 'element-plus/theme-chalk/dark/css-vars.css'
import '@/common/monaco-setup' // 导入Monaco配置
import '@/common/codeBlockHandler' // 导入代码块自动换行逻辑处理
import '@/common/tableWidthHandler' // 导入表格宽度处理逻辑
import '@/common/globalFunctions'

ElPopover.props.transition = {
  type: String,
  default: 'el-zoom-in-top',
}
const app = createApp(App)
const pinia = createPinia()

directive(app)

app.directive('debounce', debounce)

initSentry(app)
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(vue3GoogleLogin, {
  clientId: import.meta.env.VITE_APP_GOOGLE_CLIENT,
})
app.use(ElPopover)

app.mount('#app')
export default app
