<template>
  <div class="hidden">
    <div class="text-center">
      <div class="not-found__title">404</div>
      <div class="not-found__subtitle">{{ $t("notFound.error") }}</div>
      <div class="not-found__logo">JUCHATS</div>
      <div class="px-40px py-0 pt-50px text-justify text-12px">
        {{ $t("notFound.tip") }}
        <span class="text-destructive-300">{{ $t("notFound.eggCode") }}</span>
        {{ $t("notFound.copyIt") }}
      </div>
      <template v-if="enums[selected]?.code">
        <div class="not-found__group flex justify-between">
          <div v-for="(item, index) in enums[selected]?.code" :key="index">
            {{ item }}
          </div>
        </div>
        <div class="not-found__counter text-right">
          {{ enums.length }} / {{ array.length }}
        </div>
      </template>
      <div v-else class="text-destructive-300 pt-10 text-xs">
        {{ $t("notFound.noInviteCode") }}
      </div>
      <svg class="not-found__barcode"></svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import JsBarcode from "jsbarcode";
import services from "@/common/axios";

const array: any = ref([]);
const selected: any = ref(0);
const enums: any = ref([]);
onMounted(async () => {
  //   array.value = await services.get(
  //     `${import.meta.env.VITE_APP_PAY_URL}/order/exchangeList
  // `,
  //     {}
  //   )
  array.value = [];

  enums.value = array.value.filter((element: any) => !element.status);
  if (!enums.value.length) {
    return;
  }
  selected.value = Math.floor(Math.random() * array.value.length);
  JsBarcode(".not-found__barcode", String(enums.value[selected.value].code), {
    format: "CODE39",
    width: 1.8,
    height: 8.5,
    displayValue: false,
    textMargin: 0,
    fontSize: 15,
    margin: 0, // 设置条形码周围的空白边距
  });
});
</script>

<style lang="scss" scoped>
.not-found__title {
  @apply text-200px text-destructive-300;

  font-family: EXEPixelPerfect;
}

.not-found__subtitle {
  @apply text-36px -mt-90px;

  font-family: "01 Digit";
}

.not-found__logo {
  font-family: Alegreya;
  font-weight: 600;
}

.not-found__group {
  padding: 35px;

  & > div {
    @apply w-40px h-60px lh-55px border-1px border-solid border-destructive-300 text-50px text-destructive-300 text-center pl-5px;

    font-family: EXEPixelPerfect;
  }
}

.not-found__counter {
  @apply p-35px text-12px -mt-60px;

  font-family: "01 Digit";
}

.not-found__barcode {
  @apply fixed bottom-20px inset-x-0 m-auto;
}
</style>
