<template>
  <div ref="editorContainer" class="juchats-monaco-main h-full min-h-300px"></div>
</template>

<script setup lang="ts">
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'
import 'monaco-editor/min/vs/editor/editor.main.css'

interface EditorOptions {
  readOnly: boolean
  language: string
  minimap?: { enabled: boolean }
}

const props = defineProps<{
  options: EditorOptions
  streaming: boolean
}>()

const modelValue = defineModel<string>()

const editorContainer = ref<HTMLElement | null>(null)
let editor: monaco.editor.IStandaloneCodeEditor | null = null

watch(() => props.options, (newOptions, oldOptions) => {
  if (!editor) {
    return
  }
  setTimeout(() => {
    if (editor) {
      editor.updateOptions(props.options)

      // 如果语言发生变化，需要专门更新语言设置
      if (newOptions.language !== oldOptions?.language) {
        const model = editor.getModel()
        if (model) {
          monaco.editor.setModelLanguage(model, newOptions.language)
        }
      }
    }
  }, 0)
}, { deep: true })

// 初始化编辑器
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

// 销毁编辑器
onBeforeUnmount(() => {
  disposeEditor()
})
const { isDark } = useThemeChange()
const monacoThemeName = computed(() => isDark.value ? 'vs-dark' : 'vs')
// 初始化Monaco编辑器
function initEditor() {
  if (!editorContainer.value) { return }

  // 如果已存在编辑器实例，先销毁
  disposeEditor()

  // 创建新的编辑器实例
  editor = monaco.editor.create(editorContainer.value, {
    value: modelValue.value,
    language: props.options.language || 'plaintext',
    readOnly: props.options.readOnly,
    automaticLayout: true,
    fontSize: 13,
    renderLineHighlight: 'all',
    wordWrap: 'on',
    theme: monacoThemeName.value,
    minimap: props.options.minimap,
  })

  // 监听编辑器内容变化，更新modelValue
  editor.onDidChangeModelContent(() => {
    const value = editor?.getValue() || ''
    if (value !== modelValue.value) {
      modelValue.value = value
    }
  })

  // 添加Ctrl+F快捷键监听，触发编辑器内置搜索
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
    editor?.getAction('actions.find')?.run()
  })
}

// 销毁编辑器实例
function disposeEditor() {
  if (!editor) {
    return
  }
  editor.dispose()
  editor = null
}

defineExpose({
  refresh: () => {
    nextTick(() => {
      initEditor()
    })
  },
  getEditor: () => editor,
})

// 监听modelValue变化，更新编辑器
watch(
  modelValue,
  (newValue) => {
    if (!editor) {
      return
    }

    // 获取当前光标位置
    const position = editor.getPosition()

    // 只有当编辑器的值与新值不同时才更新
    const currentValue = editor.getValue()
    if (currentValue === newValue) {
      return
    }
    editor.setValue(newValue || '')

    // 只有在不处于流模式的情况下才恢复光标位置
    if (position && !props.streaming) {
      editor.setPosition(position)
      editor.revealPositionInCenter(position)
    }
  },
)

// 监听暗色模式变化，更新编辑器主题
watch(isDark, () => {
  if (editor) {
    nextTick(() => {
      editor?.updateOptions({ theme: monacoThemeName.value })
    })
  }
})

// 监听streaming状态变化，更新编辑器语言
watch(() => props.streaming, (newVal, oldVal) => {
  if (oldVal === true && newVal === false && editor) {
    // 流式输出结束时，使用options中设置的语言
    const model = editor.getModel()
    if (model && props.options.language) {
      monaco.editor.setModelLanguage(model, props.options.language)
    }
  }
})
</script>

<style lang="scss" scoped>
</style>
