// @unocss-include
import type { DialogProps } from 'element-plus'
import type { Component } from 'vue'
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'

export interface useDialogPropsOptions extends Partial<DialogProps> {
  onSubmit?: (el: Component) => void
  onCancel?: () => void
  showCancel?: boolean
  showSubmit?: boolean
  content: Component | string
  titleContent?: Component | string
  titleClass?: string
  titleProps?: any
  contentProps?: any
  showFooter?: boolean
  style?: Record<string, string>
}

export interface UseDialog {
  isVisible: globalThis.Ref<boolean>
  dialogOptions: useDialogPropsOptions
  openDialog: (options: useDialogPropsOptions) => void
  closeDialog: (isCancel?: boolean) => void
}
const isVisible = ref(false)
let dialogOptions = {
  showCancel: true,
  showSubmit: true,
  appendToBody: true,
  showFooter: true,
  onCancel: () => {},
} as useDialogPropsOptions

const getDefaultModalClass = (isPc: boolean) => {
  return isPc
    ? `bg-[rgba(var(--white-rgb),0.5)]! backdrop-blur-2.5rem! [&_.el-dialog]:(top-[50%]! -translate-y-[50%]!)`
    : '[&_.el-dialog]:(top-[50%]! left-[50%] -translate-[50%]! m-0)'
}

const defaultDialogConfig = (isPc: boolean) => ({
  width: isPc ? '450px' : '300px',
  titleClass: 'text-4.5 font-600',
  modalClass: getDefaultModalClass(isPc),
  showClose: false,
})

export function useDialog(el?: globalThis.Ref): UseDialog {
  const openDialog = (options: useDialogPropsOptions) => {
    const { isPc } = storeToRefs(useAppStore())
    dialogOptions = {
      ...dialogOptions,
      ...defaultDialogConfig(isPc.value),
      ...options,
    }

    dialogOptions.onSubmit = dialogOptions.onSubmit?.bind(el)
    dialogOptions.onCancel = dialogOptions.onCancel?.bind(el)
    isVisible.value = true
  }

  const closeDialog = (isCancel = false) => {
    try {
      isVisible.value = false
      if (isCancel && dialogOptions.onCancel) {
        dialogOptions.onCancel()
      }
      dialogOptions = {
        showCancel: true,
        showSubmit: true,
        appendToBody: true,
        showFooter: true,
        onCancel: () => {},
      } as useDialogPropsOptions
    }
    catch { }
  }

  // const showConfirm = async (message, title = "提示") => {
  //   try {
  //     await ElMessageBox.confirm(message, title, {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //     });
  //     return true;
  //   } catch {
  //     return false;
  //   }
  // };

  return {
    isVisible,
    dialogOptions,
    openDialog,
    closeDialog,
  }
}
